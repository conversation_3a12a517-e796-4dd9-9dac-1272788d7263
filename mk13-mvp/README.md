# MK13 MVP - AI Assistant with Context Awareness

A sophisticated AI assistant application built with FastAPI backend and Electron frontend, featuring real-time context detection, multi-LLM support, and seamless Google Workspace integration.

## 🚀 Features

### Core Capabilities
- **Context-Aware AI**: Automatically detects your current work context and provides relevant assistance
- **Multi-LLM Support**: Intelligent routing between OpenAI GPT, Anthropic Claude, and Google Gemini
- **Real-time Communication**: WebSocket-based real-time interaction with the AI
- **Voice Input**: Speech-to-text functionality for hands-free interaction
- **Google Workspace Integration**: Seamless integration with Gmail, Calendar, and Drive

### Advanced Features
- **Proactive Assistance**: AI anticipates your needs based on context and patterns
- **Context Hierarchy**: Intelligent context switching and management
- **Autonomy System**: Configurable levels of AI autonomy for different tasks
- **Security-First**: OAuth2 authentication, AES-256 encryption, and comprehensive security measures

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Electron UI   │◄──►│  FastAPI Backend │◄──►│  External APIs  │
│                 │    │                 │    │                 │
│ • Ghost UI      │    │ • LLM Router    │    │ • OpenAI        │
│ • Context Mgmt  │    │ • Context Det.  │    │ • Anthropic     │
│ • Voice Input   │    │ • Google APIs   │    │ • Google        │
│ • Real-time     │    │ • Security      │    │ • Supabase      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📋 Prerequisites

### System Requirements
- **Node.js**: 18.x or higher
- **Python**: 3.9 or higher
- **Redis**: 6.x or higher (for Celery)
- **PostgreSQL**: 13.x or higher (via Supabase)

### API Keys Required
- OpenAI API Key
- Anthropic API Key
- Google Cloud API Key
- Google OAuth2 Credentials
- Supabase Project URL and API Key

## 🛠️ Installation

### 1. Clone the Repository
```bash
git clone <repository-url>
cd mk13-mvp
```

### 2. Backend Setup
```bash
cd backend

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Set up environment variables
cp .env.example .env
# Edit .env with your API keys and configuration
```

### 3. Frontend Setup
```bash
cd frontend

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env
# Edit .env with your configuration
```

### 4. Database Setup
```bash
# The application uses Supabase for database management
# Ensure your Supabase project is configured and the URL/keys are in your .env file
```

## 🔧 Configuration

### Environment Variables

#### Backend (.env)
```env
# API Keys
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key
GOOGLE_API_KEY=your_google_key

# Google OAuth2
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GOOGLE_REDIRECT_URI=http://localhost:8000/auth/google/callback

# Database
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_key

# Security
JWT_SECRET_KEY=your_jwt_secret
ENCRYPTION_KEY=your_encryption_key

# Redis (for Celery)
REDIS_URL=redis://localhost:6379/0

# CORS
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080
```

#### Frontend (.env)
```env
REACT_APP_API_URL=http://localhost:8000
REACT_APP_WS_URL=ws://localhost:8000
REACT_APP_GOOGLE_CLIENT_ID=your_google_client_id
```

## 🚀 Running the Application

### Development Mode

#### 1. Start Redis (for background tasks)
```bash
redis-server
```

#### 2. Start the Backend
```bash
cd backend
source venv/bin/activate
python main.py
```

#### 3. Start Celery Worker (in another terminal)
```bash
cd backend
source venv/bin/activate
celery -A celery_app worker --loglevel=info
```

#### 4. Start the Frontend
```bash
cd frontend
npm run electron:dev
```

### Production Mode

#### Backend
```bash
cd backend
source venv/bin/activate
uvicorn main:app --host 0.0.0.0 --port 8000 --workers 4
```

#### Frontend
```bash
cd frontend
npm run build
npm run electron:pack
```

## 🧪 Testing

### Backend Tests
```bash
cd backend
python run_tests.py

# Run specific test types
python run_tests.py --tests-only
python run_tests.py --security-only
python run_tests.py --quick
```

### Frontend Tests
```bash
cd frontend
node test-runner.js

# Run specific test types
node test-runner.js --tests-only
node test-runner.js --quick
node test-runner.js --fix
```

## 📚 API Documentation

### Authentication Endpoints
- `GET /api/auth/google` - Get Google OAuth URL
- `GET /auth/google/callback` - Handle OAuth callback
- `POST /api/auth/logout` - Logout user
- `GET /api/auth/me` - Get current user info

### AI Interaction Endpoints
- `POST /api/ai/chat` - Send message to AI
- `POST /api/ai/voice` - Process voice input
- `GET /api/ai/models` - Get available AI models

### Context Management Endpoints
- `GET /api/contexts` - Get user contexts
- `POST /api/contexts` - Create new context
- `PUT /api/contexts/{id}` - Update context
- `DELETE /api/contexts/{id}` - Delete context

### Google Workspace Endpoints
- `GET /api/google/emails` - Get recent emails
- `GET /api/google/calendar` - Get calendar events
- `POST /api/google/email/send` - Send email

## 🔒 Security Features

### Authentication & Authorization
- OAuth2 with Google
- JWT tokens with secure expiration
- Session management with automatic cleanup
- API key authentication for service-to-service calls

### Data Protection
- AES-256 encryption for sensitive data
- TLS 1.3 for all communications
- Input validation and sanitization
- Rate limiting and brute force protection

### Security Headers
- Content Security Policy (CSP)
- X-Frame-Options: DENY
- X-Content-Type-Options: nosniff
- Strict-Transport-Security

## 🎯 Usage Examples

### Basic Chat Interaction
```javascript
// Frontend - Send message to AI
const response = await fetch('/api/ai/chat', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-Session-Token': sessionToken
  },
  body: JSON.stringify({
    message: 'Help me write an email to my team',
    context_id: currentContextId
  })
});
```

### Context Detection
```javascript
// Frontend - Get current context
import { useContextDetection } from './hooks/useContextDetection';

const { lastDetectedContext, detectNow } = useContextDetection({
  autoStart: true,
  detectionInterval: 30000
});

// Manually trigger detection
const context = await detectNow();
console.log('Current project:', context.work.currentProject);
```

### Voice Input
```javascript
// Frontend - Use voice input
import VoiceInput from './components/VoiceInput';

<VoiceInput
  onTranscript={(text) => console.log('Transcribed:', text)}
  onResult={(result) => handleVoiceCommand(result)}
  language="en-US"
/>
```

## 🔧 Troubleshooting

### Common Issues

#### Backend won't start
- Check that all environment variables are set
- Ensure Redis is running
- Verify Python virtual environment is activated
- Check that ports 8000 and 6379 are available

#### Frontend build fails
- Clear node_modules and reinstall: `rm -rf node_modules && npm install`
- Check Node.js version compatibility
- Ensure all environment variables are set

#### Authentication not working
- Verify Google OAuth2 credentials
- Check redirect URI configuration
- Ensure CORS settings allow your frontend domain

#### Context detection not working
- Check browser permissions for system access
- Verify WebSocket connection is established
- Check console for JavaScript errors

### Performance Optimization

#### Backend
- Use Redis for caching frequently accessed data
- Implement database connection pooling
- Use async/await for I/O operations
- Monitor memory usage with background tasks

#### Frontend
- Implement virtual scrolling for large lists
- Use React.memo for expensive components
- Optimize bundle size with code splitting
- Cache API responses where appropriate

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

### Development Guidelines
- Follow the existing code style
- Write tests for new features
- Update documentation as needed
- Ensure all tests pass before submitting

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- OpenAI for GPT models
- Anthropic for Claude models
- Google for Gemini and Workspace APIs
- Supabase for database and authentication
- The open-source community for the amazing tools and libraries

## 📞 Support

For support, please open an issue on GitHub or contact the development team.

---

**Built with ❤️ for enhanced productivity and AI-human collaboration**
