# Multi-stage Dockerfile for MK13 Frontend
# Optimized React build with <PERSON>inx serving

# Build stage
FROM node:18-alpine as builder

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Production stage
FROM nginx:alpine as production

# Install security updates
RUN apk update && apk upgrade && apk add --no-cache curl

# Remove default nginx config
RUN rm /etc/nginx/conf.d/default.conf

# Copy custom nginx config
COPY nginx.conf /etc/nginx/conf.d/

# Copy built application
COPY --from=builder /app/build /usr/share/nginx/html

# Copy environment script
COPY env.sh /docker-entrypoint.d/env.sh
RUN chmod +x /docker-entrypoint.d/env.sh

# Create non-root user
RUN addgroup -g 1001 -S mk13 && \
    adduser -S mk13 -u 1001

# Set ownership
RUN chown -R mk13:mk13 /usr/share/nginx/html && \
    chown -R mk13:mk13 /var/cache/nginx && \
    chown -R mk13:mk13 /var/log/nginx && \
    chown -R mk13:mk13 /etc/nginx/conf.d

# Switch to non-root user
USER mk13

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:80/health || exit 1

# Expose port
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
