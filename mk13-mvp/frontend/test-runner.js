#!/usr/bin/env node
/**
 * Comprehensive Test Runner for MK13 MVP Frontend
 * Runs all tests with coverage reporting and code quality checks
 */

const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');
const { promisify } = require('util');

const execAsync = promisify(exec);

class TestRunner {
  constructor() {
    this.results = {};
    this.verbose = false;
    this.coverage = true;
    this.fix = false;
  }

  async runCommand(command, args = [], options = {}) {
    return new Promise((resolve, reject) => {
      const child = spawn(command, args, {
        stdio: this.verbose ? 'inherit' : 'pipe',
        shell: true,
        ...options
      });

      let stdout = '';
      let stderr = '';

      if (!this.verbose) {
        child.stdout?.on('data', (data) => {
          stdout += data.toString();
        });

        child.stderr?.on('data', (data) => {
          stderr += data.toString();
        });
      }

      child.on('close', (code) => {
        resolve({
          code,
          stdout,
          stderr
        });
      });

      child.on('error', (error) => {
        reject(error);
      });
    });
  }

  async checkDependencies() {
    console.log('\n🔍 Checking dependencies...');
    
    const requiredPackages = [
      'jest',
      '@testing-library/react',
      '@testing-library/jest-dom',
      '@testing-library/user-event',
      'eslint',
      'prettier',
      'typescript'
    ];

    try {
      const { stdout } = await execAsync('npm list --depth=0 --json');
      const packageInfo = JSON.parse(stdout);
      const installedPackages = Object.keys(packageInfo.dependencies || {});

      const missingPackages = requiredPackages.filter(
        pkg => !installedPackages.includes(pkg)
      );

      if (missingPackages.length > 0) {
        console.log(`❌ Missing required packages: ${missingPackages.join(', ')}`);
        console.log('Install them with: npm install ' + missingPackages.join(' '));
        return false;
      }

      console.log('✅ All required testing dependencies are installed');
      return true;
    } catch (error) {
      console.log('❌ Error checking dependencies:', error.message);
      return false;
    }
  }

  async runPrettier() {
    console.log('\n🔧 Running Prettier formatting checks...');

    const prettierArgs = [
      '--check',
      'src/**/*.{ts,tsx,js,jsx,json,css,md}'
    ];

    if (this.fix) {
      prettierArgs[0] = '--write';
    }

    try {
      const result = await this.runCommand('npx', ['prettier', ...prettierArgs]);

      if (result.code === 0) {
        console.log('✅ Code formatting is correct');
        return true;
      } else {
        console.log('❌ Code formatting issues found');
        if (this.fix) {
          console.log('🔧 Fixed formatting issues');
          return true;
        } else {
          console.log('Run with --fix to automatically fix formatting');
          if (!this.verbose) {
            console.log(result.stdout);
            console.log(result.stderr);
          }
          return false;
        }
      }
    } catch (error) {
      console.log('❌ Error running Prettier:', error.message);
      return false;
    }
  }

  async runESLint() {
    console.log('\n🔍 Running ESLint...');

    const eslintArgs = [
      'src/',
      '--ext', '.ts,.tsx,.js,.jsx',
      '--format', 'stylish'
    ];

    if (this.fix) {
      eslintArgs.push('--fix');
    }

    try {
      const result = await this.runCommand('npx', ['eslint', ...eslintArgs]);

      if (result.code === 0) {
        console.log('✅ No linting issues found');
        return true;
      } else {
        console.log('❌ Linting issues found:');
        if (!this.verbose) {
          console.log(result.stdout);
          console.log(result.stderr);
        }
        return false;
      }
    } catch (error) {
      console.log('❌ Error running ESLint:', error.message);
      return false;
    }
  }

  async runTypeScript() {
    console.log('\n🔍 Running TypeScript type checking...');

    try {
      const result = await this.runCommand('npx', ['tsc', '--noEmit']);

      if (result.code === 0) {
        console.log('✅ No type checking issues found');
        return true;
      } else {
        console.log('❌ Type checking issues found:');
        if (!this.verbose) {
          console.log(result.stdout);
          console.log(result.stderr);
        }
        return false;
      }
    } catch (error) {
      console.log('❌ Error running TypeScript:', error.message);
      return false;
    }
  }

  async runJest() {
    console.log('\n🧪 Running Jest tests...');

    const jestArgs = [
      '--passWithNoTests',
      '--watchAll=false'
    ];

    if (this.coverage) {
      jestArgs.push(
        '--coverage',
        '--coverageReporters=text',
        '--coverageReporters=lcov',
        '--coverageReporters=html'
      );
    }

    if (this.verbose) {
      jestArgs.push('--verbose');
    }

    try {
      const result = await this.runCommand('npx', ['jest', ...jestArgs]);

      if (result.code === 0) {
        console.log('✅ All tests passed');
        if (this.coverage) {
          console.log('📊 Coverage report generated in coverage/');
        }
        return true;
      } else {
        console.log('❌ Some tests failed:');
        if (!this.verbose) {
          console.log(result.stdout);
          console.log(result.stderr);
        }
        return false;
      }
    } catch (error) {
      console.log('❌ Error running Jest:', error.message);
      return false;
    }
  }

  async runE2ETests() {
    console.log('\n🔗 Running E2E tests...');

    // Check if Playwright is available
    try {
      await execAsync('npx playwright --version');
    } catch (error) {
      console.log('⚠️  Playwright not found, skipping E2E tests');
      return true;
    }

    try {
      const result = await this.runCommand('npx', ['playwright', 'test']);

      if (result.code === 0) {
        console.log('✅ All E2E tests passed');
        return true;
      } else {
        console.log('❌ Some E2E tests failed:');
        if (!this.verbose) {
          console.log(result.stdout);
          console.log(result.stderr);
        }
        return false;
      }
    } catch (error) {
      console.log('❌ Error running E2E tests:', error.message);
      return false;
    }
  }

  async runBundleAnalysis() {
    console.log('\n📦 Running bundle analysis...');

    try {
      // Build the project first
      const buildResult = await this.runCommand('npm', ['run', 'build']);

      if (buildResult.code !== 0) {
        console.log('❌ Build failed, cannot analyze bundle');
        return false;
      }

      // Check bundle size
      const distPath = path.join(process.cwd(), 'dist');
      if (fs.existsSync(distPath)) {
        const stats = await this.getBundleStats(distPath);
        console.log('📊 Bundle analysis:');
        console.log(`  Total size: ${this.formatBytes(stats.totalSize)}`);
        console.log(`  JS size: ${this.formatBytes(stats.jsSize)}`);
        console.log(`  CSS size: ${this.formatBytes(stats.cssSize)}`);

        // Check if bundle size is reasonable (< 5MB)
        if (stats.totalSize > 5 * 1024 * 1024) {
          console.log('⚠️  Bundle size is quite large (>5MB)');
          return false;
        }

        console.log('✅ Bundle size is reasonable');
        return true;
      } else {
        console.log('❌ Build output not found');
        return false;
      }
    } catch (error) {
      console.log('❌ Error running bundle analysis:', error.message);
      return false;
    }
  }

  async getBundleStats(distPath) {
    const stats = { totalSize: 0, jsSize: 0, cssSize: 0 };

    const files = fs.readdirSync(distPath, { recursive: true });
    
    for (const file of files) {
      const filePath = path.join(distPath, file);
      if (fs.statSync(filePath).isFile()) {
        const size = fs.statSync(filePath).size;
        stats.totalSize += size;

        if (file.endsWith('.js')) {
          stats.jsSize += size;
        } else if (file.endsWith('.css')) {
          stats.cssSize += size;
        }
      }
    }

    return stats;
  }

  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  generateReport() {
    console.log('\n📋 Test Report Summary');
    console.log('='.repeat(50));

    const totalChecks = Object.keys(this.results).length;
    const passedChecks = Object.values(this.results).filter(Boolean).length;

    for (const [checkName, passed] of Object.entries(this.results)) {
      const status = passed ? '✅ PASS' : '❌ FAIL';
      console.log(`${checkName.padEnd(30)} ${status}`);
    }

    console.log('='.repeat(50));
    console.log(`Total: ${passedChecks}/${totalChecks} checks passed`);

    if (passedChecks === totalChecks) {
      console.log('🎉 All checks passed! Your code is ready for deployment.');
    } else {
      console.log('⚠️  Some checks failed. Please fix the issues before deployment.');
    }

    // Generate JSON report
    const report = {
      timestamp: new Date().toISOString(),
      totalChecks,
      passedChecks,
      successRate: (passedChecks / totalChecks) * 100,
      results: this.results
    };

    fs.writeFileSync('test-report.json', JSON.stringify(report, null, 2));
    console.log('📄 Detailed report saved to test-report.json');

    return passedChecks === totalChecks;
  }

  async run(options = {}) {
    this.verbose = options.verbose || false;
    this.coverage = !options.noCoverage;
    this.fix = options.fix || false;

    console.log('🚀 MK13 MVP Frontend Test Runner');
    console.log('='.repeat(50));

    // Check dependencies first
    if (!(await this.checkDependencies())) {
      process.exit(1);
    }

    if (options.securityOnly) {
      // Only run security-related checks
      this.results['ESLint Security'] = await this.runESLint();
    } else if (options.testsOnly) {
      // Only run tests
      this.results['Jest Tests'] = await this.runJest();
      if (!options.quick) {
        this.results['E2E Tests'] = await this.runE2ETests();
      }
    } else {
      // Run all checks
      this.results['Prettier'] = await this.runPrettier();
      
      if (!options.quick) {
        this.results['ESLint'] = await this.runESLint();
        this.results['TypeScript'] = await this.runTypeScript();
      }

      this.results['Jest Tests'] = await this.runJest();

      if (!options.quick) {
        this.results['E2E Tests'] = await this.runE2ETests();
        this.results['Bundle Analysis'] = await this.runBundleAnalysis();
      }
    }

    const success = this.generateReport();
    process.exit(success ? 0 : 1);
  }
}

// Parse command line arguments
const args = process.argv.slice(2);
const options = {
  fix: args.includes('--fix'),
  noCoverage: args.includes('--no-coverage'),
  verbose: args.includes('--verbose'),
  quick: args.includes('--quick'),
  securityOnly: args.includes('--security-only'),
  testsOnly: args.includes('--tests-only')
};

// Run the test runner
const runner = new TestRunner();
runner.run(options).catch(error => {
  console.error('❌ Test runner failed:', error);
  process.exit(1);
});
