/**
 * Client-side Context Detection Service
 * Detects current work context and system information
 */

export interface SystemContext {
  platform: string;
  userAgent: string;
  language: string;
  timezone: string;
  screenResolution: {
    width: number;
    height: number;
  };
  windowSize: {
    width: number;
    height: number;
  };
  timestamp: string;
}

export interface BrowserContext {
  url?: string;
  title?: string;
  domain?: string;
  isActive: boolean;
  tabCount?: number;
}

export interface ApplicationContext {
  activeApplication?: string;
  windowTitle?: string;
  isElectronApp: boolean;
  electronVersion?: string;
}

export interface WorkContext {
  currentProject?: string;
  workingDirectory?: string;
  activeFiles?: string[];
  lastActivity: string;
  sessionDuration: number;
}

export interface DetectedContext {
  system: SystemContext;
  browser: BrowserContext;
  application: ApplicationContext;
  work: WorkContext;
  confidence: number;
  detectionMethod: string;
}

class ContextDetectionService {
  private sessionStartTime: number;
  private lastActivityTime: number;
  private detectionInterval: NodeJS.Timeout | null = null;
  private callbacks: ((context: DetectedContext) => void)[] = [];

  constructor() {
    this.sessionStartTime = Date.now();
    this.lastActivityTime = Date.now();
    this.setupActivityTracking();
  }

  /**
   * Start automatic context detection
   */
  startDetection(intervalMs: number = 30000): void {
    if (this.detectionInterval) {
      clearInterval(this.detectionInterval);
    }

    this.detectionInterval = setInterval(() => {
      this.detectContext().then(context => {
        this.notifyCallbacks(context);
      }).catch(error => {
        console.error('Context detection error:', error);
      });
    }, intervalMs);

    // Initial detection
    this.detectContext().then(context => {
      this.notifyCallbacks(context);
    });
  }

  /**
   * Stop automatic context detection
   */
  stopDetection(): void {
    if (this.detectionInterval) {
      clearInterval(this.detectionInterval);
      this.detectionInterval = null;
    }
  }

  /**
   * Add callback for context changes
   */
  onContextChange(callback: (context: DetectedContext) => void): void {
    this.callbacks.push(callback);
  }

  /**
   * Remove callback
   */
  removeCallback(callback: (context: DetectedContext) => void): void {
    const index = this.callbacks.indexOf(callback);
    if (index > -1) {
      this.callbacks.splice(index, 1);
    }
  }

  /**
   * Detect current context
   */
  async detectContext(): Promise<DetectedContext> {
    const system = this.detectSystemContext();
    const browser = this.detectBrowserContext();
    const application = this.detectApplicationContext();
    const work = this.detectWorkContext();

    const confidence = this.calculateConfidence(system, browser, application, work);

    return {
      system,
      browser,
      application,
      work,
      confidence,
      detectionMethod: 'client-side',
    };
  }

  /**
   * Detect system-level context
   */
  private detectSystemContext(): SystemContext {
    return {
      platform: navigator.platform,
      userAgent: navigator.userAgent,
      language: navigator.language,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      screenResolution: {
        width: screen.width,
        height: screen.height,
      },
      windowSize: {
        width: window.innerWidth,
        height: window.innerHeight,
      },
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Detect browser context
   */
  private detectBrowserContext(): BrowserContext {
    return {
      url: window.location.href,
      title: document.title,
      domain: window.location.hostname,
      isActive: document.hasFocus(),
      // Note: Can't get tab count in Electron renderer process
    };
  }

  /**
   * Detect application context
   */
  private detectApplicationContext(): ApplicationContext {
    const isElectron = !!(window as any).process?.versions?.electron;
    
    return {
      activeApplication: isElectron ? 'MK13-MVP' : 'Browser',
      windowTitle: document.title,
      isElectronApp: isElectron,
      electronVersion: isElectron ? (window as any).process?.versions?.electron : undefined,
    };
  }

  /**
   * Detect work context
   */
  private detectWorkContext(): WorkContext {
    const sessionDuration = Date.now() - this.sessionStartTime;
    
    return {
      currentProject: this.extractProjectFromTitle(document.title),
      lastActivity: new Date(this.lastActivityTime).toISOString(),
      sessionDuration,
    };
  }

  /**
   * Extract project information from window title
   */
  private extractProjectFromTitle(title: string): string | undefined {
    // Simple heuristics to extract project name
    if (title.includes('MK13')) return 'MK13';
    if (title.includes('GitHub')) return 'GitHub';
    if (title.includes('VS Code')) return 'Development';
    if (title.includes('Slack')) return 'Communication';
    if (title.includes('Gmail')) return 'Email';
    if (title.includes('Calendar')) return 'Scheduling';
    
    return undefined;
  }

  /**
   * Calculate confidence score for context detection
   */
  private calculateConfidence(
    system: SystemContext,
    browser: BrowserContext,
    application: ApplicationContext,
    work: WorkContext
  ): number {
    let confidence = 0.5; // Base confidence

    // Increase confidence based on available data
    if (browser.isActive) confidence += 0.1;
    if (work.currentProject) confidence += 0.2;
    if (application.isElectronApp) confidence += 0.1;
    if (browser.domain && browser.domain !== 'localhost') confidence += 0.1;

    return Math.min(confidence, 1.0);
  }

  /**
   * Setup activity tracking
   */
  private setupActivityTracking(): void {
    const updateActivity = () => {
      this.lastActivityTime = Date.now();
    };

    // Track various user activities
    document.addEventListener('click', updateActivity);
    document.addEventListener('keydown', updateActivity);
    document.addEventListener('scroll', updateActivity);
    document.addEventListener('mousemove', updateActivity);
    window.addEventListener('focus', updateActivity);
    window.addEventListener('blur', updateActivity);
  }

  /**
   * Notify all callbacks of context change
   */
  private notifyCallbacks(context: DetectedContext): void {
    this.callbacks.forEach(callback => {
      try {
        callback(context);
      } catch (error) {
        console.error('Context callback error:', error);
      }
    });
  }

  /**
   * Get enhanced context with additional system information
   */
  async getEnhancedContext(): Promise<DetectedContext & { 
    performance?: any;
    memory?: any;
    network?: any;
  }> {
    const baseContext = await this.detectContext();
    
    // Add performance information if available
    const enhanced: any = { ...baseContext };
    
    if ('performance' in window && window.performance.memory) {
      enhanced.performance = {
        memory: {
          usedJSHeapSize: (window.performance as any).memory.usedJSHeapSize,
          totalJSHeapSize: (window.performance as any).memory.totalJSHeapSize,
          jsHeapSizeLimit: (window.performance as any).memory.jsHeapSizeLimit,
        },
        timing: window.performance.timing,
      };
    }

    // Add network information if available
    if ('connection' in navigator) {
      enhanced.network = {
        effectiveType: (navigator as any).connection.effectiveType,
        downlink: (navigator as any).connection.downlink,
        rtt: (navigator as any).connection.rtt,
      };
    }

    return enhanced;
  }
}

// Export singleton instance
export const contextDetectionService = new ContextDetectionService();
export default contextDetectionService;
