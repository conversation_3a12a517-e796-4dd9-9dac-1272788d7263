/**
 * Electron Mock for Testing
 * Provides mock implementations of Electron APIs
 */

export const app = {
  getName: jest.fn(() => 'MK13-MVP'),
  getVersion: jest.fn(() => '1.0.0'),
  getPath: jest.fn((name: string) => `/mock/path/${name}`),
  quit: jest.fn(),
  exit: jest.fn(),
  focus: jest.fn(),
  hide: jest.fn(),
  show: jest.fn(),
  isReady: jest.fn(() => true),
  whenReady: jest.fn(() => Promise.resolve()),
  on: jest.fn(),
  once: jest.fn(),
  removeListener: jest.fn(),
  setUserTasks: jest.fn(),
  getJumpListSettings: jest.fn(),
  setJumpList: jest.fn(),
  requestSingleInstanceLock: jest.fn(() => true),
  hasSingleInstanceLock: jest.fn(() => true),
  releaseSingleInstanceLock: jest.fn(),
  setAsDefaultProtocolClient: jest.fn(),
  removeAsDefaultProtocolClient: jest.fn(),
  isDefaultProtocolClient: jest.fn(() => false),
  getApplicationNameForProtocol: jest.fn(),
  setUserActivity: jest.fn(),
  getCurrentActivityType: jest.fn(),
  invalidateCurrentActivity: jest.fn(),
  resignCurrentActivity: jest.fn(),
  updateCurrentActivity: jest.fn(),
  setAppUserModelId: jest.fn(),
  importCertificate: jest.fn(),
  disableHardwareAcceleration: jest.fn(),
  disableDomainBlockingFor3DAPIs: jest.fn(),
  getAppMetrics: jest.fn(() => []),
  getGPUFeatureStatus: jest.fn(() => ({})),
  getGPUInfo: jest.fn(() => Promise.resolve({})),
  setBadgeCount: jest.fn(),
  getBadgeCount: jest.fn(() => 0),
  getLoginItemSettings: jest.fn(() => ({})),
  setLoginItemSettings: jest.fn(),
  isEmojiPanelSupported: jest.fn(() => false),
  showEmojiPanel: jest.fn(),
  startAccessingSecurityScopedResource: jest.fn(),
  enableSandbox: jest.fn(),
  isInApplicationsFolder: jest.fn(() => false),
  moveToApplicationsFolder: jest.fn(),
  isSecureKeyboardEntryEnabled: jest.fn(() => false),
  setSecureKeyboardEntryEnabled: jest.fn(),
  accessibilitySupportEnabled: false,
  applicationMenu: null,
  badgeCount: 0,
  commandLine: {
    appendSwitch: jest.fn(),
    appendArgument: jest.fn(),
    hasSwitch: jest.fn(() => false),
    getSwitchValue: jest.fn(() => ''),
  },
  dock: {
    bounce: jest.fn(),
    cancelBounce: jest.fn(),
    downloadFinished: jest.fn(),
    setBadge: jest.fn(),
    getBadge: jest.fn(() => ''),
    hide: jest.fn(),
    show: jest.fn(() => Promise.resolve()),
    isVisible: jest.fn(() => true),
    setMenu: jest.fn(),
    getMenu: jest.fn(),
    setIcon: jest.fn(),
  },
  isPackaged: false,
  name: 'MK13-MVP',
  userAgentFallback: 'MockUserAgent',
  allowRendererProcessReuse: true,
  runningUnderRosettaTranslation: false,
};

export const BrowserWindow = jest.fn(() => ({
  loadURL: jest.fn(() => Promise.resolve()),
  loadFile: jest.fn(() => Promise.resolve()),
  reload: jest.fn(),
  setMenu: jest.fn(),
  removeMenu: jest.fn(),
  focus: jest.fn(),
  blur: jest.fn(),
  isFocused: jest.fn(() => true),
  isDestroyed: jest.fn(() => false),
  show: jest.fn(),
  showInactive: jest.fn(),
  hide: jest.fn(),
  isVisible: jest.fn(() => true),
  isModal: jest.fn(() => false),
  maximize: jest.fn(),
  unmaximize: jest.fn(),
  isMaximized: jest.fn(() => false),
  minimize: jest.fn(),
  restore: jest.fn(),
  isMinimized: jest.fn(() => false),
  setFullScreen: jest.fn(),
  isFullScreen: jest.fn(() => false),
  setSimpleFullScreen: jest.fn(),
  isSimpleFullScreen: jest.fn(() => false),
  isNormal: jest.fn(() => true),
  setAspectRatio: jest.fn(),
  setBackgroundColor: jest.fn(),
  previewFile: jest.fn(),
  closeFilePreview: jest.fn(),
  setBounds: jest.fn(),
  getBounds: jest.fn(() => ({ x: 0, y: 0, width: 800, height: 600 })),
  getBackgroundColor: jest.fn(() => '#FFFFFF'),
  setSize: jest.fn(),
  getSize: jest.fn(() => [800, 600]),
  setContentSize: jest.fn(),
  getContentSize: jest.fn(() => [800, 600]),
  setMinimumSize: jest.fn(),
  getMinimumSize: jest.fn(() => [0, 0]),
  setMaximumSize: jest.fn(),
  getMaximumSize: jest.fn(() => [0, 0]),
  setResizable: jest.fn(),
  isResizable: jest.fn(() => true),
  setMovable: jest.fn(),
  isMovable: jest.fn(() => true),
  setMinimizable: jest.fn(),
  isMinimizable: jest.fn(() => true),
  setMaximizable: jest.fn(),
  isMaximizable: jest.fn(() => true),
  setFullScreenable: jest.fn(),
  isFullScreenable: jest.fn(() => true),
  setClosable: jest.fn(),
  isClosable: jest.fn(() => true),
  setAlwaysOnTop: jest.fn(),
  isAlwaysOnTop: jest.fn(() => false),
  moveAbove: jest.fn(),
  moveTop: jest.fn(),
  center: jest.fn(),
  setPosition: jest.fn(),
  getPosition: jest.fn(() => [0, 0]),
  setTitle: jest.fn(),
  getTitle: jest.fn(() => 'Mock Window'),
  setSheetOffset: jest.fn(),
  flashFrame: jest.fn(),
  setSkipTaskbar: jest.fn(),
  setKiosk: jest.fn(),
  isKiosk: jest.fn(() => false),
  isTabletMode: jest.fn(() => false),
  getMediaSourceId: jest.fn(() => 'mock-source-id'),
  getNativeWindowHandle: jest.fn(() => Buffer.alloc(0)),
  hookWindowMessage: jest.fn(),
  isWindowMessageHooked: jest.fn(() => false),
  unhookWindowMessage: jest.fn(),
  unhookAllWindowMessages: jest.fn(),
  setRepresentedFilename: jest.fn(),
  getRepresentedFilename: jest.fn(() => ''),
  setDocumentEdited: jest.fn(),
  isDocumentEdited: jest.fn(() => false),
  focusOnWebView: jest.fn(),
  blurWebView: jest.fn(),
  capturePage: jest.fn(() => Promise.resolve(Buffer.alloc(0))),
  setProgressBar: jest.fn(),
  setOverlayIcon: jest.fn(),
  setHasShadow: jest.fn(),
  hasShadow: jest.fn(() => true),
  setOpacity: jest.fn(),
  getOpacity: jest.fn(() => 1.0),
  setShape: jest.fn(),
  setThumbarButtons: jest.fn(),
  setThumbnailClip: jest.fn(),
  setThumbnailToolTip: jest.fn(),
  setAppDetails: jest.fn(),
  showDefinitionForSelection: jest.fn(),
  setIcon: jest.fn(),
  setWindowButtonVisibility: jest.fn(),
  setAutoHideMenuBar: jest.fn(),
  isMenuBarAutoHide: jest.fn(() => false),
  setMenuBarVisibility: jest.fn(),
  isMenuBarVisible: jest.fn(() => true),
  setVisibleOnAllWorkspaces: jest.fn(),
  isVisibleOnAllWorkspaces: jest.fn(() => false),
  setIgnoreMouseEvents: jest.fn(),
  setContentProtection: jest.fn(),
  setFocusable: jest.fn(),
  setParentWindow: jest.fn(),
  getParentWindow: jest.fn(),
  getChildWindows: jest.fn(() => []),
  setAutoHideCursor: jest.fn(),
  selectPreviousTab: jest.fn(),
  selectNextTab: jest.fn(),
  mergeAllWindows: jest.fn(),
  moveTabToNewWindow: jest.fn(),
  toggleTabBar: jest.fn(),
  addTabbedWindow: jest.fn(),
  setVibrancy: jest.fn(),
  setTrafficLightPosition: jest.fn(),
  getTrafficLightPosition: jest.fn(() => ({ x: 0, y: 0 })),
  setTouchBar: jest.fn(),
  setBrowserView: jest.fn(),
  getBrowserView: jest.fn(),
  addBrowserView: jest.fn(),
  removeBrowserView: jest.fn(),
  setTopBrowserView: jest.fn(),
  getBrowserViews: jest.fn(() => []),
  on: jest.fn(),
  once: jest.fn(),
  removeListener: jest.fn(),
  removeAllListeners: jest.fn(),
  webContents: {
    loadURL: jest.fn(() => Promise.resolve()),
    loadFile: jest.fn(() => Promise.resolve()),
    downloadURL: jest.fn(),
    getURL: jest.fn(() => 'about:blank'),
    getTitle: jest.fn(() => 'Mock Page'),
    isDestroyed: jest.fn(() => false),
    focus: jest.fn(),
    isFocused: jest.fn(() => true),
    isLoading: jest.fn(() => false),
    isLoadingMainFrame: jest.fn(() => false),
    isWaitingForResponse: jest.fn(() => false),
    stop: jest.fn(),
    reload: jest.fn(),
    reloadIgnoringCache: jest.fn(),
    canGoBack: jest.fn(() => false),
    canGoForward: jest.fn(() => false),
    canGoToOffset: jest.fn(() => false),
    clearHistory: jest.fn(),
    goBack: jest.fn(),
    goForward: jest.fn(),
    goToIndex: jest.fn(),
    goToOffset: jest.fn(),
    isCrashed: jest.fn(() => false),
    setUserAgent: jest.fn(),
    getUserAgent: jest.fn(() => 'MockUserAgent'),
    insertCSS: jest.fn(() => Promise.resolve('')),
    removeInsertedCSS: jest.fn(() => Promise.resolve()),
    executeJavaScript: jest.fn(() => Promise.resolve()),
    executeJavaScriptInIsolatedWorld: jest.fn(() => Promise.resolve()),
    setIgnoreMenuShortcuts: jest.fn(),
    setAudioMuted: jest.fn(),
    isAudioMuted: jest.fn(() => false),
    setZoomFactor: jest.fn(),
    getZoomFactor: jest.fn(() => 1.0),
    setZoomLevel: jest.fn(),
    getZoomLevel: jest.fn(() => 0),
    setVisualZoomLevelLimits: jest.fn(),
    undo: jest.fn(),
    redo: jest.fn(),
    cut: jest.fn(),
    copy: jest.fn(),
    copyImageAt: jest.fn(),
    paste: jest.fn(),
    pasteAndMatchStyle: jest.fn(),
    delete: jest.fn(),
    selectAll: jest.fn(),
    unselect: jest.fn(),
    replace: jest.fn(),
    replaceMisspelling: jest.fn(),
    insertText: jest.fn(() => Promise.resolve()),
    findInPage: jest.fn(() => ({ requestId: 1, matches: 0 })),
    stopFindInPage: jest.fn(),
    capturePage: jest.fn(() => Promise.resolve(Buffer.alloc(0))),
    isBeingCaptured: jest.fn(() => false),
    incrementCapturerCount: jest.fn(),
    decrementCapturerCount: jest.fn(),
    getPrinters: jest.fn(() => []),
    print: jest.fn(),
    printToPDF: jest.fn(() => Promise.resolve(Buffer.alloc(0))),
    addWorkSpace: jest.fn(),
    removeWorkSpace: jest.fn(),
    setDevToolsWebContents: jest.fn(),
    openDevTools: jest.fn(),
    closeDevTools: jest.fn(),
    isDevToolsOpened: jest.fn(() => false),
    isDevToolsFocused: jest.fn(() => false),
    toggleDevTools: jest.fn(),
    inspectElement: jest.fn(),
    inspectSharedWorker: jest.fn(),
    inspectSharedWorkerById: jest.fn(),
    getAllSharedWorkers: jest.fn(() => []),
    inspectServiceWorker: jest.fn(),
    send: jest.fn(),
    sendToFrame: jest.fn(),
    postMessage: jest.fn(),
    enableDeviceEmulation: jest.fn(),
    disableDeviceEmulation: jest.fn(),
    sendInputEvent: jest.fn(),
    beginFrameSubscription: jest.fn(),
    endFrameSubscription: jest.fn(),
    startDrag: jest.fn(),
    savePage: jest.fn(() => Promise.resolve()),
    showDefinitionForSelection: jest.fn(),
    isOffscreen: jest.fn(() => false),
    startPainting: jest.fn(),
    stopPainting: jest.fn(),
    isPainting: jest.fn(() => false),
    setFrameRate: jest.fn(),
    getFrameRate: jest.fn(() => 60),
    invalidate: jest.fn(),
    getType: jest.fn(() => 'window'),
    getWebPreferences: jest.fn(() => ({})),
    getOwnerBrowserWindow: jest.fn(),
    hasServiceWorker: jest.fn(() => Promise.resolve(false)),
    unregisterServiceWorker: jest.fn(() => Promise.resolve()),
    setWindowOpenHandler: jest.fn(),
    on: jest.fn(),
    once: jest.fn(),
    removeListener: jest.fn(),
    removeAllListeners: jest.fn(),
  },
  id: 1,
  autoHideMenuBar: false,
  simpleFullScreen: false,
  fullScreen: false,
  visibleOnAllWorkspaces: false,
  shadow: true,
  menuBarVisible: true,
  kiosk: false,
  documentEdited: false,
  representedFilename: '',
  title: 'Mock Window',
  minimizable: true,
  maximizable: true,
  fullScreenable: true,
  resizable: true,
  closable: true,
  movable: true,
  excludedFromShownWindowsMenu: false,
  accessibleTitle: '',
  opacity: 1.0,
}));

export const ipcMain = {
  on: jest.fn(),
  once: jest.fn(),
  removeListener: jest.fn(),
  removeAllListeners: jest.fn(),
  handle: jest.fn(),
  handleOnce: jest.fn(),
  removeHandler: jest.fn(),
};

export const ipcRenderer = {
  on: jest.fn(),
  once: jest.fn(),
  removeListener: jest.fn(),
  removeAllListeners: jest.fn(),
  send: jest.fn(),
  sendSync: jest.fn(),
  sendTo: jest.fn(),
  sendToHost: jest.fn(),
  invoke: jest.fn(() => Promise.resolve()),
  postMessage: jest.fn(),
};

export const shell = {
  showItemInFolder: jest.fn(),
  openPath: jest.fn(() => Promise.resolve('')),
  openExternal: jest.fn(() => Promise.resolve()),
  moveItemToTrash: jest.fn(() => false),
  beep: jest.fn(),
  writeShortcutLink: jest.fn(() => false),
  readShortcutLink: jest.fn(() => ({})),
};

export const dialog = {
  showOpenDialog: jest.fn(() => Promise.resolve({ canceled: false, filePaths: [] })),
  showOpenDialogSync: jest.fn(() => []),
  showSaveDialog: jest.fn(() => Promise.resolve({ canceled: false, filePath: '' })),
  showSaveDialogSync: jest.fn(() => ''),
  showMessageBox: jest.fn(() => Promise.resolve({ response: 0 })),
  showMessageBoxSync: jest.fn(() => 0),
  showErrorBox: jest.fn(),
  showCertificateTrustDialog: jest.fn(() => Promise.resolve()),
};

export const Menu = {
  buildFromTemplate: jest.fn(() => ({
    popup: jest.fn(),
    closePopup: jest.fn(),
    append: jest.fn(),
    insert: jest.fn(),
    items: [],
  })),
  setApplicationMenu: jest.fn(),
  getApplicationMenu: jest.fn(),
  sendActionToFirstResponder: jest.fn(),
};

export const MenuItem = jest.fn(() => ({
  enabled: true,
  visible: true,
  checked: false,
}));

export const Tray = jest.fn(() => ({
  destroy: jest.fn(),
  setImage: jest.fn(),
  setPressedImage: jest.fn(),
  setToolTip: jest.fn(),
  setTitle: jest.fn(),
  getTitle: jest.fn(() => ''),
  setIgnoreDoubleClickEvents: jest.fn(),
  getIgnoreDoubleClickEvents: jest.fn(() => false),
  displayBalloon: jest.fn(),
  popUpContextMenu: jest.fn(),
  setContextMenu: jest.fn(),
  getBounds: jest.fn(() => ({ x: 0, y: 0, width: 0, height: 0 })),
  isDestroyed: jest.fn(() => false),
  on: jest.fn(),
  once: jest.fn(),
  removeListener: jest.fn(),
  removeAllListeners: jest.fn(),
}));

export const nativeImage = {
  createEmpty: jest.fn(() => ({})),
  createFromPath: jest.fn(() => ({})),
  createFromBitmap: jest.fn(() => ({})),
  createFromBuffer: jest.fn(() => ({})),
  createFromDataURL: jest.fn(() => ({})),
  createFromNamedImage: jest.fn(() => ({})),
};

export const screen = {
  getCursorScreenPoint: jest.fn(() => ({ x: 0, y: 0 })),
  getPrimaryDisplay: jest.fn(() => ({
    id: 1,
    bounds: { x: 0, y: 0, width: 1920, height: 1080 },
    workArea: { x: 0, y: 0, width: 1920, height: 1040 },
    scaleFactor: 1.0,
    rotation: 0,
    internal: true,
  })),
  getAllDisplays: jest.fn(() => []),
  getDisplayNearestPoint: jest.fn(() => ({})),
  getDisplayMatching: jest.fn(() => ({})),
  on: jest.fn(),
  removeListener: jest.fn(),
};

export const clipboard = {
  readText: jest.fn(() => ''),
  writeText: jest.fn(),
  readHTML: jest.fn(() => ''),
  writeHTML: jest.fn(),
  readImage: jest.fn(() => ({})),
  writeImage: jest.fn(),
  readRTF: jest.fn(() => ''),
  writeRTF: jest.fn(),
  readBookmark: jest.fn(() => ({ title: '', url: '' })),
  writeBookmark: jest.fn(),
  readFindText: jest.fn(() => ''),
  writeFindText: jest.fn(),
  clear: jest.fn(),
  availableFormats: jest.fn(() => []),
  has: jest.fn(() => false),
  read: jest.fn(() => ''),
  write: jest.fn(),
};

export const crashReporter = {
  start: jest.fn(),
  getLastCrashReport: jest.fn(),
  getUploadedReports: jest.fn(() => []),
  getUploadToServer: jest.fn(() => true),
  setUploadToServer: jest.fn(),
  addExtraParameter: jest.fn(),
  removeExtraParameter: jest.fn(),
  getParameters: jest.fn(() => ({})),
};

export const remote = {
  app,
  BrowserWindow,
  dialog,
  Menu,
  MenuItem,
  shell,
  screen,
  clipboard,
  nativeImage,
  getCurrentWindow: jest.fn(() => new BrowserWindow()),
  getCurrentWebContents: jest.fn(() => ({})),
  getGlobal: jest.fn(),
  process: {
    platform: 'win32',
    versions: {
      electron: '13.1.0',
      node: '14.16.0',
      chrome: '91.0.4472.164',
    },
  },
};
