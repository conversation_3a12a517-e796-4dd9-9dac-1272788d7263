/**
 * React Hook for Context Detection
 * Integrates context detection service with Redux store
 */

import { useEffect, useCallback, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../store/store';
import { updateCurrentContextState, createQuickContext } from '../store/slices/contextSlice';
import { sendMessage } from '../store/slices/socketSlice';
import contextDetectionService, { DetectedContext } from '../services/contextDetection';

export interface UseContextDetectionOptions {
  autoStart?: boolean;
  detectionInterval?: number;
  sendToBackend?: boolean;
  createContextOnChange?: boolean;
  confidenceThreshold?: number;
}

export interface ContextDetectionHook {
  isDetecting: boolean;
  lastDetectedContext: DetectedContext | null;
  startDetection: () => void;
  stopDetection: () => void;
  detectNow: () => Promise<DetectedContext>;
  sendContextToBackend: (context: DetectedContext) => void;
}

export const useContextDetection = (
  options: UseContextDetectionOptions = {}
): ContextDetectionHook => {
  const {
    autoStart = true,
    detectionInterval = 30000,
    sendToBackend = true,
    createContextOnChange = false,
    confidenceThreshold = 0.6,
  } = options;

  const dispatch = useDispatch();
  const { currentContext } = useSelector((state: RootState) => state.context);
  const { userId } = useSelector((state: RootState) => state.user);
  const { isConnected } = useSelector((state: RootState) => state.socket);

  const isDetectingRef = useRef(false);
  const lastDetectedContextRef = useRef<DetectedContext | null>(null);

  /**
   * Handle context change
   */
  const handleContextChange = useCallback(
    async (detectedContext: DetectedContext) => {
      lastDetectedContextRef.current = detectedContext;

      // Update current context state in Redux
      if (currentContext) {
        dispatch(updateCurrentContextState({
          activeApplications: [detectedContext.application.activeApplication].filter(Boolean),
          currentProject: detectedContext.work.currentProject,
          browserTabs: detectedContext.browser.url ? [{
            title: detectedContext.browser.title || '',
            url: detectedContext.browser.url,
          }] : undefined,
          lastInteraction: detectedContext.system.timestamp,
        }));
      }

      // Create new context if confidence is high and no current context
      if (
        createContextOnChange &&
        !currentContext &&
        detectedContext.confidence >= confidenceThreshold &&
        detectedContext.work.currentProject
      ) {
        dispatch(createQuickContext({
          name: `${detectedContext.work.currentProject} - ${new Date().toLocaleTimeString()}`,
          type: 'work',
          userId: userId || 'anonymous',
        }));
      }

      // Send to backend if enabled and connected
      if (sendToBackend && isConnected) {
        sendContextToBackend(detectedContext);
      }
    },
    [
      dispatch,
      currentContext,
      userId,
      isConnected,
      sendToBackend,
      createContextOnChange,
      confidenceThreshold,
    ]
  );

  /**
   * Send context to backend
   */
  const sendContextToBackend = useCallback(
    (context: DetectedContext) => {
      if (!isConnected) {
        console.warn('Cannot send context to backend: not connected');
        return;
      }

      dispatch(sendMessage({
        event: 'context_update',
        data: {
          user_id: userId || 'anonymous',
          context: context,
          timestamp: new Date().toISOString(),
        },
      }));
    },
    [dispatch, isConnected, userId]
  );

  /**
   * Start context detection
   */
  const startDetection = useCallback(() => {
    if (isDetectingRef.current) return;

    isDetectingRef.current = true;
    contextDetectionService.onContextChange(handleContextChange);
    contextDetectionService.startDetection(detectionInterval);
    
    console.log('Context detection started');
  }, [handleContextChange, detectionInterval]);

  /**
   * Stop context detection
   */
  const stopDetection = useCallback(() => {
    if (!isDetectingRef.current) return;

    isDetectingRef.current = false;
    contextDetectionService.stopDetection();
    contextDetectionService.removeCallback(handleContextChange);
    
    console.log('Context detection stopped');
  }, [handleContextChange]);

  /**
   * Detect context immediately
   */
  const detectNow = useCallback(async (): Promise<DetectedContext> => {
    const context = await contextDetectionService.detectContext();
    handleContextChange(context);
    return context;
  }, [handleContextChange]);

  /**
   * Auto-start detection on mount
   */
  useEffect(() => {
    if (autoStart) {
      startDetection();
    }

    return () => {
      stopDetection();
    };
  }, [autoStart, startDetection, stopDetection]);

  /**
   * Handle connection state changes
   */
  useEffect(() => {
    if (isConnected && lastDetectedContextRef.current && sendToBackend) {
      // Send last detected context when connection is restored
      sendContextToBackend(lastDetectedContextRef.current);
    }
  }, [isConnected, sendToBackend, sendContextToBackend]);

  return {
    isDetecting: isDetectingRef.current,
    lastDetectedContext: lastDetectedContextRef.current,
    startDetection,
    stopDetection,
    detectNow,
    sendContextToBackend,
  };
};

/**
 * Hook for enhanced context detection with system performance data
 */
export const useEnhancedContextDetection = (
  options: UseContextDetectionOptions = {}
) => {
  const basicHook = useContextDetection(options);

  const detectEnhancedNow = useCallback(async () => {
    const enhancedContext = await contextDetectionService.getEnhancedContext();
    
    // Send enhanced context to backend
    if (options.sendToBackend !== false) {
      basicHook.sendContextToBackend(enhancedContext);
    }
    
    return enhancedContext;
  }, [basicHook, options.sendToBackend]);

  return {
    ...basicHook,
    detectEnhancedNow,
  };
};

export default useContextDetection;
