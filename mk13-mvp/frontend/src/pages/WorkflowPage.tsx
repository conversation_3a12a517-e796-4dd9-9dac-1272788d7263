/**
 * Workflow Page Component
 * Main page for workflow management and visual builder
 */

import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useParams, useNavigate } from 'react-router-dom';
import { RootState } from '../store/store';
import {
  fetchWorkflows,
  createWorkflow,
  updateWorkflow,
  deleteWorkflow,
  executeWorkflow,
  setCurrentWorkflow,
  clearCurrentWorkflow
} from '../store/slices/workflowSlice';
import WorkflowCanvas from '../components/WorkflowBuilder/WorkflowCanvas';
import WorkflowList from '../components/WorkflowBuilder/WorkflowList';
import WorkflowTemplates from '../components/WorkflowBuilder/WorkflowTemplates';
import './WorkflowPage.css';

interface WorkflowPageProps {}

const WorkflowPage: React.FC<WorkflowPageProps> = () => {
  const { workflowId } = useParams<{ workflowId?: string }>();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const {
    workflows,
    currentWorkflow,
    isLoading,
    error,
    isExecuting
  } = useSelector((state: RootState) => state.workflows);

  const { user } = useSelector((state: RootState) => state.auth);

  const [view, setView] = useState<'list' | 'builder' | 'templates'>('list');
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  useEffect(() => {
    if (user) {
      dispatch(fetchWorkflows());
    }
  }, [user, dispatch]);

  useEffect(() => {
    if (workflowId && workflowId !== 'new') {
      const workflow = workflows.find(w => w.id === workflowId);
      if (workflow) {
        dispatch(setCurrentWorkflow(workflow));
        setView('builder');
      }
    } else if (workflowId === 'new') {
      dispatch(clearCurrentWorkflow());
      setView('builder');
    } else {
      setView('list');
    }
  }, [workflowId, workflows, dispatch]);

  const handleCreateWorkflow = async (workflowData: any) => {
    try {
      const result = await dispatch(createWorkflow(workflowData)).unwrap();
      navigate(`/workflows/${result.id}`);
      setHasUnsavedChanges(false);
    } catch (error) {
      console.error('Failed to create workflow:', error);
    }
  };

  const handleUpdateWorkflow = async (workflowId: string, updates: any) => {
    try {
      await dispatch(updateWorkflow({ id: workflowId, updates })).unwrap();
      setHasUnsavedChanges(false);
    } catch (error) {
      console.error('Failed to update workflow:', error);
    }
  };

  const handleDeleteWorkflow = async (workflowId: string) => {
    if (window.confirm('Are you sure you want to delete this workflow?')) {
      try {
        await dispatch(deleteWorkflow(workflowId)).unwrap();
        if (currentWorkflow?.id === workflowId) {
          navigate('/workflows');
        }
      } catch (error) {
        console.error('Failed to delete workflow:', error);
      }
    }
  };

  const handleExecuteWorkflow = async (workflowId: string, triggerData?: any) => {
    try {
      await dispatch(executeWorkflow({ id: workflowId, triggerData })).unwrap();
    } catch (error) {
      console.error('Failed to execute workflow:', error);
    }
  };

  const handleSaveWorkflow = async () => {
    if (!currentWorkflow) return;

    if (currentWorkflow.id) {
      // Update existing workflow
      await handleUpdateWorkflow(currentWorkflow.id, currentWorkflow);
    } else {
      // Create new workflow
      await handleCreateWorkflow(currentWorkflow);
    }
  };

  const handleWorkflowChange = () => {
    setHasUnsavedChanges(true);
  };

  const handleNavigateBack = () => {
    if (hasUnsavedChanges) {
      setShowSaveDialog(true);
    } else {
      navigate('/workflows');
    }
  };

  const handleSaveAndExit = async () => {
    await handleSaveWorkflow();
    setShowSaveDialog(false);
    navigate('/workflows');
  };

  const handleDiscardChanges = () => {
    setShowSaveDialog(false);
    setHasUnsavedChanges(false);
    navigate('/workflows');
  };

  const renderHeader = () => (
    <div className="workflow-page-header">
      <div className="header-left">
        <button
          className="back-btn"
          onClick={handleNavigateBack}
          title="Back to workflows"
        >
          ← Back
        </button>
        <h1 className="page-title">
          {view === 'list' && 'Workflows'}
          {view === 'builder' && (currentWorkflow?.name || 'New Workflow')}
          {view === 'templates' && 'Workflow Templates'}
        </h1>
        {hasUnsavedChanges && (
          <span className="unsaved-indicator">• Unsaved changes</span>
        )}
      </div>

      <div className="header-actions">
        {view === 'list' && (
          <>
            <button
              className="action-btn secondary"
              onClick={() => setView('templates')}
            >
              Templates
            </button>
            <button
              className="action-btn primary"
              onClick={() => navigate('/workflows/new')}
            >
              + New Workflow
            </button>
          </>
        )}

        {view === 'builder' && (
          <>
            <button
              className="action-btn secondary"
              onClick={() => currentWorkflow?.id && handleExecuteWorkflow(currentWorkflow.id)}
              disabled={!currentWorkflow?.id || isExecuting}
            >
              {isExecuting ? 'Running...' : 'Test Run'}
            </button>
            <button
              className="action-btn primary"
              onClick={handleSaveWorkflow}
              disabled={isLoading}
            >
              {isLoading ? 'Saving...' : 'Save'}
            </button>
          </>
        )}

        {view === 'templates' && (
          <button
            className="action-btn secondary"
            onClick={() => setView('list')}
          >
            Back to List
          </button>
        )}
      </div>
    </div>
  );

  const renderContent = () => {
    if (view === 'list') {
      return (
        <WorkflowList
          workflows={workflows}
          onEdit={(workflow) => navigate(`/workflows/${workflow.id}`)}
          onDelete={handleDeleteWorkflow}
          onExecute={handleExecuteWorkflow}
          onDuplicate={(workflow) => {
            const duplicated = {
              ...workflow,
              id: undefined,
              name: `${workflow.name} (Copy)`,
              created_at: undefined,
              updated_at: undefined
            };
            handleCreateWorkflow(duplicated);
          }}
        />
      );
    }

    if (view === 'templates') {
      return (
        <WorkflowTemplates
          onSelectTemplate={(template) => {
            const newWorkflow = {
              name: template.name,
              description: template.description,
              trigger: template.trigger,
              nodes: template.nodes,
              enabled: false
            };
            dispatch(setCurrentWorkflow(newWorkflow));
            navigate('/workflows/new');
          }}
        />
      );
    }

    if (view === 'builder') {
      return (
        <WorkflowCanvas
          workflowId={currentWorkflow?.id || 'new'}
          onSave={handleSaveWorkflow}
          onChange={handleWorkflowChange}
        />
      );
    }

    return null;
  };

  return (
    <div className="workflow-page">
      {renderHeader()}
      
      <div className="workflow-page-content">
        {error && (
          <div className="error-banner">
            <span>⚠️ {error}</span>
            <button onClick={() => dispatch(clearError())}>×</button>
          </div>
        )}

        {renderContent()}
      </div>

      {/* Save Dialog */}
      {showSaveDialog && (
        <div className="save-dialog-overlay">
          <div className="save-dialog">
            <h3>Unsaved Changes</h3>
            <p>You have unsaved changes. What would you like to do?</p>
            <div className="dialog-actions">
              <button
                className="action-btn secondary"
                onClick={handleDiscardChanges}
              >
                Discard Changes
              </button>
              <button
                className="action-btn primary"
                onClick={handleSaveAndExit}
              >
                Save & Exit
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default WorkflowPage;
