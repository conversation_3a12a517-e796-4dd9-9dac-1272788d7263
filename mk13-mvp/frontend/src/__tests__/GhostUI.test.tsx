/**
 * Tests for GhostUI Component
 * Comprehensive testing of the main UI component
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import '@testing-library/jest-dom';

import GhostUI from '../components/GhostUI';
import uiSlice from '../store/slices/uiSlice';
import chatSlice from '../store/slices/chatSlice';
import socketSlice from '../store/slices/socketSlice';
import userSlice from '../store/slices/userSlice';
import notificationSlice from '../store/slices/notificationSlice';
import contextSlice from '../store/slices/contextSlice';

// Mock the context detection hook
jest.mock('../hooks/useContextDetection', () => ({
  __esModule: true,
  default: () => ({
    isDetecting: false,
    lastDetectedContext: null,
    startDetection: jest.fn(),
    stopDetection: jest.fn(),
    detectNow: jest.fn(),
    sendContextToBackend: jest.fn(),
  }),
}));

// Mock Socket.IO
jest.mock('socket.io-client', () => ({
  io: jest.fn(() => ({
    on: jest.fn(),
    emit: jest.fn(),
    disconnect: jest.fn(),
    connected: true,
  })),
}));

// Create test store
const createTestStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      ui: uiSlice,
      chat: chatSlice,
      socket: socketSlice,
      user: userSlice,
      notifications: notificationSlice,
      context: contextSlice,
    },
    preloadedState: {
      ui: {
        isVisible: true,
        currentMode: 'chat',
        position: { x: 100, y: 100 },
        isMinimized: false,
        theme: 'dark',
        settings: {
          autoHide: false,
          hotkey: 'Ctrl+Shift+M',
          opacity: 0.95,
          alwaysOnTop: true,
        },
      },
      socket: {
        isConnected: false,
        isConnecting: false,
        error: null,
        connectionId: null,
        lastPing: null,
        reconnectAttempts: 0,
        maxReconnectAttempts: 5,
      },
      context: {
        contexts: [],
        currentContext: null,
        isLoading: false,
        error: null,
        autoSaveEnabled: true,
        contextSwitchHistory: [],
      },
      user: {
        userId: 'test-user',
        email: '<EMAIL>',
        name: 'Test User',
        isAuthenticated: false,
        profile: null,
        preferences: {},
        isLoading: false,
        error: null,
      },
      chat: {
        messages: [],
        isLoading: false,
        error: null,
        currentConversationId: null,
        suggestions: [],
      },
      notifications: {
        notifications: [],
        unreadCount: 0,
        settings: {
          enabled: true,
          sound: true,
          desktop: true,
        },
      },
      ...initialState,
    },
  });
};

const renderWithProvider = (component: React.ReactElement, store = createTestStore()) => {
  return render(
    <Provider store={store}>
      {component}
    </Provider>
  );
};

describe('GhostUI Component', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  test('renders GhostUI when visible', () => {
    renderWithProvider(<GhostUI />);
    
    expect(screen.getByText('MK13 Assistant')).toBeInTheDocument();
    expect(screen.getByText('💬 Chat')).toBeInTheDocument();
    expect(screen.getByText('🎤 Voice')).toBeInTheDocument();
    expect(screen.getByText('⚡ Actions')).toBeInTheDocument();
    expect(screen.getByText('📂 Context')).toBeInTheDocument();
  });

  test('does not render when not visible', () => {
    const store = createTestStore({
      ui: {
        isVisible: false,
        currentMode: 'chat',
        position: { x: 100, y: 100 },
        isMinimized: false,
        theme: 'dark',
        settings: {
          autoHide: false,
          hotkey: 'Ctrl+Shift+M',
          opacity: 0.95,
          alwaysOnTop: true,
        },
      },
    });

    renderWithProvider(<GhostUI />, store);
    
    expect(screen.queryByText('MK13 Assistant')).not.toBeInTheDocument();
  });

  test('switches between modes correctly', () => {
    renderWithProvider(<GhostUI />);
    
    // Initially should be in chat mode
    expect(screen.getByText('💬 Chat')).toHaveClass('active');
    
    // Click voice mode
    fireEvent.click(screen.getByText('🎤 Voice'));
    
    // Should switch to voice mode
    expect(screen.getByText('🎤 Voice')).toHaveClass('active');
    expect(screen.getByText('💬 Chat')).not.toHaveClass('active');
  });

  test('shows connection status correctly', () => {
    // Test disconnected state
    renderWithProvider(<GhostUI />);
    expect(screen.getByText('🔴 Disconnected')).toBeInTheDocument();
    
    // Test connected state
    const connectedStore = createTestStore({
      socket: {
        isConnected: true,
        isConnecting: false,
        error: null,
        connectionId: 'test-connection',
        lastPing: Date.now(),
        reconnectAttempts: 0,
        maxReconnectAttempts: 5,
      },
    });
    
    renderWithProvider(<GhostUI />, connectedStore);
    expect(screen.getByText('🟢 Connected')).toBeInTheDocument();
  });

  test('shows context detection status', () => {
    const mockUseContextDetection = require('../hooks/useContextDetection').default;
    mockUseContextDetection.mockReturnValue({
      isDetecting: true,
      lastDetectedContext: {
        work: { currentProject: 'Test Project' },
        application: { activeApplication: 'VS Code' },
        confidence: 0.85,
      },
      startDetection: jest.fn(),
      stopDetection: jest.fn(),
      detectNow: jest.fn(),
      sendContextToBackend: jest.fn(),
    });

    renderWithProvider(<GhostUI />);
    
    expect(screen.getByText('👁️ Detecting')).toBeInTheDocument();
    expect(screen.getByText(/Test Project/)).toBeInTheDocument();
    expect(screen.getByText(/VS Code/)).toBeInTheDocument();
    expect(screen.getByText(/85% confidence/)).toBeInTheDocument();
  });

  test('handles minimize and close actions', () => {
    const onCloseMock = jest.fn();
    renderWithProvider(<GhostUI onClose={onCloseMock} />);
    
    // Test minimize
    const minimizeButton = screen.getByTitle(/Minimize|Expand/);
    fireEvent.click(minimizeButton);
    
    // Test close
    const closeButton = screen.getByTitle('Close');
    fireEvent.click(closeButton);
    
    expect(onCloseMock).toHaveBeenCalled();
  });

  test('responds to keyboard shortcuts', () => {
    renderWithProvider(<GhostUI />);
    
    // Simulate Ctrl+Shift+M keypress
    fireEvent.keyDown(window, {
      key: 'M',
      ctrlKey: true,
      shiftKey: true,
    });
    
    // The UI should toggle visibility (this would be handled by the store)
    // We can't easily test this without mocking the dispatch
  });

  test('displays current context information', () => {
    const contextStore = createTestStore({
      context: {
        contexts: [
          {
            id: 'context-1',
            name: 'Work Context',
            state: {
              currentProject: 'MK13',
              activeApplications: ['VS Code', 'Chrome'],
              lastInteraction: new Date().toISOString(),
            },
            metadata: {
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
              category: 'work',
              priority: 'high',
            },
            userId: 'test-user',
          },
        ],
        currentContext: {
          id: 'context-1',
          name: 'Work Context',
          state: {
            currentProject: 'MK13',
            activeApplications: ['VS Code', 'Chrome'],
            lastInteraction: new Date().toISOString(),
          },
          metadata: {
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            category: 'work',
            priority: 'high',
          },
          userId: 'test-user',
        },
        isLoading: false,
        error: null,
        autoSaveEnabled: true,
        contextSwitchHistory: [],
      },
    });

    renderWithProvider(<GhostUI />, contextStore);
    
    expect(screen.getByText(/Context: Work Context/)).toBeInTheDocument();
  });

  test('handles drag and drop functionality', () => {
    renderWithProvider(<GhostUI />);
    
    const header = screen.getByText('MK13 Assistant').closest('.ghost-ui-header');
    
    // Simulate mouse down to start drag
    fireEvent.mouseDown(header!, {
      clientX: 100,
      clientY: 100,
    });
    
    // Simulate mouse move
    fireEvent.mouseMove(document, {
      clientX: 150,
      clientY: 150,
    });
    
    // Simulate mouse up to end drag
    fireEvent.mouseUp(document);
    
    // The component should handle the drag (position would be updated in store)
  });

  test('shows error states appropriately', () => {
    const errorStore = createTestStore({
      socket: {
        isConnected: false,
        isConnecting: false,
        error: 'Connection failed',
        connectionId: null,
        lastPing: null,
        reconnectAttempts: 3,
        maxReconnectAttempts: 5,
      },
    });

    renderWithProvider(<GhostUI />, errorStore);
    
    expect(screen.getByText('🔴 Disconnected')).toBeInTheDocument();
  });

  test('integrates with all mode components', () => {
    renderWithProvider(<GhostUI />);
    
    // Test chat mode (default)
    expect(screen.getByText('💬 Chat')).toHaveClass('active');
    
    // Switch to voice mode
    fireEvent.click(screen.getByText('🎤 Voice'));
    expect(screen.getByText('🎤 Voice')).toHaveClass('active');
    
    // Switch to actions mode
    fireEvent.click(screen.getByText('⚡ Actions'));
    expect(screen.getByText('⚡ Actions')).toHaveClass('active');
    
    // Switch to context mode
    fireEvent.click(screen.getByText('📂 Context'));
    expect(screen.getByText('📂 Context')).toHaveClass('active');
  });
});

describe('GhostUI Accessibility', () => {
  test('has proper ARIA labels and roles', () => {
    renderWithProvider(<GhostUI />);
    
    // Check for proper button roles
    const buttons = screen.getAllByRole('button');
    expect(buttons.length).toBeGreaterThan(0);
    
    // Check for proper titles/tooltips
    expect(screen.getByTitle('Close')).toBeInTheDocument();
    expect(screen.getByTitle(/Minimize|Expand/)).toBeInTheDocument();
  });

  test('supports keyboard navigation', () => {
    renderWithProvider(<GhostUI />);
    
    // Tab through interactive elements
    const chatButton = screen.getByText('💬 Chat');
    chatButton.focus();
    expect(chatButton).toHaveFocus();
    
    // Test Enter key activation
    fireEvent.keyDown(chatButton, { key: 'Enter' });
    // Should activate the button (mode switch)
  });
});
