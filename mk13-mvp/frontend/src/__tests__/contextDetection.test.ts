/**
 * Tests for Context Detection Service
 * Tests client-side context detection functionality
 */

import { contextDetectionService, DetectedContext } from '../services/contextDetection';

// Mock browser APIs
const mockNavigator = {
  platform: 'Win32',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
  language: 'en-US',
};

const mockScreen = {
  width: 1920,
  height: 1080,
};

const mockWindow = {
  innerWidth: 1200,
  innerHeight: 800,
  location: {
    href: 'http://localhost:3000/test',
    hostname: 'localhost',
  },
};

const mockDocument = {
  title: 'MK13 MVP - Test Page',
  hasFocus: () => true,
};

const mockIntl = {
  DateTimeFormat: () => ({
    resolvedOptions: () => ({ timeZone: 'America/New_York' }),
  }),
};

// Setup global mocks
Object.defineProperty(global, 'navigator', {
  value: mockNavigator,
  writable: true,
});

Object.defineProperty(global, 'screen', {
  value: mockScreen,
  writable: true,
});

Object.defineProperty(global, 'window', {
  value: mockWindow,
  writable: true,
});

Object.defineProperty(global, 'document', {
  value: mockDocument,
  writable: true,
});

Object.defineProperty(global, 'Intl', {
  value: mockIntl,
  writable: true,
});

describe('ContextDetectionService', () => {
  beforeEach(() => {
    // Reset service state
    contextDetectionService.stopDetection();
    jest.clearAllMocks();
  });

  afterEach(() => {
    contextDetectionService.stopDetection();
  });

  describe('Basic Context Detection', () => {
    test('detects system context correctly', async () => {
      const context = await contextDetectionService.detectContext();
      
      expect(context.system).toEqual({
        platform: 'Win32',
        userAgent: expect.stringContaining('Mozilla'),
        language: 'en-US',
        timezone: 'America/New_York',
        screenResolution: {
          width: 1920,
          height: 1080,
        },
        windowSize: {
          width: 1200,
          height: 800,
        },
        timestamp: expect.any(String),
      });
    });

    test('detects browser context correctly', async () => {
      const context = await contextDetectionService.detectContext();
      
      expect(context.browser).toEqual({
        url: 'http://localhost:3000/test',
        title: 'MK13 MVP - Test Page',
        domain: 'localhost',
        isActive: true,
      });
    });

    test('detects application context correctly', async () => {
      const context = await contextDetectionService.detectContext();
      
      expect(context.application).toEqual({
        activeApplication: 'Browser',
        windowTitle: 'MK13 MVP - Test Page',
        isElectronApp: false,
        electronVersion: undefined,
      });
    });

    test('detects work context correctly', async () => {
      const context = await contextDetectionService.detectContext();
      
      expect(context.work).toEqual({
        currentProject: 'MK13',
        lastActivity: expect.any(String),
        sessionDuration: expect.any(Number),
      });
      
      expect(context.work.sessionDuration).toBeGreaterThan(0);
    });

    test('calculates confidence score', async () => {
      const context = await contextDetectionService.detectContext();
      
      expect(context.confidence).toBeGreaterThan(0);
      expect(context.confidence).toBeLessThanOrEqual(1);
      expect(context.detectionMethod).toBe('client-side');
    });
  });

  describe('Project Detection', () => {
    test('detects MK13 project from title', async () => {
      mockDocument.title = 'MK13 Development - VS Code';
      
      const context = await contextDetectionService.detectContext();
      expect(context.work.currentProject).toBe('MK13');
    });

    test('detects GitHub project from title', async () => {
      mockDocument.title = 'GitHub - user/repo';
      
      const context = await contextDetectionService.detectContext();
      expect(context.work.currentProject).toBe('GitHub');
    });

    test('detects development project from title', async () => {
      mockDocument.title = 'VS Code - project.js';
      
      const context = await contextDetectionService.detectContext();
      expect(context.work.currentProject).toBe('Development');
    });

    test('returns undefined for unknown projects', async () => {
      mockDocument.title = 'Random Website';
      
      const context = await contextDetectionService.detectContext();
      expect(context.work.currentProject).toBeUndefined();
    });
  });

  describe('Electron Detection', () => {
    test('detects Electron environment', async () => {
      // Mock Electron environment
      (global as any).window = {
        ...mockWindow,
        process: {
          versions: {
            electron: '13.1.0',
          },
        },
      };
      
      const context = await contextDetectionService.detectContext();
      
      expect(context.application.isElectronApp).toBe(true);
      expect(context.application.electronVersion).toBe('13.1.0');
      expect(context.application.activeApplication).toBe('MK13-MVP');
    });

    test('detects non-Electron environment', async () => {
      // Ensure no Electron process
      (global as any).window = mockWindow;
      
      const context = await contextDetectionService.detectContext();
      
      expect(context.application.isElectronApp).toBe(false);
      expect(context.application.electronVersion).toBeUndefined();
      expect(context.application.activeApplication).toBe('Browser');
    });
  });

  describe('Automatic Detection', () => {
    test('starts and stops automatic detection', () => {
      const callback = jest.fn();
      contextDetectionService.onContextChange(callback);
      
      // Start detection with short interval
      contextDetectionService.startDetection(100);
      
      // Should start detecting
      expect(contextDetectionService.stopDetection).toBeDefined();
      
      // Stop detection
      contextDetectionService.stopDetection();
    });

    test('calls callbacks on context changes', (done) => {
      const callback = jest.fn((context: DetectedContext) => {
        expect(context).toBeDefined();
        expect(context.system).toBeDefined();
        expect(context.browser).toBeDefined();
        expect(context.application).toBeDefined();
        expect(context.work).toBeDefined();
        
        contextDetectionService.stopDetection();
        done();
      });
      
      contextDetectionService.onContextChange(callback);
      contextDetectionService.startDetection(50);
    });

    test('removes callbacks correctly', () => {
      const callback1 = jest.fn();
      const callback2 = jest.fn();
      
      contextDetectionService.onContextChange(callback1);
      contextDetectionService.onContextChange(callback2);
      
      // Remove first callback
      contextDetectionService.removeCallback(callback1);
      
      // Trigger detection
      contextDetectionService.detectContext();
      
      // Only callback2 should be called
      expect(callback1).not.toHaveBeenCalled();
    });
  });

  describe('Enhanced Context Detection', () => {
    test('includes performance data when available', async () => {
      // Mock performance API
      (global as any).window.performance = {
        memory: {
          usedJSHeapSize: 1000000,
          totalJSHeapSize: 2000000,
          jsHeapSizeLimit: 4000000,
        },
        timing: {
          navigationStart: Date.now() - 5000,
          loadEventEnd: Date.now() - 4000,
        },
      };
      
      const enhancedContext = await contextDetectionService.getEnhancedContext();
      
      expect(enhancedContext.performance).toBeDefined();
      expect(enhancedContext.performance.memory).toBeDefined();
      expect(enhancedContext.performance.timing).toBeDefined();
    });

    test('includes network data when available', async () => {
      // Mock connection API
      (global as any).navigator.connection = {
        effectiveType: '4g',
        downlink: 10,
        rtt: 100,
      };
      
      const enhancedContext = await contextDetectionService.getEnhancedContext();
      
      expect(enhancedContext.network).toBeDefined();
      expect(enhancedContext.network.effectiveType).toBe('4g');
      expect(enhancedContext.network.downlink).toBe(10);
      expect(enhancedContext.network.rtt).toBe(100);
    });

    test('handles missing performance APIs gracefully', async () => {
      // Remove performance API
      delete (global as any).window.performance;
      delete (global as any).navigator.connection;
      
      const enhancedContext = await contextDetectionService.getEnhancedContext();
      
      expect(enhancedContext.performance).toBeUndefined();
      expect(enhancedContext.network).toBeUndefined();
      
      // Should still have basic context
      expect(enhancedContext.system).toBeDefined();
      expect(enhancedContext.browser).toBeDefined();
    });
  });

  describe('Activity Tracking', () => {
    test('tracks user activity', () => {
      const initialTime = Date.now();
      
      // Simulate user activity
      const clickEvent = new Event('click');
      document.dispatchEvent(clickEvent);
      
      // Activity should be tracked (we can't easily test the private lastActivityTime)
      // But we can verify the service is set up to track activity
      expect(document.addEventListener).toBeDefined();
    });

    test('updates session duration', async () => {
      const context1 = await contextDetectionService.detectContext();
      
      // Wait a bit
      await new Promise(resolve => setTimeout(resolve, 10));
      
      const context2 = await contextDetectionService.detectContext();
      
      expect(context2.work.sessionDuration).toBeGreaterThan(context1.work.sessionDuration);
    });
  });

  describe('Confidence Calculation', () => {
    test('increases confidence with more data', async () => {
      // Test with minimal data
      mockDocument.title = 'Untitled';
      mockDocument.hasFocus = () => false;
      mockWindow.location.hostname = 'localhost';
      
      const context1 = await contextDetectionService.detectContext();
      const confidence1 = context1.confidence;
      
      // Test with more data
      mockDocument.title = 'MK13 Development';
      mockDocument.hasFocus = () => true;
      mockWindow.location.hostname = 'github.com';
      
      const context2 = await contextDetectionService.detectContext();
      const confidence2 = context2.confidence;
      
      expect(confidence2).toBeGreaterThan(confidence1);
    });

    test('confidence never exceeds 1.0', async () => {
      // Set up maximum confidence scenario
      mockDocument.title = 'MK13 Development - VS Code';
      mockDocument.hasFocus = () => true;
      mockWindow.location.hostname = 'github.com';
      (global as any).window.process = { versions: { electron: '13.1.0' } };
      
      const context = await contextDetectionService.detectContext();
      
      expect(context.confidence).toBeLessThanOrEqual(1.0);
    });
  });

  describe('Error Handling', () => {
    test('handles detection errors gracefully', async () => {
      // Mock an error in detection
      const originalConsoleError = console.error;
      console.error = jest.fn();
      
      // Cause an error by making navigator undefined
      (global as any).navigator = undefined;
      
      try {
        const context = await contextDetectionService.detectContext();
        // Should still return some context even with errors
        expect(context).toBeDefined();
      } catch (error) {
        // Should not throw unhandled errors
        expect(error).toBeUndefined();
      }
      
      console.error = originalConsoleError;
    });

    test('handles callback errors gracefully', () => {
      const errorCallback = jest.fn(() => {
        throw new Error('Callback error');
      });
      
      const goodCallback = jest.fn();
      
      contextDetectionService.onContextChange(errorCallback);
      contextDetectionService.onContextChange(goodCallback);
      
      // Should not throw when callbacks error
      expect(() => {
        contextDetectionService.detectContext();
      }).not.toThrow();
      
      // Good callback should still be called
      expect(goodCallback).toHaveBeenCalled();
    });
  });
});
