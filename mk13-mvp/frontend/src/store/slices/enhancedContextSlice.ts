/**
 * Enhanced Context Slice
 * Manages enhanced context intelligence state
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { apiService } from '../../services/apiService';

export interface EnhancedContext {
  id: string;
  primary_type: string;
  confidence: number;
  timestamp: string;
  active_applications: Array<{
    name: string;
    category: string;
    productivity_score: number;
    active_time: number;
  }>;
  current_meeting?: {
    meeting_id: string;
    title: string;
    participants: string[];
    start_time: string;
    end_time: string;
    meeting_type: string;
  };
  upcoming_meetings: Array<{
    meeting_id: string;
    title: string;
    start_time: string;
    context_relevance: number;
  }>;
  device_contexts: Array<{
    device_id: string;
    device_type: string;
    device_name: string;
    last_sync: string;
    sync_status: string;
  }>;
  ai_insights: {
    predicted_next?: string;
    stability?: number;
    interruption_likelihood?: number;
    recommendations?: string[];
    potential_switches?: string[];
  };
  predicted_next_context?: string;
  context_stability: number;
  interruption_likelihood: number;
}

export interface TemporalPatterns {
  patterns: Array<{
    pattern_id: string;
    context_type: string;
    frequency: number;
    confidence: number;
    next_predicted?: string;
  }>;
  matching_patterns: Array<any>;
  predictions: Array<{
    context_type: string;
    confidence: number;
    predicted_time: string;
  }>;
  pattern_confidence: number;
}

export interface ApplicationInsights {
  total_score: number;
  category_breakdown: Record<string, number>;
  focus_distribution: Record<string, number>;
}

export interface MeetingContext {
  current?: {
    meeting_id: string;
    title: string;
    participants: string[];
    context_relevance: number;
  };
  upcoming: Array<{
    meeting_id: string;
    title: string;
    start_time: string;
    preparation_needed: boolean;
  }>;
  preparation_needed: boolean;
  insights: {
    meeting_load: number;
    focus_time_available: number;
    preparation_urgency: string;
  };
}

export interface DeviceSync {
  device_contexts: Array<{
    device_id: string;
    device_type: string;
    device_name: string;
    platform: string;
    last_sync: string;
    sync_status: string;
  }>;
  continuity_analysis: {
    active_devices: number;
    primary_device?: string;
    context_switches: number;
    sync_health: string;
    recommendations: string[];
  };
}

export interface EnhancedContextState {
  enhancedContext: EnhancedContext | null;
  temporalPatterns: TemporalPatterns | null;
  applicationInsights: ApplicationInsights | null;
  meetingContext: MeetingContext | null;
  deviceSync: DeviceSync | null;
  isLoading: boolean;
  error: string | null;
  lastUpdated: string | null;
}

const initialState: EnhancedContextState = {
  enhancedContext: null,
  temporalPatterns: null,
  applicationInsights: null,
  meetingContext: null,
  deviceSync: null,
  isLoading: false,
  error: null,
  lastUpdated: null,
};

// Async thunks
export const fetchEnhancedContext = createAsyncThunk(
  'enhancedContext/fetchEnhancedContext',
  async (_, { rejectWithValue }) => {
    try {
      const response = await apiService.get('/api/context/enhanced');
      return response.data.enhanced_context;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch enhanced context');
    }
  }
);

export const fetchTemporalPatterns = createAsyncThunk(
  'enhancedContext/fetchTemporalPatterns',
  async (_, { rejectWithValue }) => {
    try {
      const response = await apiService.get('/api/context/temporal-patterns');
      return response.data.patterns;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch temporal patterns');
    }
  }
);

export const fetchApplicationInsights = createAsyncThunk(
  'enhancedContext/fetchApplicationInsights',
  async (_, { rejectWithValue }) => {
    try {
      const response = await apiService.get('/api/context/application-insights');
      return response.data.insights;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch application insights');
    }
  }
);

export const fetchMeetingContext = createAsyncThunk(
  'enhancedContext/fetchMeetingContext',
  async (_, { rejectWithValue }) => {
    try {
      const response = await apiService.get('/api/context/meeting-context');
      return response.data.meeting_context;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch meeting context');
    }
  }
);

export const fetchDeviceSync = createAsyncThunk(
  'enhancedContext/fetchDeviceSync',
  async (_, { rejectWithValue }) => {
    try {
      const response = await apiService.get('/api/context/device-sync');
      return {
        device_contexts: response.data.device_contexts,
        continuity_analysis: response.data.continuity_analysis
      };
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch device sync');
    }
  }
);

export const fetchAllContextData = createAsyncThunk(
  'enhancedContext/fetchAllContextData',
  async (_, { dispatch, rejectWithValue }) => {
    try {
      // Fetch all context data in parallel
      const results = await Promise.allSettled([
        dispatch(fetchEnhancedContext()).unwrap(),
        dispatch(fetchTemporalPatterns()).unwrap(),
        dispatch(fetchApplicationInsights()).unwrap(),
        dispatch(fetchMeetingContext()).unwrap(),
        dispatch(fetchDeviceSync()).unwrap(),
      ]);

      // Check if any failed
      const failures = results.filter(result => result.status === 'rejected');
      if (failures.length > 0) {
        console.warn('Some context data failed to load:', failures);
      }

      return true;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch context data');
    }
  }
);

// Enhanced Context slice
const enhancedContextSlice = createSlice({
  name: 'enhancedContext',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    
    updateContextConfidence: (state, action: PayloadAction<number>) => {
      if (state.enhancedContext) {
        state.enhancedContext.confidence = action.payload;
      }
    },
    
    addApplicationActivity: (state, action: PayloadAction<{
      name: string;
      category: string;
      activity_time: number;
    }>) => {
      if (state.enhancedContext) {
        const existingApp = state.enhancedContext.active_applications.find(
          app => app.name === action.payload.name
        );
        
        if (existingApp) {
          existingApp.active_time += action.payload.activity_time;
        } else {
          state.enhancedContext.active_applications.push({
            name: action.payload.name,
            category: action.payload.category,
            productivity_score: 0.5, // Default score
            active_time: action.payload.activity_time
          });
        }
      }
    },
    
    updateMeetingStatus: (state, action: PayloadAction<{
      meeting_id: string;
      status: 'started' | 'ended' | 'joined' | 'left';
    }>) => {
      if (state.meetingContext) {
        const { meeting_id, status } = action.payload;
        
        if (status === 'started' || status === 'joined') {
          // Move from upcoming to current
          const upcomingMeeting = state.meetingContext.upcoming.find(
            m => m.meeting_id === meeting_id
          );
          
          if (upcomingMeeting) {
            state.meetingContext.current = {
              meeting_id: upcomingMeeting.meeting_id,
              title: upcomingMeeting.title,
              participants: [],
              context_relevance: 0.8
            };
            
            state.meetingContext.upcoming = state.meetingContext.upcoming.filter(
              m => m.meeting_id !== meeting_id
            );
          }
        } else if (status === 'ended' || status === 'left') {
          // Clear current meeting
          if (state.meetingContext.current?.meeting_id === meeting_id) {
            state.meetingContext.current = undefined;
          }
        }
      }
    },
    
    updateDeviceSyncStatus: (state, action: PayloadAction<{
      device_id: string;
      sync_status: string;
      last_sync: string;
    }>) => {
      if (state.deviceSync) {
        const device = state.deviceSync.device_contexts.find(
          d => d.device_id === action.payload.device_id
        );
        
        if (device) {
          device.sync_status = action.payload.sync_status;
          device.last_sync = action.payload.last_sync;
        }
      }
    },
  },
  extraReducers: (builder) => {
    // Fetch Enhanced Context
    builder
      .addCase(fetchEnhancedContext.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchEnhancedContext.fulfilled, (state, action) => {
        state.isLoading = false;
        state.enhancedContext = action.payload;
        state.lastUpdated = new Date().toISOString();
      })
      .addCase(fetchEnhancedContext.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch Temporal Patterns
    builder
      .addCase(fetchTemporalPatterns.fulfilled, (state, action) => {
        state.temporalPatterns = action.payload;
      })
      .addCase(fetchTemporalPatterns.rejected, (state, action) => {
        state.error = action.payload as string;
      });

    // Fetch Application Insights
    builder
      .addCase(fetchApplicationInsights.fulfilled, (state, action) => {
        state.applicationInsights = action.payload;
      })
      .addCase(fetchApplicationInsights.rejected, (state, action) => {
        state.error = action.payload as string;
      });

    // Fetch Meeting Context
    builder
      .addCase(fetchMeetingContext.fulfilled, (state, action) => {
        state.meetingContext = action.payload;
      })
      .addCase(fetchMeetingContext.rejected, (state, action) => {
        state.error = action.payload as string;
      });

    // Fetch Device Sync
    builder
      .addCase(fetchDeviceSync.fulfilled, (state, action) => {
        state.deviceSync = action.payload;
      })
      .addCase(fetchDeviceSync.rejected, (state, action) => {
        state.error = action.payload as string;
      });

    // Fetch All Context Data
    builder
      .addCase(fetchAllContextData.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchAllContextData.fulfilled, (state) => {
        state.isLoading = false;
        state.lastUpdated = new Date().toISOString();
      })
      .addCase(fetchAllContextData.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  clearError,
  updateContextConfidence,
  addApplicationActivity,
  updateMeetingStatus,
  updateDeviceSyncStatus,
} = enhancedContextSlice.actions;

export default enhancedContextSlice.reducer;
