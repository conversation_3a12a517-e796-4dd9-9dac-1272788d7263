/**
 * Collaboration Slice
 * Manages real-time collaboration state
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { apiService } from '../../services/apiService';

export interface Participant {
  user_id: string;
  session_id: string;
  name: string;
  role: 'owner' | 'collaborator' | 'viewer';
  joined_at: string;
  last_activity: string;
  cursor_position?: {
    x: number;
    y: number;
    element?: string;
  };
  is_speaking: boolean;
  is_screen_sharing: boolean;
  media_state: {
    audio: boolean;
    video: boolean;
    screen: boolean;
  };
  context_data: Record<string, any>;
}

export interface SharedWorkspace {
  workspace_id: string;
  name: string;
  owner_id: string;
  created_at: string;
  last_modified: string;
  participants: Record<string, Participant>;
  content: Record<string, any>;
  ai_context: {
    enabled: boolean;
    model: string;
    context_sharing: boolean;
  };
  settings: {
    max_participants: number;
    allow_voice: boolean;
    allow_video: boolean;
    allow_screen_share: boolean;
    auto_save: boolean;
    version_history: boolean;
  };
  version: number;
}

export interface CollaborationEvent {
  event_id: string;
  event_type: string;
  workspace_id: string;
  user_id: string;
  timestamp: string;
  data: Record<string, any>;
  version: number;
}

export interface AICollaborationSession {
  session_id: string;
  workspace_id: string;
  participants: string[];
  conversation_history: Array<{
    user_id: string;
    message: string;
    response: string;
    timestamp: string;
  }>;
  active_requests: Record<string, any>;
}

export interface CollaborationState {
  currentWorkspace: SharedWorkspace | null;
  participants: Participant[];
  events: CollaborationEvent[];
  aiSession: AICollaborationSession | null;
  isConnected: boolean;
  connectionStatus: 'disconnected' | 'connecting' | 'connected' | 'error';
  isLoading: boolean;
  error: string | null;
  
  // Real-time state
  cursors: Record<string, { x: number; y: number; user_name: string }>;
  activeEditors: Record<string, string>; // element_id -> user_id
  voiceActivity: Record<string, boolean>; // user_id -> is_speaking
  screenShares: Record<string, any>; // user_id -> screen_share_info
}

const initialState: CollaborationState = {
  currentWorkspace: null,
  participants: [],
  events: [],
  aiSession: null,
  isConnected: false,
  connectionStatus: 'disconnected',
  isLoading: false,
  error: null,
  cursors: {},
  activeEditors: {},
  voiceActivity: {},
  screenShares: {},
};

// Async thunks
export const createWorkspace = createAsyncThunk(
  'collaboration/createWorkspace',
  async (workspaceData: {
    name: string;
    description?: string;
    settings?: Record<string, any>;
  }, { rejectWithValue }) => {
    try {
      const response = await apiService.post('/api/collaboration/workspaces', workspaceData);
      return response.data.workspace;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to create workspace');
    }
  }
);

export const joinWorkspace = createAsyncThunk(
  'collaboration/joinWorkspace',
  async (joinData: {
    workspaceId: string;
    userData: {
      name: string;
      role?: string;
      context?: Record<string, any>;
    };
  }, { rejectWithValue }) => {
    try {
      const response = await apiService.post(
        `/api/collaboration/workspaces/${joinData.workspaceId}/join`,
        joinData.userData
      );
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to join workspace');
    }
  }
);

export const leaveWorkspace = createAsyncThunk(
  'collaboration/leaveWorkspace',
  async (workspaceId: string, { rejectWithValue }) => {
    try {
      await apiService.post(`/api/collaboration/workspaces/${workspaceId}/leave`);
      return workspaceId;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to leave workspace');
    }
  }
);

export const updateWorkspaceContent = createAsyncThunk(
  'collaboration/updateWorkspaceContent',
  async (updateData: {
    workspaceId?: string;
    content: Record<string, any>;
    operation?: Record<string, any>;
  }, { getState, rejectWithValue }) => {
    try {
      const state = getState() as { collaboration: CollaborationState };
      const workspaceId = updateData.workspaceId || state.collaboration.currentWorkspace?.workspace_id;
      
      if (!workspaceId) {
        throw new Error('No workspace ID available');
      }

      const response = await apiService.patch(
        `/api/collaboration/workspaces/${workspaceId}/content`,
        {
          content: updateData.content,
          operation: updateData.operation
        }
      );
      
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to update workspace content');
    }
  }
);

export const sendAIRequest = createAsyncThunk(
  'collaboration/sendAIRequest',
  async (requestData: {
    workspaceId?: string;
    message: string;
    context?: Record<string, any>;
  }, { getState, rejectWithValue }) => {
    try {
      const state = getState() as { collaboration: CollaborationState };
      const workspaceId = requestData.workspaceId || state.collaboration.currentWorkspace?.workspace_id;
      
      if (!workspaceId) {
        throw new Error('No workspace ID available');
      }

      const response = await apiService.post(
        `/api/collaboration/workspaces/${workspaceId}/ai-request`,
        {
          message: requestData.message,
          context: requestData.context
        }
      );
      
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to send AI request');
    }
  }
);

export const fetchWorkspaceHistory = createAsyncThunk(
  'collaboration/fetchWorkspaceHistory',
  async (workspaceId: string, { rejectWithValue }) => {
    try {
      const response = await apiService.get(`/api/collaboration/workspaces/${workspaceId}/history`);
      return response.data.events;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch workspace history');
    }
  }
);

// Collaboration slice
const collaborationSlice = createSlice({
  name: 'collaboration',
  initialState,
  reducers: {
    setConnectionStatus: (state, action: PayloadAction<CollaborationState['connectionStatus']>) => {
      state.connectionStatus = action.payload;
      state.isConnected = action.payload === 'connected';
    },

    clearError: (state) => {
      state.error = null;
    },

    addCollaborationEvent: (state, action: PayloadAction<CollaborationEvent>) => {
      const event = action.payload;
      
      // Add to events list
      state.events.push(event);
      
      // Keep only last 100 events
      if (state.events.length > 100) {
        state.events = state.events.slice(-100);
      }
      
      // Handle specific event types
      switch (event.event_type) {
        case 'user_joined':
          const newParticipant = event.data.participant;
          const existingIndex = state.participants.findIndex(p => p.user_id === newParticipant.user_id);
          
          if (existingIndex >= 0) {
            state.participants[existingIndex] = newParticipant;
          } else {
            state.participants.push(newParticipant);
          }
          break;

        case 'user_left':
          state.participants = state.participants.filter(p => p.user_id !== event.user_id);
          
          // Clean up user-specific state
          delete state.cursors[event.user_id];
          delete state.voiceActivity[event.user_id];
          delete state.screenShares[event.user_id];
          
          // Remove from active editors
          Object.keys(state.activeEditors).forEach(elementId => {
            if (state.activeEditors[elementId] === event.user_id) {
              delete state.activeEditors[elementId];
            }
          });
          break;

        case 'cursor_move':
          if (event.data.position) {
            state.cursors[event.user_id] = {
              x: event.data.position.x,
              y: event.data.position.y,
              user_name: event.data.user_name
            };
          }
          break;

        case 'text_edit':
          // Update workspace content and version
          if (state.currentWorkspace) {
            state.currentWorkspace.version = event.version;
            state.currentWorkspace.last_modified = event.timestamp;
          }
          break;

        case 'voice_start':
          state.voiceActivity[event.user_id] = true;
          break;

        case 'voice_end':
          state.voiceActivity[event.user_id] = false;
          break;

        case 'screen_share':
          if (event.data.started) {
            state.screenShares[event.user_id] = event.data.screen_info;
          } else {
            delete state.screenShares[event.user_id];
          }
          break;

        case 'ai_response':
          // Add to AI session history
          if (state.aiSession) {
            state.aiSession.conversation_history.push({
              user_id: event.data.original_user,
              message: '', // Would need to track the original message
              response: event.data.response,
              timestamp: event.timestamp
            });
          }
          break;
      }
    },

    updateCursorPosition: (state, action: PayloadAction<{
      user_id: string;
      position: { x: number; y: number };
      user_name: string;
    }>) => {
      const { user_id, position, user_name } = action.payload;
      state.cursors[user_id] = { ...position, user_name };
    },

    setActiveEditor: (state, action: PayloadAction<{
      element_id: string;
      user_id: string;
    }>) => {
      const { element_id, user_id } = action.payload;
      state.activeEditors[element_id] = user_id;
    },

    clearActiveEditor: (state, action: PayloadAction<string>) => {
      const element_id = action.payload;
      delete state.activeEditors[element_id];
    },

    updateMediaState: (state, action: PayloadAction<{
      user_id: string;
      media_state: {
        audio?: boolean;
        video?: boolean;
        screen?: boolean;
      };
    }>) => {
      const { user_id, media_state } = action.payload;
      const participant = state.participants.find(p => p.user_id === user_id);
      
      if (participant) {
        participant.media_state = { ...participant.media_state, ...media_state };
      }
    },

    setWorkspaceContent: (state, action: PayloadAction<Record<string, any>>) => {
      if (state.currentWorkspace) {
        state.currentWorkspace.content = action.payload;
      }
    },

    incrementWorkspaceVersion: (state) => {
      if (state.currentWorkspace) {
        state.currentWorkspace.version += 1;
        state.currentWorkspace.last_modified = new Date().toISOString();
      }
    },

    resetCollaborationState: (state) => {
      state.currentWorkspace = null;
      state.participants = [];
      state.events = [];
      state.aiSession = null;
      state.isConnected = false;
      state.connectionStatus = 'disconnected';
      state.cursors = {};
      state.activeEditors = {};
      state.voiceActivity = {};
      state.screenShares = {};
    },
  },

  extraReducers: (builder) => {
    // Create Workspace
    builder
      .addCase(createWorkspace.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createWorkspace.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentWorkspace = action.payload;
      })
      .addCase(createWorkspace.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Join Workspace
    builder
      .addCase(joinWorkspace.pending, (state) => {
        state.isLoading = true;
        state.error = null;
        state.connectionStatus = 'connecting';
      })
      .addCase(joinWorkspace.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentWorkspace = action.payload.workspace;
        state.participants = Object.values(action.payload.workspace.participants);
        state.connectionStatus = 'connected';
        state.isConnected = true;
      })
      .addCase(joinWorkspace.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
        state.connectionStatus = 'error';
      });

    // Leave Workspace
    builder
      .addCase(leaveWorkspace.fulfilled, (state) => {
        state.currentWorkspace = null;
        state.participants = [];
        state.events = [];
        state.isConnected = false;
        state.connectionStatus = 'disconnected';
        state.cursors = {};
        state.activeEditors = {};
        state.voiceActivity = {};
        state.screenShares = {};
      });

    // Update Workspace Content
    builder
      .addCase(updateWorkspaceContent.fulfilled, (state, action) => {
        if (state.currentWorkspace) {
          state.currentWorkspace.content = action.payload.content;
          state.currentWorkspace.version = action.payload.version;
          state.currentWorkspace.last_modified = action.payload.last_modified;
        }
      });

    // Send AI Request
    builder
      .addCase(sendAIRequest.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(sendAIRequest.fulfilled, (state, action) => {
        state.isLoading = false;
        // AI response will come through WebSocket events
      })
      .addCase(sendAIRequest.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch Workspace History
    builder
      .addCase(fetchWorkspaceHistory.fulfilled, (state, action) => {
        state.events = action.payload;
      });
  },
});

export const {
  setConnectionStatus,
  clearError,
  addCollaborationEvent,
  updateCursorPosition,
  setActiveEditor,
  clearActiveEditor,
  updateMediaState,
  setWorkspaceContent,
  incrementWorkspaceVersion,
  resetCollaborationState,
} = collaborationSlice.actions;

// Convenience action for sending collaboration events
export const sendCollaborationEvent = (event: CollaborationEvent) => (dispatch: any) => {
  dispatch(addCollaborationEvent(event));
};

export default collaborationSlice.reducer;
