/**
 * Context Manager Component
 * Allows users to view, create, and switch between contexts
 */

import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../store/store';
import {
  fetchUserContexts,
  createContext,
  setCurrentContext,
  createQuickContext,
  Context,
} from '../store/slices/contextSlice';
import useContextDetection from '../hooks/useContextDetection';

interface ContextManagerProps {
  onClose?: () => void;
}

const ContextManager: React.FC<ContextManagerProps> = ({ onClose }) => {
  const dispatch = useDispatch();
  const { contexts, currentContext, isLoading } = useSelector((state: RootState) => state.context);
  const { userId } = useSelector((state: RootState) => state.user);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newContextName, setNewContextName] = useState('');
  const [newContextType, setNewContextType] = useState<'work' | 'personal' | 'project' | 'meeting'>('work');

  const { lastDetectedContext, detectNow } = useContextDetection({
    autoStart: false,
    sendToBackend: false,
  });

  useEffect(() => {
    if (userId) {
      dispatch(fetchUserContexts(userId));
    }
  }, [dispatch, userId]);

  const handleCreateContext = async () => {
    if (!newContextName.trim() || !userId) return;

    // Get current detected context for initial state
    const detectedContext = await detectNow();

    const contextState = {
      activeApplications: detectedContext.application.activeApplication ? 
        [detectedContext.application.activeApplication] : [],
      currentProject: detectedContext.work.currentProject,
      browserTabs: detectedContext.browser.url ? [{
        title: detectedContext.browser.title || '',
        url: detectedContext.browser.url,
      }] : [],
      lastInteraction: new Date().toISOString(),
    };

    dispatch(createContext({
      userId,
      name: newContextName.trim(),
      state: contextState,
      metadata: {
        category: newContextType,
        priority: 'medium',
      },
    }));

    setNewContextName('');
    setShowCreateForm(false);
  };

  const handleQuickCreate = (type: 'work' | 'personal' | 'project' | 'meeting') => {
    if (!userId) return;

    const name = `${type.charAt(0).toUpperCase() + type.slice(1)} - ${new Date().toLocaleTimeString()}`;
    dispatch(createQuickContext({
      name,
      type,
      userId,
    }));
  };

  const handleSwitchContext = (context: Context) => {
    dispatch(setCurrentContext(context));
  };

  const formatLastInteraction = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    return `${diffDays}d ago`;
  };

  return (
    <div className="context-manager">
      <div className="context-manager-header">
        <h3>Context Manager</h3>
        {onClose && (
          <button className="close-btn" onClick={onClose}>×</button>
        )}
      </div>

      {/* Current Context */}
      {currentContext && (
        <div className="current-context-card">
          <h4>📂 Current Context</h4>
          <div className="context-details">
            <div className="context-name">{currentContext.name}</div>
            <div className="context-meta">
              <span className="context-category">{currentContext.metadata.category}</span>
              <span className="context-time">
                {formatLastInteraction(currentContext.state.lastInteraction || currentContext.metadata.updatedAt)}
              </span>
            </div>
            {currentContext.state.currentProject && (
              <div className="context-project">🎯 {currentContext.state.currentProject}</div>
            )}
            {currentContext.state.activeApplications && currentContext.state.activeApplications.length > 0 && (
              <div className="context-apps">
                🖥️ {currentContext.state.activeApplications.join(', ')}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Quick Actions */}
      <div className="quick-actions-section">
        <h4>Quick Create</h4>
        <div className="quick-actions-grid">
          <button 
            className="quick-action-btn work"
            onClick={() => handleQuickCreate('work')}
          >
            💼 Work
          </button>
          <button 
            className="quick-action-btn personal"
            onClick={() => handleQuickCreate('personal')}
          >
            🏠 Personal
          </button>
          <button 
            className="quick-action-btn project"
            onClick={() => handleQuickCreate('project')}
          >
            🚀 Project
          </button>
          <button 
            className="quick-action-btn meeting"
            onClick={() => handleQuickCreate('meeting')}
          >
            📅 Meeting
          </button>
        </div>
      </div>

      {/* Create New Context */}
      <div className="create-context-section">
        {!showCreateForm ? (
          <button 
            className="create-context-btn"
            onClick={() => setShowCreateForm(true)}
          >
            ➕ Create New Context
          </button>
        ) : (
          <div className="create-form">
            <input
              type="text"
              placeholder="Context name..."
              value={newContextName}
              onChange={(e) => setNewContextName(e.target.value)}
              className="context-name-input"
              autoFocus
            />
            <select
              value={newContextType}
              onChange={(e) => setNewContextType(e.target.value as any)}
              className="context-type-select"
            >
              <option value="work">Work</option>
              <option value="personal">Personal</option>
              <option value="project">Project</option>
              <option value="meeting">Meeting</option>
            </select>
            <div className="form-actions">
              <button 
                className="create-btn"
                onClick={handleCreateContext}
                disabled={!newContextName.trim()}
              >
                Create
              </button>
              <button 
                className="cancel-btn"
                onClick={() => {
                  setShowCreateForm(false);
                  setNewContextName('');
                }}
              >
                Cancel
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Context List */}
      <div className="contexts-list">
        <h4>Available Contexts ({contexts.length})</h4>
        {isLoading ? (
          <div className="loading">Loading contexts...</div>
        ) : contexts.length === 0 ? (
          <div className="empty-state">
            <p>No contexts yet. Create your first context above!</p>
          </div>
        ) : (
          <div className="contexts-grid">
            {contexts.map((context) => (
              <div
                key={context.id}
                className={`context-card ${currentContext?.id === context.id ? 'active' : ''}`}
                onClick={() => handleSwitchContext(context)}
              >
                <div className="context-card-header">
                  <span className="context-card-name">{context.name}</span>
                  <span className="context-card-category">{context.metadata.category}</span>
                </div>
                <div className="context-card-details">
                  {context.state.currentProject && (
                    <div className="context-card-project">🎯 {context.state.currentProject}</div>
                  )}
                  <div className="context-card-time">
                    {formatLastInteraction(context.state.lastInteraction || context.metadata.updatedAt)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Detected Context Info */}
      {lastDetectedContext && (
        <div className="detected-context-info">
          <h4>🔍 Current Detection</h4>
          <div className="detection-details">
            <div>📱 App: {lastDetectedContext.application.activeApplication}</div>
            <div>🎯 Project: {lastDetectedContext.work.currentProject || 'Unknown'}</div>
            <div>🌐 Domain: {lastDetectedContext.browser.domain || 'N/A'}</div>
            <div>📊 Confidence: {Math.round(lastDetectedContext.confidence * 100)}%</div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ContextManager;
