/**
 * Node Palette Component
 * Sidebar with available workflow nodes that can be dragged to canvas
 */

import React, { useState, useMemo } from 'react';
import { useDrag } from 'react-dnd';
import NodeIcon from './NodeIcon';
import './NodePalette.css';

interface Position {
  x: number;
  y: number;
}

interface NodePaletteProps {
  onNodeDrop: (nodeType: string, position: Position) => void;
}

interface NodeTemplate {
  type: string;
  name: string;
  description: string;
  category: string;
  icon: string;
  color: string;
  parameters: Record<string, any>;
}

const NODE_TEMPLATES: NodeTemplate[] = [
  // Triggers
  {
    type: 'trigger_manual',
    name: 'Manual Trigger',
    description: 'Start workflow manually',
    category: 'Triggers',
    icon: 'play_arrow',
    color: '#4CAF50',
    parameters: {}
  },
  {
    type: 'trigger_context_change',
    name: 'Context Change',
    description: 'Trigger when context changes',
    category: 'Triggers',
    icon: 'layers',
    color: '#2196F3',
    parameters: {
      context_type: 'work',
      conditions: {}
    }
  },
  {
    type: 'trigger_time_based',
    name: 'Schedule',
    description: 'Trigger at specific times',
    category: 'Triggers',
    icon: 'schedule',
    color: '#FF9800',
    parameters: {
      schedule: '0 9 * * *',
      timezone: 'UTC'
    }
  },
  {
    type: 'trigger_email_received',
    name: 'Email Received',
    description: 'Trigger when email is received',
    category: 'Triggers',
    icon: 'email',
    color: '#9C27B0',
    parameters: {
      from: '',
      subject_contains: '',
      folder: 'INBOX'
    }
  },

  // Actions
  {
    type: 'action_send_email',
    name: 'Send Email',
    description: 'Send an email message',
    category: 'Actions',
    icon: 'send',
    color: '#F44336',
    parameters: {
      to: '',
      subject: '',
      body: '',
      template: ''
    }
  },
  {
    type: 'action_ai_response',
    name: 'AI Response',
    description: 'Get AI-generated response',
    category: 'Actions',
    icon: 'psychology',
    color: '#673AB7',
    parameters: {
      prompt: '',
      model: 'gpt-4',
      temperature: 0.7
    }
  },
  {
    type: 'action_create_calendar_event',
    name: 'Create Event',
    description: 'Create calendar event',
    category: 'Actions',
    icon: 'event',
    color: '#009688',
    parameters: {
      title: '',
      start_time: '',
      duration: 60,
      attendees: []
    }
  },
  {
    type: 'action_notification',
    name: 'Send Notification',
    description: 'Send push notification',
    category: 'Actions',
    icon: 'notifications',
    color: '#FF5722',
    parameters: {
      title: '',
      message: '',
      priority: 'normal'
    }
  },
  {
    type: 'action_api_call',
    name: 'API Call',
    description: 'Make HTTP API request',
    category: 'Actions',
    icon: 'api',
    color: '#607D8B',
    parameters: {
      url: '',
      method: 'GET',
      headers: {},
      body: ''
    }
  },

  // Logic
  {
    type: 'logic_condition',
    name: 'Condition',
    description: 'Conditional branching',
    category: 'Logic',
    icon: 'alt_route',
    color: '#795548',
    parameters: {
      condition: '',
      operator: 'equals',
      value: ''
    }
  },
  {
    type: 'logic_delay',
    name: 'Delay',
    description: 'Wait for specified time',
    category: 'Logic',
    icon: 'timer',
    color: '#9E9E9E',
    parameters: {
      duration: 5,
      unit: 'seconds'
    }
  },
  {
    type: 'logic_loop',
    name: 'Loop',
    description: 'Repeat actions',
    category: 'Logic',
    icon: 'loop',
    color: '#3F51B5',
    parameters: {
      type: 'count',
      count: 3,
      condition: ''
    }
  },

  // Data
  {
    type: 'data_transform',
    name: 'Transform Data',
    description: 'Transform data format',
    category: 'Data',
    icon: 'transform',
    color: '#E91E63',
    parameters: {
      input_format: 'json',
      output_format: 'json',
      transformation: ''
    }
  },
  {
    type: 'data_filter',
    name: 'Filter Data',
    description: 'Filter data based on criteria',
    category: 'Data',
    icon: 'filter_list',
    color: '#00BCD4',
    parameters: {
      criteria: '',
      operator: 'contains'
    }
  }
];

const CATEGORIES = ['Triggers', 'Actions', 'Logic', 'Data'];

interface DraggableNodeProps {
  template: NodeTemplate;
  onDrop: (nodeType: string, position: Position) => void;
}

const DraggableNode: React.FC<DraggableNodeProps> = ({ template, onDrop }) => {
  const [{ isDragging }, drag] = useDrag({
    type: 'palette-node',
    item: { nodeType: template.type },
    end: (item, monitor) => {
      const dropResult = monitor.getDropResult();
      if (item && dropResult) {
        const clientOffset = monitor.getClientOffset();
        if (clientOffset) {
          onDrop(template.type, clientOffset);
        }
      }
    },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  return (
    <div
      ref={drag}
      className={`palette-node ${isDragging ? 'dragging' : ''}`}
      style={{ borderLeftColor: template.color }}
    >
      <div className="palette-node-icon">
        <NodeIcon type={template.type} color={template.color} />
      </div>
      <div className="palette-node-content">
        <div className="palette-node-name">{template.name}</div>
        <div className="palette-node-description">{template.description}</div>
      </div>
    </div>
  );
};

const NodePalette: React.FC<NodePaletteProps> = ({ onNodeDrop }) => {
  const [selectedCategory, setSelectedCategory] = useState<string>('All');
  const [searchTerm, setSearchTerm] = useState('');
  const [isCollapsed, setIsCollapsed] = useState(false);

  const filteredNodes = useMemo(() => {
    let filtered = NODE_TEMPLATES;

    // Filter by category
    if (selectedCategory !== 'All') {
      filtered = filtered.filter(node => node.category === selectedCategory);
    }

    // Filter by search term
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(node =>
        node.name.toLowerCase().includes(term) ||
        node.description.toLowerCase().includes(term) ||
        node.type.toLowerCase().includes(term)
      );
    }

    return filtered;
  }, [selectedCategory, searchTerm]);

  const nodesByCategory = useMemo(() => {
    const grouped: Record<string, NodeTemplate[]> = {};
    
    filteredNodes.forEach(node => {
      if (!grouped[node.category]) {
        grouped[node.category] = [];
      }
      grouped[node.category].push(node);
    });

    return grouped;
  }, [filteredNodes]);

  return (
    <div className={`node-palette ${isCollapsed ? 'collapsed' : ''}`}>
      <div className="palette-header">
        <h3>Workflow Nodes</h3>
        <button
          className="collapse-btn"
          onClick={() => setIsCollapsed(!isCollapsed)}
          title={isCollapsed ? 'Expand palette' : 'Collapse palette'}
        >
          {isCollapsed ? '▶' : '◀'}
        </button>
      </div>

      {!isCollapsed && (
        <>
          {/* Search */}
          <div className="palette-search">
            <input
              type="text"
              placeholder="Search nodes..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>

          {/* Category filter */}
          <div className="palette-categories">
            <button
              className={`category-btn ${selectedCategory === 'All' ? 'active' : ''}`}
              onClick={() => setSelectedCategory('All')}
            >
              All
            </button>
            {CATEGORIES.map(category => (
              <button
                key={category}
                className={`category-btn ${selectedCategory === category ? 'active' : ''}`}
                onClick={() => setSelectedCategory(category)}
              >
                {category}
              </button>
            ))}
          </div>

          {/* Nodes */}
          <div className="palette-content">
            {selectedCategory === 'All' ? (
              // Show nodes grouped by category
              Object.entries(nodesByCategory).map(([category, nodes]) => (
                <div key={category} className="palette-category-group">
                  <div className="category-header">{category}</div>
                  {nodes.map(template => (
                    <DraggableNode
                      key={template.type}
                      template={template}
                      onDrop={onNodeDrop}
                    />
                  ))}
                </div>
              ))
            ) : (
              // Show filtered nodes
              filteredNodes.map(template => (
                <DraggableNode
                  key={template.type}
                  template={template}
                  onDrop={onNodeDrop}
                />
              ))
            )}

            {filteredNodes.length === 0 && (
              <div className="no-nodes-message">
                No nodes found matching your criteria.
              </div>
            )}
          </div>

          {/* AI Suggestions */}
          <div className="palette-ai-suggestions">
            <div className="suggestions-header">
              <span>💡 AI Suggestions</span>
            </div>
            <div className="suggestions-content">
              <div className="suggestion-item">
                Based on your context, consider adding an email trigger
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default NodePalette;
