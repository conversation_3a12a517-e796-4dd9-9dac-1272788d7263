/**
 * Workflow Canvas Component
 * Visual drag-and-drop workflow builder interface
 */

import React, { useState, useRef, useCallback, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { RootState } from '../../store/store';
import { 
  addNode, 
  updateNode, 
  deleteNode, 
  connectNodes, 
  disconnectNodes,
  setCanvasPosition,
  setZoomLevel
} from '../../store/slices/workflowSlice';
import WorkflowNode from './WorkflowNode';
import NodePalette from './NodePalette';
import ConnectionLine from './ConnectionLine';
import CanvasControls from './CanvasControls';
import './WorkflowCanvas.css';

interface Position {
  x: number;
  y: number;
}

interface WorkflowCanvasProps {
  workflowId: string;
  readOnly?: boolean;
  onSave?: () => void;
}

const WorkflowCanvas: React.FC<WorkflowCanvasProps> = ({
  workflowId,
  readOnly = false,
  onSave
}) => {
  const dispatch = useDispatch();
  const canvasRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState<Position>({ x: 0, y: 0 });
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionStart, setConnectionStart] = useState<string | null>(null);
  const [mousePosition, setMousePosition] = useState<Position>({ x: 0, y: 0 });

  const {
    currentWorkflow,
    canvasPosition,
    zoomLevel,
    selectedNodes,
    isExecuting
  } = useSelector((state: RootState) => state.workflows);

  const nodes = currentWorkflow?.nodes || [];
  const connections = currentWorkflow?.connections || [];

  // Handle canvas panning
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (e.target === canvasRef.current) {
      setIsDragging(true);
      setDragStart({ x: e.clientX, y: e.clientY });
    }
  }, []);

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    setMousePosition({ x: e.clientX, y: e.clientY });

    if (isDragging) {
      const deltaX = e.clientX - dragStart.x;
      const deltaY = e.clientY - dragStart.y;
      
      dispatch(setCanvasPosition({
        x: canvasPosition.x + deltaX,
        y: canvasPosition.y + deltaY
      }));
      
      setDragStart({ x: e.clientX, y: e.clientY });
    }
  }, [isDragging, dragStart, canvasPosition, dispatch]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
    if (isConnecting) {
      setIsConnecting(false);
      setConnectionStart(null);
    }
  }, [isConnecting]);

  // Handle node operations
  const handleNodeMove = useCallback((nodeId: string, position: Position) => {
    dispatch(updateNode({
      id: nodeId,
      updates: { position }
    }));
  }, [dispatch]);

  const handleNodeSelect = useCallback((nodeId: string, multiSelect: boolean = false) => {
    // Implementation for node selection
  }, []);

  const handleNodeDelete = useCallback((nodeId: string) => {
    if (!readOnly) {
      dispatch(deleteNode(nodeId));
    }
  }, [dispatch, readOnly]);

  // Handle connections
  const handleConnectionStart = useCallback((nodeId: string) => {
    if (!readOnly) {
      setIsConnecting(true);
      setConnectionStart(nodeId);
    }
  }, [readOnly]);

  const handleConnectionEnd = useCallback((nodeId: string) => {
    if (isConnecting && connectionStart && connectionStart !== nodeId) {
      dispatch(connectNodes({
        sourceId: connectionStart,
        targetId: nodeId
      }));
    }
    setIsConnecting(false);
    setConnectionStart(null);
  }, [isConnecting, connectionStart, dispatch]);

  // Handle node drop from palette
  const handleNodeDrop = useCallback((nodeType: string, position: Position) => {
    if (!readOnly) {
      const canvasRect = canvasRef.current?.getBoundingClientRect();
      if (canvasRect) {
        const relativePosition = {
          x: (position.x - canvasRect.left - canvasPosition.x) / zoomLevel,
          y: (position.y - canvasRect.top - canvasPosition.y) / zoomLevel
        };

        dispatch(addNode({
          type: nodeType,
          position: relativePosition
        }));
      }
    }
  }, [readOnly, canvasPosition, zoomLevel, dispatch]);

  // Zoom handling
  const handleWheel = useCallback((e: React.WheelEvent) => {
    e.preventDefault();
    const delta = e.deltaY > 0 ? -0.1 : 0.1;
    const newZoom = Math.max(0.1, Math.min(2, zoomLevel + delta));
    dispatch(setZoomLevel(newZoom));
  }, [zoomLevel, dispatch]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case 's':
            e.preventDefault();
            onSave?.();
            break;
          case 'z':
            e.preventDefault();
            // Implement undo
            break;
          case 'y':
            e.preventDefault();
            // Implement redo
            break;
        }
      }
      
      if (e.key === 'Delete' && selectedNodes.length > 0) {
        selectedNodes.forEach(nodeId => handleNodeDelete(nodeId));
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [selectedNodes, handleNodeDelete, onSave]);

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="workflow-canvas-container">
        {!readOnly && <NodePalette onNodeDrop={handleNodeDrop} />}
        
        <div className="workflow-canvas-wrapper">
          <div
            ref={canvasRef}
            className={`workflow-canvas ${isDragging ? 'dragging' : ''} ${isExecuting ? 'executing' : ''}`}
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onWheel={handleWheel}
            style={{
              transform: `translate(${canvasPosition.x}px, ${canvasPosition.y}px) scale(${zoomLevel})`
            }}
          >
            {/* Grid background */}
            <div className="canvas-grid" />
            
            {/* Connection lines */}
            <svg className="connections-layer">
              {connections.map(connection => (
                <ConnectionLine
                  key={`${connection.sourceId}-${connection.targetId}`}
                  connection={connection}
                  nodes={nodes}
                  isActive={connection.sourceId === connectionStart}
                />
              ))}
              
              {/* Temporary connection line while connecting */}
              {isConnecting && connectionStart && (
                <ConnectionLine
                  connection={{
                    sourceId: connectionStart,
                    targetId: 'temp',
                    type: 'default'
                  }}
                  nodes={[
                    ...nodes,
                    {
                      id: 'temp',
                      position: {
                        x: (mousePosition.x - canvasPosition.x) / zoomLevel,
                        y: (mousePosition.y - canvasPosition.y) / zoomLevel
                      }
                    }
                  ]}
                  isTemporary
                />
              )}
            </svg>
            
            {/* Workflow nodes */}
            {nodes.map(node => (
              <WorkflowNode
                key={node.id}
                node={node}
                isSelected={selectedNodes.includes(node.id)}
                isConnecting={isConnecting}
                readOnly={readOnly}
                onMove={handleNodeMove}
                onSelect={handleNodeSelect}
                onDelete={handleNodeDelete}
                onConnectionStart={handleConnectionStart}
                onConnectionEnd={handleConnectionEnd}
              />
            ))}
          </div>
          
          <CanvasControls
            zoomLevel={zoomLevel}
            onZoomIn={() => dispatch(setZoomLevel(Math.min(2, zoomLevel + 0.1)))}
            onZoomOut={() => dispatch(setZoomLevel(Math.max(0.1, zoomLevel - 0.1)))}
            onZoomReset={() => dispatch(setZoomLevel(1))}
            onFitToScreen={() => {
              // Implementation for fit to screen
            }}
            onSave={onSave}
            readOnly={readOnly}
          />
        </div>
      </div>
    </DndProvider>
  );
};

export default WorkflowCanvas;
