/* Workflow Canvas Styles */

.workflow-canvas-container {
  display: flex;
  height: 100vh;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  overflow: hidden;
}

.workflow-canvas-wrapper {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.workflow-canvas {
  width: 100%;
  height: 100%;
  position: relative;
  cursor: grab;
  transform-origin: 0 0;
  transition: transform 0.1s ease-out;
}

.workflow-canvas.dragging {
  cursor: grabbing;
}

.workflow-canvas.executing {
  pointer-events: none;
}

.canvas-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
  pointer-events: none;
  z-index: 0;
}

.connections-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

/* Workflow Node Styles */
.workflow-node {
  position: absolute;
  min-width: 200px;
  max-width: 300px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  cursor: move;
  user-select: none;
  z-index: 10;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.workflow-node:hover {
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.4);
  transform: translateY(-2px);
}

.workflow-node.selected {
  border-color: #4CAF50;
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.3);
}

.workflow-node.dragging {
  opacity: 0.8;
  transform: rotate(2deg);
}

.workflow-node.hovered {
  border-color: #2196F3;
}

/* Node Type Styles */
.workflow-node.node-type-trigger-manual {
  border-left: 4px solid #4CAF50;
}

.workflow-node.node-type-trigger-context-change {
  border-left: 4px solid #2196F3;
}

.workflow-node.node-type-trigger-time-based {
  border-left: 4px solid #FF9800;
}

.workflow-node.node-type-action-send-email {
  border-left: 4px solid #F44336;
}

.workflow-node.node-type-action-ai-response {
  border-left: 4px solid #673AB7;
}

.workflow-node.node-type-logic-condition {
  border-left: 4px solid #795548;
}

/* Node Status Styles */
.workflow-node.status-running {
  animation: pulse 2s infinite;
}

.workflow-node.status-completed {
  border-color: #4CAF50;
}

.workflow-node.status-error {
  border-color: #F44336;
  background: rgba(244, 67, 54, 0.1);
}

@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7); }
  70% { box-shadow: 0 0 0 10px rgba(76, 175, 80, 0); }
  100% { box-shadow: 0 0 0 0 rgba(76, 175, 80, 0); }
}

/* Node Header */
.node-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 12px 12px 0 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.node-title {
  flex: 1;
  font-weight: 600;
  font-size: 14px;
  color: #333;
  margin-left: 8px;
}

.node-actions {
  display: flex;
  gap: 4px;
}

.node-action-btn {
  width: 20px;
  height: 20px;
  border: none;
  border-radius: 4px;
  background: rgba(0, 0, 0, 0.1);
  color: #666;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  transition: all 0.2s ease;
}

.node-action-btn:hover {
  background: rgba(0, 0, 0, 0.2);
  color: #333;
}

.delete-btn:hover {
  background: #F44336;
  color: white;
}

/* Node Body */
.node-body {
  padding: 12px 16px;
}

.node-description {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
  line-height: 1.4;
}

.node-parameters {
  font-size: 11px;
  color: #888;
}

.parameter-item {
  display: flex;
  margin-bottom: 4px;
}

.parameter-key {
  font-weight: 500;
  margin-right: 4px;
  min-width: 60px;
}

.parameter-value {
  flex: 1;
  word-break: break-word;
}

.parameter-more {
  font-style: italic;
  color: #aaa;
  margin-top: 4px;
}

/* Node Indicators */
.execution-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 16px;
  height: 16px;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(76, 175, 80, 0.3);
  border-top: 2px solid #4CAF50;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-indicator,
.success-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: bold;
}

.error-indicator {
  background: #F44336;
  color: white;
}

.success-indicator {
  background: #4CAF50;
  color: white;
}

/* Connection Lines */
.connection-line {
  stroke: #666;
  stroke-width: 2;
  fill: none;
  marker-end: url(#arrowhead);
}

.connection-line.active {
  stroke: #4CAF50;
  stroke-width: 3;
}

.connection-line.temporary {
  stroke: #2196F3;
  stroke-width: 2;
  stroke-dasharray: 5,5;
}

/* Node Connectors */
.node-connector {
  position: absolute;
  width: 12px;
  height: 12px;
  background: #666;
  border: 2px solid white;
  border-radius: 50%;
  cursor: crosshair;
  transition: all 0.2s ease;
}

.node-connector.input {
  top: 50%;
  left: -6px;
  transform: translateY(-50%);
}

.node-connector.output {
  top: 50%;
  right: -6px;
  transform: translateY(-50%);
}

.node-connector:hover {
  background: #4CAF50;
  transform: translateY(-50%) scale(1.2);
}

.node-connector.connecting {
  background: #2196F3;
  animation: pulse-connector 1s infinite;
}

@keyframes pulse-connector {
  0% { transform: translateY(-50%) scale(1); }
  50% { transform: translateY(-50%) scale(1.3); }
  100% { transform: translateY(-50%) scale(1); }
}

/* Canvas Controls */
.canvas-controls {
  position: absolute;
  bottom: 20px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  z-index: 100;
}

.control-group {
  display: flex;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  padding: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.control-btn {
  width: 36px;
  height: 36px;
  border: none;
  background: transparent;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  transition: all 0.2s ease;
}

.control-btn:hover {
  background: rgba(0, 0, 0, 0.1);
  color: #333;
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.zoom-level {
  padding: 8px 12px;
  font-size: 12px;
  color: #666;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  min-width: 60px;
  text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
  .workflow-node {
    min-width: 150px;
    max-width: 250px;
  }
  
  .node-header {
    padding: 8px 12px;
  }
  
  .node-body {
    padding: 8px 12px;
  }
  
  .canvas-controls {
    bottom: 10px;
    right: 10px;
  }
}
