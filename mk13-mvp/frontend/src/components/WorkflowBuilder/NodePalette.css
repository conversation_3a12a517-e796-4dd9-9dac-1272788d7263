/* Node Palette Styles */

.node-palette {
  width: 300px;
  background: rgba(255, 255, 255, 0.95);
  border-right: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  height: 100vh;
  transition: width 0.3s ease;
  z-index: 20;
}

.node-palette.collapsed {
  width: 50px;
}

.palette-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: rgba(0, 0, 0, 0.05);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.palette-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.collapse-btn {
  width: 24px;
  height: 24px;
  border: none;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  font-size: 12px;
  transition: all 0.2s ease;
}

.collapse-btn:hover {
  background: rgba(0, 0, 0, 0.2);
  color: #333;
}

.node-palette.collapsed .palette-header h3 {
  display: none;
}

/* Search */
.palette-search {
  padding: 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.search-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  font-size: 14px;
  background: white;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #4CAF50;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

.node-palette.collapsed .palette-search {
  display: none;
}

/* Categories */
.palette-categories {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  padding: 12px 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.category-btn {
  padding: 6px 12px;
  border: 1px solid rgba(0, 0, 0, 0.2);
  background: white;
  border-radius: 16px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.category-btn:hover {
  background: rgba(76, 175, 80, 0.1);
  border-color: #4CAF50;
}

.category-btn.active {
  background: #4CAF50;
  color: white;
  border-color: #4CAF50;
}

.node-palette.collapsed .palette-categories {
  display: none;
}

/* Content */
.palette-content {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.node-palette.collapsed .palette-content {
  display: none;
}

/* Category Groups */
.palette-category-group {
  margin-bottom: 16px;
}

.category-header {
  font-size: 12px;
  font-weight: 600;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 8px;
  padding: 0 8px;
}

/* Palette Nodes */
.palette-node {
  display: flex;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  cursor: grab;
  transition: all 0.2s ease;
  border-left: 4px solid #ddd;
}

.palette-node:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
  border-color: rgba(0, 0, 0, 0.2);
}

.palette-node.dragging {
  opacity: 0.5;
  cursor: grabbing;
  transform: rotate(2deg);
}

.palette-node-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  background: rgba(0, 0, 0, 0.05);
  flex-shrink: 0;
}

.palette-node-content {
  flex: 1;
  min-width: 0;
}

.palette-node-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.palette-node-description {
  font-size: 12px;
  color: #666;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* No Nodes Message */
.no-nodes-message {
  text-align: center;
  padding: 32px 16px;
  color: #666;
  font-size: 14px;
  font-style: italic;
}

/* AI Suggestions */
.palette-ai-suggestions {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  background: rgba(76, 175, 80, 0.05);
}

.suggestions-header {
  padding: 12px 16px;
  font-size: 12px;
  font-weight: 600;
  color: #4CAF50;
  border-bottom: 1px solid rgba(76, 175, 80, 0.2);
}

.suggestions-content {
  padding: 12px 16px;
}

.suggestion-item {
  padding: 8px 12px;
  background: white;
  border: 1px solid rgba(76, 175, 80, 0.3);
  border-radius: 6px;
  font-size: 12px;
  color: #333;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.suggestion-item:hover {
  background: rgba(76, 175, 80, 0.1);
  border-color: #4CAF50;
}

.node-palette.collapsed .palette-ai-suggestions {
  display: none;
}

/* Node Icon Styles */
.node-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.node-icon.trigger {
  color: #4CAF50;
}

.node-icon.action {
  color: #2196F3;
}

.node-icon.logic {
  color: #FF9800;
}

.node-icon.data {
  color: #9C27B0;
}

/* Scrollbar Styling */
.palette-content::-webkit-scrollbar {
  width: 6px;
}

.palette-content::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
}

.palette-content::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.palette-content::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .node-palette {
    width: 250px;
  }
  
  .node-palette.collapsed {
    width: 40px;
  }
}

@media (max-width: 768px) {
  .node-palette {
    position: absolute;
    left: 0;
    top: 0;
    z-index: 30;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.2);
  }
  
  .node-palette.collapsed {
    width: 40px;
  }
  
  .palette-node {
    padding: 8px;
  }
  
  .palette-node-icon {
    width: 28px;
    height: 28px;
    margin-right: 8px;
  }
  
  .palette-node-name {
    font-size: 13px;
  }
  
  .palette-node-description {
    font-size: 11px;
  }
}

/* Animation for drag preview */
@keyframes dragPreview {
  0% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: scale(0.9) rotate(2deg);
    opacity: 0.8;
  }
}

.palette-node.drag-preview {
  animation: dragPreview 0.2s ease-out;
}
