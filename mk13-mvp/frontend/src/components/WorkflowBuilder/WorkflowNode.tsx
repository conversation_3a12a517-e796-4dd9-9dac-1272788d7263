/**
 * Workflow Node Component
 * Individual draggable node in the workflow canvas
 */

import React, { useState, useRef, useCallback } from 'react';
import { useDrag } from 'react-dnd';
import { getEmptyImage } from 'react-dnd-html5-backend';
import { WorkflowNode as WorkflowNodeType } from '../../types/workflow';
import NodeIcon from './NodeIcon';
import NodeConnector from './NodeConnector';
import './WorkflowNode.css';

interface Position {
  x: number;
  y: number;
}

interface WorkflowNodeProps {
  node: WorkflowNodeType;
  isSelected: boolean;
  isConnecting: boolean;
  readOnly: boolean;
  onMove: (nodeId: string, position: Position) => void;
  onSelect: (nodeId: string, multiSelect: boolean) => void;
  onDelete: (nodeId: string) => void;
  onConnectionStart: (nodeId: string) => void;
  onConnectionEnd: (nodeId: string) => void;
}

const WorkflowNode: React.FC<WorkflowNodeProps> = ({
  node,
  isSelected,
  isConnecting,
  readOnly,
  onMove,
  onSelect,
  onDelete,
  onConnectionStart,
  onConnectionEnd
}) => {
  const nodeRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState<Position>({ x: 0, y: 0 });
  const [isHovered, setIsHovered] = useState(false);

  // Drag and drop setup
  const [{ isDragPreview }, drag, preview] = useDrag({
    type: 'workflow-node',
    item: () => {
      setIsDragging(true);
      return { id: node.id, type: node.type };
    },
    end: () => {
      setIsDragging(false);
    },
    collect: (monitor) => ({
      isDragPreview: monitor.isDragging(),
    }),
  });

  // Use empty image for drag preview (we'll show custom preview)
  React.useEffect(() => {
    preview(getEmptyImage(), { captureDraggingState: true });
  }, [preview]);

  // Handle mouse events for dragging
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (readOnly) return;
    
    e.stopPropagation();
    
    const rect = nodeRef.current?.getBoundingClientRect();
    if (rect) {
      setDragOffset({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      });
    }
    
    onSelect(node.id, e.ctrlKey || e.metaKey);
  }, [readOnly, node.id, onSelect]);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (isDragging && !readOnly) {
      const newPosition = {
        x: e.clientX - dragOffset.x,
        y: e.clientY - dragOffset.y
      };
      onMove(node.id, newPosition);
    }
  }, [isDragging, readOnly, dragOffset, node.id, onMove]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  // Attach global mouse events for dragging
  React.useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  // Handle double click for editing
  const handleDoubleClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    if (!readOnly) {
      // Open node editor
      console.log('Edit node:', node.id);
    }
  }, [readOnly, node.id]);

  // Handle delete key
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Delete' && isSelected && !readOnly) {
      e.stopPropagation();
      onDelete(node.id);
    }
  }, [isSelected, readOnly, node.id, onDelete]);

  // Handle connection events
  const handleConnectionMouseDown = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    if (!readOnly) {
      onConnectionStart(node.id);
    }
  }, [readOnly, node.id, onConnectionStart]);

  const handleConnectionMouseUp = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    if (isConnecting && !readOnly) {
      onConnectionEnd(node.id);
    }
  }, [isConnecting, readOnly, node.id, onConnectionEnd]);

  // Get node status class
  const getStatusClass = () => {
    if (node.status === 'running') return 'status-running';
    if (node.status === 'completed') return 'status-completed';
    if (node.status === 'error') return 'status-error';
    return '';
  };

  // Get node type class
  const getTypeClass = () => {
    return `node-type-${node.type.replace('_', '-')}`;
  };

  return (
    <div
      ref={(el) => {
        nodeRef.current = el;
        drag(el);
      }}
      className={`workflow-node ${getTypeClass()} ${getStatusClass()} ${
        isSelected ? 'selected' : ''
      } ${isDragPreview ? 'dragging' : ''} ${isHovered ? 'hovered' : ''}`}
      style={{
        transform: `translate(${node.position.x}px, ${node.position.y}px)`,
        opacity: isDragPreview ? 0.5 : 1,
      }}
      onMouseDown={handleMouseDown}
      onDoubleClick={handleDoubleClick}
      onKeyDown={handleKeyDown}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      tabIndex={0}
    >
      {/* Node header */}
      <div className="node-header">
        <NodeIcon type={node.type} status={node.status} />
        <span className="node-title">{node.name}</span>
        
        {!readOnly && (
          <div className="node-actions">
            <button
              className="node-action-btn delete-btn"
              onClick={(e) => {
                e.stopPropagation();
                onDelete(node.id);
              }}
              title="Delete node"
            >
              ×
            </button>
          </div>
        )}
      </div>

      {/* Node body */}
      <div className="node-body">
        {node.description && (
          <div className="node-description">{node.description}</div>
        )}
        
        {/* Show key parameters */}
        {node.parameters && Object.keys(node.parameters).length > 0 && (
          <div className="node-parameters">
            {Object.entries(node.parameters).slice(0, 2).map(([key, value]) => (
              <div key={key} className="parameter-item">
                <span className="parameter-key">{key}:</span>
                <span className="parameter-value">
                  {typeof value === 'string' && value.length > 20
                    ? `${value.substring(0, 20)}...`
                    : String(value)
                  }
                </span>
              </div>
            ))}
            {Object.keys(node.parameters).length > 2 && (
              <div className="parameter-more">
                +{Object.keys(node.parameters).length - 2} more
              </div>
            )}
          </div>
        )}
      </div>

      {/* Connection points */}
      <NodeConnector
        position="input"
        isConnecting={isConnecting}
        onMouseDown={handleConnectionMouseDown}
        onMouseUp={handleConnectionMouseUp}
      />
      
      <NodeConnector
        position="output"
        isConnecting={isConnecting}
        onMouseDown={handleConnectionMouseDown}
        onMouseUp={handleConnectionMouseUp}
      />

      {/* Execution indicator */}
      {node.status === 'running' && (
        <div className="execution-indicator">
          <div className="spinner" />
        </div>
      )}

      {/* Error indicator */}
      {node.status === 'error' && node.error && (
        <div className="error-indicator" title={node.error}>
          ⚠️
        </div>
      )}

      {/* Success indicator */}
      {node.status === 'completed' && (
        <div className="success-indicator">
          ✓
        </div>
      )}
    </div>
  );
};

export default WorkflowNode;
