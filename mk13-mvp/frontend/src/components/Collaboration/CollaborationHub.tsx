/**
 * Collaboration Hub Component
 * Main interface for real-time collaboration with WebRTC and shared workspaces
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../store/store';
import {
  createWorkspace,
  joinWorkspace,
  leaveWorkspace,
  sendCollaborationEvent,
  updateWorkspaceContent
} from '../../store/slices/collaborationSlice';
import SharedWorkspace from './SharedWorkspace';
import VideoConference from './VideoConference';
import VoiceChat from './VoiceChat';
import ParticipantsList from './ParticipantsList';
import AICollaborationPanel from './AICollaborationPanel';
import ScreenShare from './ScreenShare';
import './CollaborationHub.css';

interface CollaborationHubProps {
  workspaceId?: string;
  initialMode?: 'create' | 'join' | 'workspace';
}

const CollaborationHub: React.FC<CollaborationHubProps> = ({
  workspaceId,
  initialMode = 'workspace'
}) => {
  const dispatch = useDispatch();
  const [mode, setMode] = useState(initialMode);
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const [activeFeatures, setActiveFeatures] = useState({
    video: false,
    audio: false,
    screenShare: false,
    ai: true
  });

  const {
    currentWorkspace,
    participants,
    isConnected,
    connectionStatus,
    events,
    error
  } = useSelector((state: RootState) => state.collaboration);

  const { user } = useSelector((state: RootState) => state.auth);

  const websocketRef = useRef<WebSocket | null>(null);
  const localStreamRef = useRef<MediaStream | null>(null);
  const peerConnectionsRef = useRef<Map<string, RTCPeerConnection>>(new Map());

  useEffect(() => {
    if (workspaceId && user && mode === 'workspace') {
      handleJoinWorkspace(workspaceId);
    }

    return () => {
      cleanup();
    };
  }, [workspaceId, user, mode]);

  const handleCreateWorkspace = async (workspaceData: any) => {
    try {
      setIsConnecting(true);
      setConnectionError(null);

      const workspace = await dispatch(createWorkspace({
        name: workspaceData.name,
        description: workspaceData.description,
        settings: {
          maxParticipants: workspaceData.maxParticipants || 10,
          allowVoice: workspaceData.allowVoice !== false,
          allowVideo: workspaceData.allowVideo !== false,
          allowScreenShare: workspaceData.allowScreenShare !== false,
          aiEnabled: workspaceData.aiEnabled !== false
        }
      })).unwrap();

      await connectToWorkspace(workspace.workspace_id);
      setMode('workspace');

    } catch (error: any) {
      setConnectionError(error.message || 'Failed to create workspace');
    } finally {
      setIsConnecting(false);
    }
  };

  const handleJoinWorkspace = async (workspaceId: string) => {
    try {
      setIsConnecting(true);
      setConnectionError(null);

      await dispatch(joinWorkspace({
        workspaceId,
        userData: {
          name: user?.name || 'Anonymous',
          role: 'collaborator'
        }
      })).unwrap();

      await connectToWorkspace(workspaceId);

    } catch (error: any) {
      setConnectionError(error.message || 'Failed to join workspace');
    } finally {
      setIsConnecting(false);
    }
  };

  const connectToWorkspace = async (workspaceId: string) => {
    try {
      // Establish WebSocket connection
      const wsUrl = `${process.env.REACT_APP_WS_URL}/collaboration?workspace_id=${workspaceId}&user_id=${user?.id}`;
      const websocket = new WebSocket(wsUrl);

      websocket.onopen = () => {
        console.log('Connected to collaboration workspace');
        websocketRef.current = websocket;
      };

      websocket.onmessage = (event) => {
        handleWebSocketMessage(JSON.parse(event.data));
      };

      websocket.onclose = () => {
        console.log('Disconnected from collaboration workspace');
        websocketRef.current = null;
      };

      websocket.onerror = (error) => {
        console.error('WebSocket error:', error);
        setConnectionError('Connection failed');
      };

      // Initialize media if needed
      if (activeFeatures.video || activeFeatures.audio) {
        await initializeMedia();
      }

    } catch (error: any) {
      setConnectionError(error.message || 'Failed to connect');
    }
  };

  const handleWebSocketMessage = useCallback((data: any) => {
    const { type, event } = data;

    if (type === 'collaboration_event') {
      dispatch(sendCollaborationEvent(event));

      // Handle specific event types
      switch (event.event_type) {
        case 'user_joined':
          handleUserJoined(event);
          break;
        case 'user_left':
          handleUserLeft(event);
          break;
        case 'webrtc_offer':
          handleWebRTCOffer(event);
          break;
        case 'webrtc_answer':
          handleWebRTCAnswer(event);
          break;
        case 'ice_candidate':
          handleICECandidate(event);
          break;
        case 'ai_response':
          handleAIResponse(event);
          break;
      }
    }
  }, [dispatch]);

  const initializeMedia = async () => {
    try {
      const constraints = {
        video: activeFeatures.video ? {
          width: { ideal: 1280 },
          height: { ideal: 720 },
          frameRate: { ideal: 30 }
        } : false,
        audio: activeFeatures.audio ? {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        } : false
      };

      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      localStreamRef.current = stream;

      // Create peer connections for existing participants
      participants.forEach(participant => {
        if (participant.user_id !== user?.id) {
          createPeerConnection(participant.user_id);
        }
      });

    } catch (error) {
      console.error('Failed to initialize media:', error);
      setConnectionError('Failed to access camera/microphone');
    }
  };

  const createPeerConnection = async (participantId: string) => {
    try {
      const peerConnection = new RTCPeerConnection({
        iceServers: [
          { urls: 'stun:stun.l.google.com:19302' },
          { urls: 'stun:stun1.l.google.com:19302' }
        ]
      });

      // Add local stream
      if (localStreamRef.current) {
        localStreamRef.current.getTracks().forEach(track => {
          peerConnection.addTrack(track, localStreamRef.current!);
        });
      }

      // Handle ICE candidates
      peerConnection.onicecandidate = (event) => {
        if (event.candidate && websocketRef.current) {
          websocketRef.current.send(JSON.stringify({
            type: 'ice_candidate',
            target_peer_id: participantId,
            candidate: event.candidate
          }));
        }
      };

      // Handle remote stream
      peerConnection.ontrack = (event) => {
        const remoteStream = event.streams[0];
        // Handle remote stream (display in video element)
        console.log('Received remote stream from', participantId);
      };

      peerConnectionsRef.current.set(participantId, peerConnection);

      // Create offer if we're the initiator
      const offer = await peerConnection.createOffer();
      await peerConnection.setLocalDescription(offer);

      if (websocketRef.current) {
        websocketRef.current.send(JSON.stringify({
          type: 'webrtc_offer',
          target_peer_id: participantId,
          offer: offer
        }));
      }

    } catch (error) {
      console.error('Failed to create peer connection:', error);
    }
  };

  const handleUserJoined = (event: any) => {
    const { user_id } = event.data.participant;
    
    // Create peer connection for new user if media is active
    if ((activeFeatures.video || activeFeatures.audio) && user_id !== user?.id) {
      createPeerConnection(user_id);
    }
  };

  const handleUserLeft = (event: any) => {
    // Clean up peer connection
    const peerConnection = peerConnectionsRef.current.get(event.user_id);
    if (peerConnection) {
      peerConnection.close();
      peerConnectionsRef.current.delete(event.user_id);
    }
  };

  const handleWebRTCOffer = async (event: any) => {
    try {
      const { from_peer_id, offer } = event.data;
      
      let peerConnection = peerConnectionsRef.current.get(from_peer_id);
      if (!peerConnection) {
        peerConnection = new RTCPeerConnection({
          iceServers: [
            { urls: 'stun:stun.l.google.com:19302' }
          ]
        });
        peerConnectionsRef.current.set(from_peer_id, peerConnection);
      }

      await peerConnection.setRemoteDescription(offer);
      const answer = await peerConnection.createAnswer();
      await peerConnection.setLocalDescription(answer);

      if (websocketRef.current) {
        websocketRef.current.send(JSON.stringify({
          type: 'webrtc_answer',
          target_peer_id: from_peer_id,
          answer: answer
        }));
      }

    } catch (error) {
      console.error('Failed to handle WebRTC offer:', error);
    }
  };

  const handleWebRTCAnswer = async (event: any) => {
    try {
      const { from_peer_id, answer } = event.data;
      const peerConnection = peerConnectionsRef.current.get(from_peer_id);
      
      if (peerConnection) {
        await peerConnection.setRemoteDescription(answer);
      }

    } catch (error) {
      console.error('Failed to handle WebRTC answer:', error);
    }
  };

  const handleICECandidate = async (event: any) => {
    try {
      const { from_peer_id, candidate } = event.data;
      const peerConnection = peerConnectionsRef.current.get(from_peer_id);
      
      if (peerConnection) {
        await peerConnection.addIceCandidate(candidate);
      }

    } catch (error) {
      console.error('Failed to handle ICE candidate:', error);
    }
  };

  const handleAIResponse = (event: any) => {
    // Handle AI response in collaboration context
    console.log('AI Response:', event.data.response);
  };

  const toggleFeature = async (feature: keyof typeof activeFeatures) => {
    const newState = !activeFeatures[feature];
    
    setActiveFeatures(prev => ({
      ...prev,
      [feature]: newState
    }));

    if ((feature === 'video' || feature === 'audio') && newState && !localStreamRef.current) {
      await initializeMedia();
    }

    // Notify other participants
    if (websocketRef.current) {
      websocketRef.current.send(JSON.stringify({
        type: 'media_state_changed',
        media_state: {
          ...activeFeatures,
          [feature]: newState
        }
      }));
    }
  };

  const sendAIRequest = (message: string) => {
    if (websocketRef.current) {
      websocketRef.current.send(JSON.stringify({
        type: 'ai_request',
        message: message
      }));
    }
  };

  const cleanup = () => {
    // Close WebSocket
    if (websocketRef.current) {
      websocketRef.current.close();
      websocketRef.current = null;
    }

    // Close peer connections
    peerConnectionsRef.current.forEach(pc => pc.close());
    peerConnectionsRef.current.clear();

    // Stop local stream
    if (localStreamRef.current) {
      localStreamRef.current.getTracks().forEach(track => track.stop());
      localStreamRef.current = null;
    }
  };

  const renderCreateWorkspace = () => (
    <div className="create-workspace-form">
      <h2>Create New Workspace</h2>
      {/* Create workspace form */}
    </div>
  );

  const renderJoinWorkspace = () => (
    <div className="join-workspace-form">
      <h2>Join Workspace</h2>
      {/* Join workspace form */}
    </div>
  );

  const renderWorkspace = () => (
    <div className="collaboration-workspace">
      {/* Header */}
      <div className="workspace-header">
        <div className="workspace-info">
          <h2>{currentWorkspace?.name || 'Collaboration Workspace'}</h2>
          <span className="participant-count">
            {participants.length} participant{participants.length !== 1 ? 's' : ''}
          </span>
        </div>

        <div className="workspace-controls">
          <button
            className={`control-btn ${activeFeatures.audio ? 'active' : ''}`}
            onClick={() => toggleFeature('audio')}
            title="Toggle microphone"
          >
            🎤
          </button>
          
          <button
            className={`control-btn ${activeFeatures.video ? 'active' : ''}`}
            onClick={() => toggleFeature('video')}
            title="Toggle camera"
          >
            📹
          </button>
          
          <button
            className={`control-btn ${activeFeatures.screenShare ? 'active' : ''}`}
            onClick={() => toggleFeature('screenShare')}
            title="Share screen"
          >
            🖥️
          </button>
          
          <button
            className={`control-btn ${activeFeatures.ai ? 'active' : ''}`}
            onClick={() => toggleFeature('ai')}
            title="Toggle AI assistant"
          >
            🤖
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="workspace-content">
        <div className="main-area">
          <SharedWorkspace
            workspace={currentWorkspace}
            onContentChange={(content) => {
              dispatch(updateWorkspaceContent({ content }));
            }}
          />
        </div>

        <div className="sidebar">
          <ParticipantsList
            participants={participants}
            currentUserId={user?.id}
          />

          {activeFeatures.ai && (
            <AICollaborationPanel
              onSendMessage={sendAIRequest}
              conversationHistory={events.filter(e => 
                e.event_type === 'ai_request' || e.event_type === 'ai_response'
              )}
            />
          )}
        </div>
      </div>

      {/* Video Conference */}
      {(activeFeatures.video || activeFeatures.audio) && (
        <VideoConference
          participants={participants}
          localStream={localStreamRef.current}
          peerConnections={peerConnectionsRef.current}
        />
      )}

      {/* Screen Share */}
      {activeFeatures.screenShare && (
        <ScreenShare
          onStartShare={() => {/* Handle screen share */}}
          onStopShare={() => {/* Handle stop share */}}
        />
      )}
    </div>
  );

  return (
    <div className="collaboration-hub">
      {connectionError && (
        <div className="error-banner">
          <span>⚠️ {connectionError}</span>
          <button onClick={() => setConnectionError(null)}>×</button>
        </div>
      )}

      {isConnecting && (
        <div className="connecting-overlay">
          <div className="connecting-spinner" />
          <div>Connecting to workspace...</div>
        </div>
      )}

      {mode === 'create' && renderCreateWorkspace()}
      {mode === 'join' && renderJoinWorkspace()}
      {mode === 'workspace' && renderWorkspace()}
    </div>
  );
};

export default CollaborationHub;
