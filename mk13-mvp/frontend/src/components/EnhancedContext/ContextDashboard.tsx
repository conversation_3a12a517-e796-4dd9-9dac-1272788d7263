/**
 * Enhanced Context Dashboard Component
 * Displays comprehensive context intelligence with AI insights
 */

import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../store/store';
import { fetchEnhancedContext } from '../../store/slices/enhancedContextSlice';
import ContextOverview from './ContextOverview';
import ApplicationInsights from './ApplicationInsights';
import MeetingContext from './MeetingContext';
import TemporalPatterns from './TemporalPatterns';
import DeviceSync from './DeviceSync';
import AIInsights from './AIInsights';
import './ContextDashboard.css';

interface ContextDashboardProps {
  className?: string;
}

const ContextDashboard: React.FC<ContextDashboardProps> = ({ className }) => {
  const dispatch = useDispatch();
  const [activeTab, setActiveTab] = useState('overview');
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);

  const {
    enhancedContext,
    temporalPatterns,
    applicationInsights,
    meetingContext,
    deviceSync,
    isLoading,
    error,
    lastUpdated
  } = useSelector((state: RootState) => state.enhancedContext);

  const { user } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    if (user) {
      // Initial fetch
      dispatch(fetchEnhancedContext());

      // Set up auto-refresh every 30 seconds
      const interval = setInterval(() => {
        dispatch(fetchEnhancedContext());
      }, 30000);

      setRefreshInterval(interval);

      return () => {
        if (interval) {
          clearInterval(interval);
        }
      };
    }
  }, [user, dispatch]);

  useEffect(() => {
    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, [refreshInterval]);

  const handleRefresh = () => {
    dispatch(fetchEnhancedContext());
  };

  const tabs = [
    { id: 'overview', label: 'Overview', icon: '📊' },
    { id: 'applications', label: 'Applications', icon: '💻' },
    { id: 'meetings', label: 'Meetings', icon: '📅' },
    { id: 'patterns', label: 'Patterns', icon: '🔄' },
    { id: 'devices', label: 'Devices', icon: '📱' },
    { id: 'insights', label: 'AI Insights', icon: '🧠' }
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return <ContextOverview context={enhancedContext} />;
      case 'applications':
        return <ApplicationInsights insights={applicationInsights} />;
      case 'meetings':
        return <MeetingContext context={meetingContext} />;
      case 'patterns':
        return <TemporalPatterns patterns={temporalPatterns} />;
      case 'devices':
        return <DeviceSync syncData={deviceSync} />;
      case 'insights':
        return <AIInsights insights={enhancedContext?.ai_insights} />;
      default:
        return <ContextOverview context={enhancedContext} />;
    }
  };

  const getContextStatusColor = () => {
    if (!enhancedContext) return '#666';
    
    if (enhancedContext.confidence > 0.8) return '#4CAF50';
    if (enhancedContext.confidence > 0.6) return '#FF9800';
    return '#F44336';
  };

  const getLastUpdatedText = () => {
    if (!lastUpdated) return 'Never';
    
    const now = new Date();
    const updated = new Date(lastUpdated);
    const diffMs = now.getTime() - updated.getTime();
    const diffSeconds = Math.floor(diffMs / 1000);
    
    if (diffSeconds < 60) return `${diffSeconds}s ago`;
    if (diffSeconds < 3600) return `${Math.floor(diffSeconds / 60)}m ago`;
    return `${Math.floor(diffSeconds / 3600)}h ago`;
  };

  return (
    <div className={`context-dashboard ${className || ''}`}>
      {/* Header */}
      <div className="dashboard-header">
        <div className="header-left">
          <h2 className="dashboard-title">Context Intelligence</h2>
          <div className="context-status">
            <div 
              className="status-indicator"
              style={{ backgroundColor: getContextStatusColor() }}
            />
            <span className="status-text">
              {enhancedContext?.primary_type || 'Unknown'} Context
            </span>
            {enhancedContext && (
              <span className="confidence-score">
                {Math.round(enhancedContext.confidence * 100)}% confidence
              </span>
            )}
          </div>
        </div>

        <div className="header-right">
          <div className="last-updated">
            Last updated: {getLastUpdatedText()}
          </div>
          <button
            className="refresh-btn"
            onClick={handleRefresh}
            disabled={isLoading}
            title="Refresh context data"
          >
            {isLoading ? '⟳' : '↻'}
          </button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="error-banner">
          <span className="error-icon">⚠️</span>
          <span className="error-message">{error}</span>
          <button 
            className="error-dismiss"
            onClick={() => dispatch({ type: 'enhancedContext/clearError' })}
          >
            ×
          </button>
        </div>
      )}

      {/* Quick Stats */}
      {enhancedContext && (
        <div className="quick-stats">
          <div className="stat-item">
            <div className="stat-value">
              {enhancedContext.active_applications?.length || 0}
            </div>
            <div className="stat-label">Active Apps</div>
          </div>
          
          <div className="stat-item">
            <div className="stat-value">
              {enhancedContext.upcoming_meetings?.length || 0}
            </div>
            <div className="stat-label">Upcoming Meetings</div>
          </div>
          
          <div className="stat-item">
            <div className="stat-value">
              {enhancedContext.device_contexts?.length || 0}
            </div>
            <div className="stat-label">Synced Devices</div>
          </div>
          
          <div className="stat-item">
            <div className="stat-value">
              {Math.round((enhancedContext.context_stability || 0) * 100)}%
            </div>
            <div className="stat-label">Stability</div>
          </div>
        </div>
      )}

      {/* Navigation Tabs */}
      <div className="dashboard-tabs">
        {tabs.map(tab => (
          <button
            key={tab.id}
            className={`tab-button ${activeTab === tab.id ? 'active' : ''}`}
            onClick={() => setActiveTab(tab.id)}
          >
            <span className="tab-icon">{tab.icon}</span>
            <span className="tab-label">{tab.label}</span>
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="dashboard-content">
        {isLoading && !enhancedContext ? (
          <div className="loading-state">
            <div className="loading-spinner" />
            <div className="loading-text">Loading context intelligence...</div>
          </div>
        ) : (
          renderTabContent()
        )}
      </div>

      {/* Predictions Panel */}
      {enhancedContext?.predicted_next_context && (
        <div className="predictions-panel">
          <h3 className="predictions-title">🔮 Predictions</h3>
          <div className="prediction-item">
            <div className="prediction-label">Next Context:</div>
            <div className="prediction-value">
              {enhancedContext.predicted_next_context}
            </div>
          </div>
          
          {enhancedContext.interruption_likelihood > 0.5 && (
            <div className="prediction-item warning">
              <div className="prediction-label">Interruption Risk:</div>
              <div className="prediction-value">
                {Math.round(enhancedContext.interruption_likelihood * 100)}%
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ContextDashboard;
