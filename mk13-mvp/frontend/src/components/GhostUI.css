/* Ghost UI Styles */
.ghost-ui {
  width: 400px;
  min-height: 300px;
  background: rgba(20, 20, 30, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: white;
  user-select: none;
  transition: all 0.3s ease;
}

.ghost-ui.minimized {
  min-height: auto;
  height: 40px;
}

.ghost-ui.dragging {
  cursor: grabbing;
  opacity: 0.8;
}

/* Header */
.ghost-ui-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px 12px 0 0;
  cursor: grab;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.ghost-ui-header:active {
  cursor: grabbing;
}

.ghost-ui-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
}

.ghost-ui-icon {
  font-size: 16px;
}

.ghost-ui-controls {
  display: flex;
  gap: 4px;
}

.ghost-ui-btn {
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  transition: background 0.2s ease;
}

.ghost-ui-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.ghost-ui-btn.close:hover {
  background: rgba(255, 59, 48, 0.8);
}

/* Content */
.ghost-ui-content {
  padding: 16px;
}

.ghost-ui-modes {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 4px;
}

.mode-btn {
  flex: 1;
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  background: transparent;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.mode-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.mode-btn.active {
  background: rgba(0, 122, 255, 0.8);
  color: white;
}

/* Main Content Area */
.ghost-ui-main {
  min-height: 200px;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
}

/* Status Bar */
.ghost-ui-status {
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
}

.status-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-indicator {
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-indicator.connected {
  color: #34c759;
}

.status-indicator.disconnected {
  color: #ff3b30;
}

.status-indicator.detecting {
  color: #007AFF;
}

.status-indicator.idle {
  color: rgba(255, 255, 255, 0.5);
}

.context-info {
  text-align: center;
  color: rgba(255, 255, 255, 0.7);
  font-size: 11px;
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

.current-context {
  text-align: center;
  color: rgba(0, 122, 255, 0.9);
  font-size: 11px;
  padding: 2px 8px;
  background: rgba(0, 122, 255, 0.1);
  border-radius: 4px;
  border: 1px solid rgba(0, 122, 255, 0.3);
}

/* Chat Interface Styles */
.chat-interface {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 12px;
  max-height: 150px;
}

.chat-message {
  margin-bottom: 8px;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 13px;
  line-height: 1.4;
}

.chat-message.user {
  background: rgba(0, 122, 255, 0.2);
  margin-left: 20px;
}

.chat-message.assistant {
  background: rgba(255, 255, 255, 0.1);
  margin-right: 20px;
}

.chat-input-container {
  display: flex;
  gap: 8px;
}

.chat-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 13px;
}

.chat-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.chat-input:focus {
  outline: none;
  border-color: rgba(0, 122, 255, 0.8);
}

.send-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  background: rgba(0, 122, 255, 0.8);
  color: white;
  cursor: pointer;
  font-size: 13px;
  transition: background 0.2s ease;
}

.send-btn:hover {
  background: rgba(0, 122, 255, 1);
}

.send-btn:disabled {
  background: rgba(255, 255, 255, 0.1);
  cursor: not-allowed;
}

/* Voice Input Styles */
.voice-input {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: 16px;
}

.voice-btn {
  width: 80px;
  height: 80px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 59, 48, 0.8);
  color: white;
  cursor: pointer;
  font-size: 24px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.voice-btn:hover {
  background: rgba(255, 59, 48, 1);
  transform: scale(1.05);
}

.voice-btn.recording {
  background: rgba(255, 149, 0, 0.8);
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.voice-status {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
}

/* Quick Actions Styles */
.quick-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  height: 100%;
}

.action-btn {
  padding: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.05);
  color: white;
  cursor: pointer;
  font-size: 12px;
  text-align: center;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(0, 122, 255, 0.8);
}

.action-icon {
  font-size: 20px;
}

/* Scrollbar Styles */
.chat-messages::-webkit-scrollbar {
  width: 4px;
}

.chat-messages::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Chat Interface Additional Styles */
.chat-welcome {
  text-align: center;
  color: rgba(255, 255, 255, 0.7);
  font-size: 13px;
  padding: 20px;
}

.message-suggestions {
  margin-top: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.suggestion-btn {
  padding: 4px 8px;
  border: 1px solid rgba(0, 122, 255, 0.5);
  border-radius: 12px;
  background: rgba(0, 122, 255, 0.1);
  color: rgba(0, 122, 255, 0.9);
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.suggestion-btn:hover {
  background: rgba(0, 122, 255, 0.2);
  border-color: rgba(0, 122, 255, 0.8);
}

.typing-indicator {
  display: flex;
  gap: 4px;
  align-items: center;
}

.typing-indicator span {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 80%, 100% { opacity: 0.3; }
  40% { opacity: 1; }
}

/* Voice Input Additional Styles */
.voice-transcript {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  padding: 12px;
  margin-top: 12px;
  font-size: 13px;
}

.voice-error {
  text-align: center;
  color: rgba(255, 59, 48, 0.8);
  font-size: 13px;
}

.error-icon {
  font-size: 24px;
  display: block;
  margin-bottom: 8px;
}

.voice-instructions {
  margin-top: 16px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.voice-instructions ul {
  margin: 8px 0 0 16px;
  padding: 0;
}

.voice-instructions li {
  margin-bottom: 4px;
}

/* Quick Actions Additional Styles */
.action-category {
  margin-bottom: 16px;
}

.category-header {
  border-left: 3px solid #007AFF;
  padding-left: 8px;
  margin-bottom: 8px;
}

.category-header h4 {
  margin: 0;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.action-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 6px;
}

.custom-action {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.custom-action-input {
  margin-top: 8px;
}

.custom-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.05);
  color: white;
  font-size: 12px;
  margin-bottom: 4px;
}

.custom-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.custom-input:focus {
  outline: none;
  border-color: rgba(0, 122, 255, 0.8);
}

.input-hint {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.5);
}

/* Notification Overlay Styles */
.notification-overlay {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 20000;
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-width: 350px;
}

.notification {
  background: rgba(20, 20, 30, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  backdrop-filter: blur(20px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  color: white;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  transform: translateX(100%);
  opacity: 0;
  transition: all 0.3s ease;
}

.notification.visible {
  transform: translateX(0);
  opacity: 1;
}

.notification-success {
  border-left: 4px solid #34c759;
}

.notification-warning {
  border-left: 4px solid #ff9500;
}

.notification-error {
  border-left: 4px solid #ff3b30;
}

.notification-ai_suggestion {
  border-left: 4px solid #007aff;
}

.notification-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 12px 8px 12px;
}

.notification-icon {
  font-size: 16px;
}

.notification-title {
  flex: 1;
  font-weight: 500;
  font-size: 14px;
}

.notification-close {
  width: 20px;
  height: 20px;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-close:hover {
  background: rgba(255, 255, 255, 0.2);
}

.notification-content {
  padding: 0 12px 8px 12px;
}

.notification-message {
  margin: 0;
  font-size: 13px;
  line-height: 1.4;
  color: rgba(255, 255, 255, 0.9);
}

.notification-actions {
  margin-top: 8px;
  display: flex;
  gap: 6px;
}

.notification-action-btn {
  padding: 4px 12px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: background 0.2s ease;
}

.notification-action-btn.primary {
  background: rgba(0, 122, 255, 0.8);
  color: white;
}

.notification-action-btn.primary:hover {
  background: rgba(0, 122, 255, 1);
}

.notification-action-btn.secondary {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.notification-action-btn.secondary:hover {
  background: rgba(255, 255, 255, 0.2);
}

.notification-timestamp {
  padding: 0 12px 8px 12px;
  font-size: 11px;
  color: rgba(255, 255, 255, 0.5);
  text-align: right;
}

/* Context Manager Styles */
.context-manager {
  max-height: 400px;
  overflow-y: auto;
  padding: 16px;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 8px;
}

.context-manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.context-manager-header h3 {
  margin: 0;
  font-size: 16px;
  color: white;
}

.close-btn {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  font-size: 18px;
  cursor: pointer;
  padding: 4px;
}

.close-btn:hover {
  color: white;
}

.current-context-card {
  background: rgba(0, 122, 255, 0.1);
  border: 1px solid rgba(0, 122, 255, 0.3);
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 16px;
}

.current-context-card h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: rgba(0, 122, 255, 0.9);
}

.context-details {
  font-size: 12px;
}

.context-name {
  font-weight: 500;
  color: white;
  margin-bottom: 4px;
}

.context-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.context-category {
  background: rgba(255, 255, 255, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  text-transform: uppercase;
}

.context-time {
  color: rgba(255, 255, 255, 0.6);
}

.context-project,
.context-apps {
  color: rgba(255, 255, 255, 0.8);
  font-size: 11px;
  margin-bottom: 2px;
}

.quick-actions-section {
  margin-bottom: 16px;
}

.quick-actions-section h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: white;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 6px;
}

.quick-action-btn {
  padding: 8px 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.05);
  color: white;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.2s ease;
}

.quick-action-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.3);
}

.create-context-section {
  margin-bottom: 16px;
}

.create-context-btn {
  width: 100%;
  padding: 10px;
  border: 1px dashed rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  background: transparent;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.create-context-btn:hover {
  background: rgba(255, 255, 255, 0.05);
  color: white;
  border-color: rgba(255, 255, 255, 0.5);
}

.create-form {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.context-name-input,
.context-type-select {
  padding: 8px 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.05);
  color: white;
  font-size: 12px;
}

.context-name-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.context-name-input:focus,
.context-type-select:focus {
  outline: none;
  border-color: rgba(0, 122, 255, 0.8);
}

.form-actions {
  display: flex;
  gap: 8px;
}

.create-btn,
.cancel-btn {
  flex: 1;
  padding: 8px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.create-btn {
  background: rgba(0, 122, 255, 0.8);
  color: white;
}

.create-btn:hover:not(:disabled) {
  background: rgba(0, 122, 255, 1);
}

.create-btn:disabled {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.5);
  cursor: not-allowed;
}

.cancel-btn {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.cancel-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.contexts-list {
  margin-bottom: 16px;
}

.contexts-list h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: white;
}

.loading {
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  padding: 20px;
}

.empty-state {
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  padding: 20px;
}

.contexts-grid {
  display: flex;
  flex-direction: column;
  gap: 6px;
  max-height: 200px;
  overflow-y: auto;
}

.context-card {
  padding: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.05);
  cursor: pointer;
  transition: all 0.2s ease;
}

.context-card:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
}

.context-card.active {
  background: rgba(0, 122, 255, 0.2);
  border-color: rgba(0, 122, 255, 0.5);
}

.context-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.context-card-name {
  font-weight: 500;
  color: white;
  font-size: 12px;
}

.context-card-category {
  background: rgba(255, 255, 255, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 9px;
  text-transform: uppercase;
  color: rgba(255, 255, 255, 0.8);
}

.context-card-details {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.7);
}

.context-card-project {
  margin-bottom: 2px;
}

.context-card-time {
  color: rgba(255, 255, 255, 0.5);
}

.detected-context-info {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  padding: 12px;
  margin-top: 16px;
}

.detected-context-info h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: white;
}

.detection-details {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.8);
}

.detection-details div {
  margin-bottom: 2px;
}
