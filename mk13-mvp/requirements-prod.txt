# Production Requirements for MK13 AI Assistant
# Core dependencies for production deployment

# Web Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
gunicorn==21.2.0
gevent==23.9.1

# Data Validation & Settings
pydantic==2.5.0
pydantic-settings==2.1.0

# Database
sqlalchemy==2.0.23
alembic==1.13.0
asyncpg==0.29.0
psycopg2-binary==2.9.9

# Caching & Message Queue
redis==5.0.1
celery==5.3.4

# WebSocket & Real-time
websockets==12.0
python-socketio==5.10.0

# HTTP Client
httpx==0.25.2

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
cryptography==41.0.7
bcrypt==4.1.2

# File Handling
python-multipart==0.0.6
aiofiles==23.2.1
Pillow==10.1.0
python-magic==0.4.27

# Configuration
python-dotenv==1.0.0

# Logging & Monitoring
structlog==23.2.0
prometheus-client==0.19.0
sentry-sdk[fastapi]==1.38.0

# Distributed Tracing
opentelemetry-api==1.21.0
opentelemetry-sdk==1.21.0
opentelemetry-instrumentation-fastapi==0.42b0
opentelemetry-instrumentation-sqlalchemy==0.42b0
opentelemetry-instrumentation-redis==0.42b0

# System Monitoring
psutil==5.9.6

# AI & ML
numpy==1.25.2
scipy==1.11.4
scikit-learn==1.3.2

# Validation
email-validator==2.1.0
phonenumbers==8.13.26

# Timezone
tzdata==2023.3

# JSON Logging
python-json-logger==2.0.7
