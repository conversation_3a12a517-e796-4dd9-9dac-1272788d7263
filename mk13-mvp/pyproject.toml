[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "mk13-ai-assistant"
version = "1.0.0"
description = "Enterprise-grade Personal AI Assistant with Real-time Collaboration"
readme = "README.md"
license = {file = "LICENSE"}
authors = [
    {name = "MK13 Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "MK13 Team", email = "<EMAIL>"}
]
keywords = ["ai", "assistant", "collaboration", "productivity", "enterprise"]
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Intended Audience :: End Users/Desktop",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Office/Business :: Groupware",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Communications :: Chat",
    "Topic :: Internet :: WWW/HTTP :: Dynamic Content",
]
requires-python = ">=3.11"
dependencies = [
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "sqlalchemy>=2.0.23",
    "alembic>=1.13.0",
    "asyncpg>=0.29.0",
    "redis>=5.0.1",
    "celery>=5.3.4",
    "websockets>=12.0",
    "httpx>=0.25.2",
    "python-multipart>=0.0.6",
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",
    "python-dotenv>=1.0.0",
    "structlog>=23.2.0",
    "prometheus-client>=0.19.0",
    "opentelemetry-api>=1.21.0",
    "opentelemetry-sdk>=1.21.0",
    "opentelemetry-instrumentation-fastapi>=0.42b0",
    "opentelemetry-instrumentation-sqlalchemy>=0.42b0",
    "opentelemetry-instrumentation-redis>=0.42b0",
    "sentry-sdk[fastapi]>=1.38.0",
    "psutil>=5.9.6",
    "numpy>=1.25.2",
    "scipy>=1.11.4",
    "scikit-learn>=1.3.2",
    "aiofiles>=23.2.1",
    "Pillow>=10.1.0",
    "python-magic>=0.4.27",
    "cryptography>=41.0.7",
    "bcrypt>=4.1.2",
    "email-validator>=2.1.0",
    "phonenumbers>=8.13.26",
    "tzdata>=2023.3",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
    "httpx>=0.25.2",
    "black>=23.11.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
    "mypy>=1.7.1",
    "pre-commit>=3.6.0",
    "bandit>=1.7.5",
    "safety>=2.3.5",
    "coverage>=7.3.2",
]
test = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
    "httpx>=0.25.2",
    "factory-boy>=3.3.0",
    "faker>=20.1.0",
]
prod = [
    "gunicorn>=21.2.0",
    "gevent>=23.9.1",
]

[project.urls]
Homepage = "https://mk13.ai"
Documentation = "https://docs.mk13.ai"
Repository = "https://github.com/mk13-ai/mk13-assistant"
"Bug Tracker" = "https://github.com/mk13-ai/mk13-assistant/issues"
Changelog = "https://github.com/mk13-ai/mk13-assistant/blob/main/CHANGELOG.md"

[project.scripts]
mk13-server = "mk13.cli:main"
mk13-worker = "mk13.worker:main"
mk13-migrate = "mk13.database:migrate"

[tool.setuptools.packages.find]
where = ["src"]
include = ["mk13*"]
exclude = ["tests*"]

[tool.setuptools.package-data]
mk13 = ["py.typed", "*.pyi"]

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["mk13"]
known_third_party = ["fastapi", "pydantic", "sqlalchemy", "redis", "celery"]

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true

[[tool.mypy.overrides]]
module = [
    "psutil.*",
    "websockets.*",
    "celery.*",
    "redis.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "e2e: marks tests as end-to-end tests",
]

[tool.coverage.run]
source = ["src/mk13"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/migrations/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.bandit]
exclude_dirs = ["tests", "migrations"]
skips = ["B101", "B601"]

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    ".venv",
    ".eggs",
    "*.egg",
]
