# MK13 MVP API Documentation

Complete API reference for the MK13 MVP backend services.

## 🔗 Base URL

- **Development**: `http://localhost:8000`
- **Production**: `https://your-domain.com`

## 🔐 Authentication

The API uses multiple authentication methods:

### 1. Session-Based Authentication
```http
X-Session-Token: your_session_token
```

### 2. JWT Bearer Token
```http
Authorization: Bearer your_jwt_token
```

### 3. API Key Authentication
```http
X-API-Key: your_api_key
```

## 📋 API Endpoints

### Health Check

#### GET /health
Check API health status.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00Z",
  "version": "1.0.0",
  "services": {
    "database": "connected",
    "redis": "connected",
    "ai_services": "available"
  }
}
```

---

## 🔑 Authentication Endpoints

### GET /api/auth/google
Generate Google OAuth2 authorization URL.

**Query Parameters:**
- `user_id` (optional): User identifier for state tracking

**Response:**
```json
{
  "success": true,
  "auth_url": "https://accounts.google.com/o/oauth2/auth?...",
  "state": "secure_random_state"
}
```

### GET /auth/google/callback
Handle Google OAuth2 callback.

**Query Parameters:**
- `code`: Authorization code from Google
- `state`: State parameter for verification

**Response:**
```json
{
  "success": true,
  "user": {
    "id": "user_123",
    "email": "<EMAIL>",
    "name": "John Doe",
    "picture": "https://..."
  },
  "jwt_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "session_token": "secure_session_token",
  "expires_in": 86400
}
```

### POST /api/auth/logout
Logout current user.

**Headers:**
```http
X-Session-Token: your_session_token
```

**Response:**
```json
{
  "success": true,
  "message": "Logged out successfully"
}
```

### GET /api/auth/me
Get current user information.

**Headers:**
```http
X-Session-Token: your_session_token
```

**Response:**
```json
{
  "success": true,
  "user": {
    "user_id": "user_123",
    "email": "<EMAIL>",
    "name": "John Doe",
    "created_at": "2024-01-01T00:00:00Z",
    "last_activity": "2024-01-15T10:30:00Z"
  }
}
```

---

## 🤖 AI Interaction Endpoints

### POST /api/ai/chat
Send message to AI for processing.

**Headers:**
```http
X-Session-Token: your_session_token
Content-Type: application/json
```

**Request Body:**
```json
{
  "message": "Help me write an email to my team",
  "context_id": "context_123",
  "model_preference": "gpt-4",
  "stream": false
}
```

**Response:**
```json
{
  "success": true,
  "result": {
    "response": "I'd be happy to help you write an email...",
    "model_used": "gpt-4",
    "tokens_used": 150,
    "processing_time": 2.3,
    "confidence": 0.95,
    "suggestions": [
      "Would you like me to make it more formal?",
      "Should I include specific action items?"
    ]
  }
}
```

### POST /api/ai/voice
Process voice input through AI.

**Headers:**
```http
X-Session-Token: your_session_token
Content-Type: multipart/form-data
```

**Request Body:**
```
audio_file: [audio file]
language: en-US
context_id: context_123
```

**Response:**
```json
{
  "success": true,
  "result": {
    "transcript": "Help me schedule a meeting with John",
    "ai_response": "I can help you schedule a meeting...",
    "actions": [
      {
        "type": "calendar_create",
        "data": {
          "title": "Meeting with John",
          "suggested_times": ["2024-01-16T14:00:00Z"]
        }
      }
    ]
  }
}
```

### GET /api/ai/models
Get available AI models and their capabilities.

**Response:**
```json
{
  "success": true,
  "models": [
    {
      "id": "gpt-4",
      "name": "GPT-4",
      "provider": "openai",
      "capabilities": ["text", "code", "analysis"],
      "max_tokens": 8192,
      "cost_per_token": 0.00003,
      "available": true
    },
    {
      "id": "claude-3",
      "name": "Claude 3",
      "provider": "anthropic",
      "capabilities": ["text", "analysis", "reasoning"],
      "max_tokens": 100000,
      "cost_per_token": 0.000015,
      "available": true
    }
  ]
}
```

---

## 📁 Context Management Endpoints

### GET /api/contexts
Get user's contexts.

**Headers:**
```http
X-Session-Token: your_session_token
```

**Query Parameters:**
- `limit` (optional): Number of contexts to return (default: 50)
- `offset` (optional): Offset for pagination (default: 0)
- `category` (optional): Filter by category (work, personal, project, meeting)

**Response:**
```json
{
  "success": true,
  "contexts": [
    {
      "id": "context_123",
      "name": "Work Project Alpha",
      "state": {
        "currentProject": "Alpha",
        "activeApplications": ["VS Code", "Chrome"],
        "browserTabs": [
          {
            "title": "GitHub - Alpha Project",
            "url": "https://github.com/company/alpha"
          }
        ],
        "lastInteraction": "2024-01-15T10:30:00Z"
      },
      "metadata": {
        "category": "work",
        "priority": "high",
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-01-15T10:30:00Z"
      }
    }
  ],
  "total": 5,
  "has_more": false
}
```

### POST /api/contexts
Create a new context.

**Headers:**
```http
X-Session-Token: your_session_token
Content-Type: application/json
```

**Request Body:**
```json
{
  "name": "New Project Context",
  "state": {
    "currentProject": "New Project",
    "activeApplications": ["VS Code"],
    "lastInteraction": "2024-01-15T10:30:00Z"
  },
  "metadata": {
    "category": "work",
    "priority": "medium"
  }
}
```

**Response:**
```json
{
  "success": true,
  "context": {
    "id": "context_456",
    "name": "New Project Context",
    "state": { /* ... */ },
    "metadata": { /* ... */ },
    "userId": "user_123"
  }
}
```

### PUT /api/contexts/{context_id}
Update an existing context.

**Headers:**
```http
X-Session-Token: your_session_token
Content-Type: application/json
```

**Request Body:**
```json
{
  "name": "Updated Context Name",
  "state": {
    "currentProject": "Updated Project",
    "lastInteraction": "2024-01-15T11:00:00Z"
  }
}
```

**Response:**
```json
{
  "success": true,
  "context": {
    "id": "context_123",
    "name": "Updated Context Name",
    "state": { /* updated state */ },
    "metadata": { /* updated metadata */ }
  }
}
```

### DELETE /api/contexts/{context_id}
Delete a context.

**Headers:**
```http
X-Session-Token: your_session_token
```

**Response:**
```json
{
  "success": true,
  "message": "Context deleted successfully"
}
```

---

## 📧 Google Workspace Endpoints

### GET /api/google/emails
Get recent emails from Gmail.

**Headers:**
```http
X-Session-Token: your_session_token
```

**Query Parameters:**
- `limit` (optional): Number of emails to return (default: 10, max: 50)
- `query` (optional): Gmail search query
- `label` (optional): Filter by label (INBOX, SENT, etc.)

**Response:**
```json
{
  "success": true,
  "emails": [
    {
      "id": "email_123",
      "threadId": "thread_456",
      "subject": "Project Update",
      "from": {
        "name": "John Doe",
        "email": "<EMAIL>"
      },
      "to": [
        {
          "name": "You",
          "email": "<EMAIL>"
        }
      ],
      "snippet": "Here's the latest update on the project...",
      "date": "2024-01-15T09:30:00Z",
      "labels": ["INBOX", "IMPORTANT"],
      "isRead": false
    }
  ],
  "total": 25,
  "nextPageToken": "next_page_token"
}
```

### POST /api/google/email/send
Send an email through Gmail.

**Headers:**
```http
X-Session-Token: your_session_token
Content-Type: application/json
```

**Request Body:**
```json
{
  "to": ["<EMAIL>"],
  "cc": ["<EMAIL>"],
  "subject": "Email Subject",
  "body": "Email body content",
  "html": "<p>HTML email body</p>",
  "attachments": [
    {
      "filename": "document.pdf",
      "content": "base64_encoded_content",
      "mimeType": "application/pdf"
    }
  ]
}
```

**Response:**
```json
{
  "success": true,
  "message": {
    "id": "message_123",
    "threadId": "thread_456",
    "labelIds": ["SENT"]
  }
}
```

### GET /api/google/calendar
Get calendar events.

**Headers:**
```http
X-Session-Token: your_session_token
```

**Query Parameters:**
- `timeMin` (optional): Start time (ISO 8601)
- `timeMax` (optional): End time (ISO 8601)
- `maxResults` (optional): Max events to return (default: 10)

**Response:**
```json
{
  "success": true,
  "events": [
    {
      "id": "event_123",
      "summary": "Team Meeting",
      "description": "Weekly team sync",
      "start": {
        "dateTime": "2024-01-16T14:00:00Z",
        "timeZone": "America/New_York"
      },
      "end": {
        "dateTime": "2024-01-16T15:00:00Z",
        "timeZone": "America/New_York"
      },
      "attendees": [
        {
          "email": "<EMAIL>",
          "responseStatus": "accepted"
        }
      ],
      "location": "Conference Room A",
      "meetingLink": "https://meet.google.com/abc-defg-hij"
    }
  ]
}
```

---

## 🔄 WebSocket Events

### Connection
```javascript
const ws = new WebSocket('ws://localhost:8000/ws');
```

### Events

#### Client → Server

**Join User Channel:**
```json
{
  "event": "join",
  "data": {
    "user_id": "user_123",
    "session_token": "session_token"
  }
}
```

**Send Message:**
```json
{
  "event": "message",
  "data": {
    "message": "Hello AI",
    "context_id": "context_123"
  }
}
```

**Context Update:**
```json
{
  "event": "context_update",
  "data": {
    "user_id": "user_123",
    "context": {
      "currentProject": "Alpha",
      "activeApplications": ["VS Code"]
    }
  }
}
```

#### Server → Client

**AI Response:**
```json
{
  "event": "ai_response",
  "data": {
    "response": "Hello! How can I help you?",
    "model_used": "gpt-4",
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

**Context Detected:**
```json
{
  "event": "context_detected",
  "data": {
    "context": {
      "currentProject": "Alpha",
      "confidence": 0.85
    },
    "suggestions": [
      "Would you like me to help with Alpha project tasks?"
    ]
  }
}
```

**Notification:**
```json
{
  "event": "notification",
  "data": {
    "type": "info",
    "title": "New Email",
    "message": "You have a new email from John Doe",
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

---

## ❌ Error Responses

### Standard Error Format
```json
{
  "success": false,
  "error": {
    "code": "INVALID_REQUEST",
    "message": "The request is invalid",
    "details": "Missing required field: message"
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Common Error Codes

| Code | Status | Description |
|------|--------|-------------|
| `UNAUTHORIZED` | 401 | Authentication required |
| `FORBIDDEN` | 403 | Insufficient permissions |
| `NOT_FOUND` | 404 | Resource not found |
| `INVALID_REQUEST` | 400 | Request validation failed |
| `RATE_LIMITED` | 429 | Too many requests |
| `INTERNAL_ERROR` | 500 | Server error |
| `SERVICE_UNAVAILABLE` | 503 | External service unavailable |

---

## 📊 Rate Limits

| Endpoint Category | Limit | Window |
|------------------|-------|---------|
| Authentication | 5 requests | 5 minutes |
| AI Interactions | 20 requests | 1 minute |
| General API | 100 requests | 1 minute |
| Google Workspace | 50 requests | 1 minute |

Rate limit headers are included in responses:
```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1642248600
```

---

## 🔧 SDK Examples

### JavaScript/TypeScript
```typescript
import { MK13Client } from '@mk13/sdk';

const client = new MK13Client({
  baseURL: 'https://api.mk13.com',
  sessionToken: 'your_session_token'
});

// Send AI message
const response = await client.ai.chat({
  message: 'Help me with my project',
  contextId: 'context_123'
});

// Get contexts
const contexts = await client.contexts.list();

// Send email
await client.google.sendEmail({
  to: ['<EMAIL>'],
  subject: 'Hello',
  body: 'Hello from MK13!'
});
```

### Python
```python
from mk13_sdk import MK13Client

client = MK13Client(
    base_url='https://api.mk13.com',
    session_token='your_session_token'
)

# Send AI message
response = client.ai.chat(
    message='Help me with my project',
    context_id='context_123'
)

# Get contexts
contexts = client.contexts.list()

# Send email
client.google.send_email(
    to=['<EMAIL>'],
    subject='Hello',
    body='Hello from MK13!'
)
```

This API documentation provides comprehensive coverage of all available endpoints, authentication methods, and usage examples for the MK13 MVP backend services.
