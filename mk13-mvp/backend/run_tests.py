#!/usr/bin/env python3
"""
Comprehensive Test Runner for MK13 MVP Backend
Runs all tests with coverage reporting and security checks
"""

import os
import sys
import subprocess
import argparse
import json
from pathlib import Path
from typing import Dict, List, Optional

def run_command(cmd: List[str], cwd: Optional[str] = None) -> tuple[int, str, str]:
    """Run a command and return exit code, stdout, stderr"""
    try:
        result = subprocess.run(
            cmd,
            cwd=cwd,
            capture_output=True,
            text=True,
            timeout=300  # 5 minutes timeout
        )
        return result.returncode, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return 1, "", "Command timed out"
    except Exception as e:
        return 1, "", str(e)

def check_dependencies():
    """Check if required testing dependencies are installed"""
    required_packages = [
        'pytest',
        'pytest-cov',
        'pytest-asyncio',
        'pytest-mock',
        'black',
        'flake8',
        'mypy',
        'bandit',
        'safety'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        exit_code, _, _ = run_command([sys.executable, '-m', 'pip', 'show', package])
        if exit_code != 0:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing required packages: {', '.join(missing_packages)}")
        print("Install them with: pip install " + " ".join(missing_packages))
        return False
    
    print("✅ All required testing dependencies are installed")
    return True

def run_code_formatting(fix: bool = False) -> bool:
    """Run code formatting checks"""
    print("\n🔧 Running code formatting checks...")
    
    # Black formatting
    black_cmd = [sys.executable, '-m', 'black']
    if not fix:
        black_cmd.append('--check')
    black_cmd.extend(['--line-length', '88', '.'])
    
    exit_code, stdout, stderr = run_command(black_cmd)
    
    if exit_code == 0:
        print("✅ Code formatting is correct")
        return True
    else:
        print("❌ Code formatting issues found")
        if fix:
            print("🔧 Fixed formatting issues")
            return True
        else:
            print("Run with --fix to automatically fix formatting")
            print(stdout)
            print(stderr)
            return False

def run_linting() -> bool:
    """Run code linting"""
    print("\n🔍 Running code linting...")
    
    # Flake8 linting
    flake8_cmd = [
        sys.executable, '-m', 'flake8',
        '--max-line-length', '88',
        '--extend-ignore', 'E203,W503',
        '.'
    ]
    
    exit_code, stdout, stderr = run_command(flake8_cmd)
    
    if exit_code == 0:
        print("✅ No linting issues found")
        return True
    else:
        print("❌ Linting issues found:")
        print(stdout)
        print(stderr)
        return False

def run_type_checking() -> bool:
    """Run type checking"""
    print("\n🔍 Running type checking...")
    
    mypy_cmd = [
        sys.executable, '-m', 'mypy',
        '--ignore-missing-imports',
        '--strict-optional',
        '.'
    ]
    
    exit_code, stdout, stderr = run_command(mypy_cmd)
    
    if exit_code == 0:
        print("✅ No type checking issues found")
        return True
    else:
        print("❌ Type checking issues found:")
        print(stdout)
        print(stderr)
        return False

def run_security_checks() -> bool:
    """Run security checks"""
    print("\n🔒 Running security checks...")
    
    success = True
    
    # Bandit security linting
    bandit_cmd = [
        sys.executable, '-m', 'bandit',
        '-r', '.',
        '-f', 'json',
        '-x', './venv,./node_modules'
    ]
    
    exit_code, stdout, stderr = run_command(bandit_cmd)
    
    if exit_code == 0:
        print("✅ No security issues found by Bandit")
    else:
        print("❌ Security issues found by Bandit:")
        try:
            bandit_results = json.loads(stdout)
            for result in bandit_results.get('results', []):
                print(f"  - {result['test_name']}: {result['issue_text']}")
                print(f"    File: {result['filename']}:{result['line_number']}")
        except json.JSONDecodeError:
            print(stdout)
            print(stderr)
        success = False
    
    # Safety check for known vulnerabilities
    safety_cmd = [sys.executable, '-m', 'safety', 'check', '--json']
    
    exit_code, stdout, stderr = run_command(safety_cmd)
    
    if exit_code == 0:
        print("✅ No known vulnerabilities found")
    else:
        print("❌ Known vulnerabilities found:")
        try:
            safety_results = json.loads(stdout)
            for vuln in safety_results:
                print(f"  - {vuln['package']}: {vuln['advisory']}")
        except json.JSONDecodeError:
            print(stdout)
            print(stderr)
        success = False
    
    return success

def run_unit_tests(coverage: bool = True, verbose: bool = False) -> bool:
    """Run unit tests"""
    print("\n🧪 Running unit tests...")
    
    pytest_cmd = [sys.executable, '-m', 'pytest']
    
    if coverage:
        pytest_cmd.extend([
            '--cov=.',
            '--cov-report=html:htmlcov',
            '--cov-report=xml:coverage.xml',
            '--cov-report=term-missing',
            '--cov-fail-under=70'
        ])
    
    if verbose:
        pytest_cmd.append('-v')
    
    pytest_cmd.extend([
        '--tb=short',
        '--strict-markers',
        '--disable-warnings'
    ])
    
    exit_code, stdout, stderr = run_command(pytest_cmd)
    
    if exit_code == 0:
        print("✅ All tests passed")
        if coverage:
            print("📊 Coverage report generated in htmlcov/")
        return True
    else:
        print("❌ Some tests failed:")
        print(stdout)
        print(stderr)
        return False

def run_integration_tests() -> bool:
    """Run integration tests"""
    print("\n🔗 Running integration tests...")
    
    # Check if test database is available
    test_db_url = os.getenv('TEST_DATABASE_URL')
    if not test_db_url:
        print("⚠️  No TEST_DATABASE_URL found, skipping integration tests")
        return True
    
    pytest_cmd = [
        sys.executable, '-m', 'pytest',
        'tests/integration/',
        '-v',
        '--tb=short'
    ]
    
    exit_code, stdout, stderr = run_command(pytest_cmd)
    
    if exit_code == 0:
        print("✅ All integration tests passed")
        return True
    else:
        print("❌ Some integration tests failed:")
        print(stdout)
        print(stderr)
        return False

def generate_test_report(results: Dict[str, bool]) -> None:
    """Generate a comprehensive test report"""
    print("\n📋 Test Report Summary")
    print("=" * 50)
    
    total_checks = len(results)
    passed_checks = sum(1 for result in results.values() if result)
    
    for check_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{check_name:<30} {status}")
    
    print("=" * 50)
    print(f"Total: {passed_checks}/{total_checks} checks passed")
    
    if passed_checks == total_checks:
        print("🎉 All checks passed! Your code is ready for deployment.")
    else:
        print("⚠️  Some checks failed. Please fix the issues before deployment.")
    
    # Generate JSON report
    report = {
        'timestamp': subprocess.check_output(['date', '-Iseconds']).decode().strip(),
        'total_checks': total_checks,
        'passed_checks': passed_checks,
        'success_rate': passed_checks / total_checks * 100,
        'results': results
    }
    
    with open('test_report.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"📄 Detailed report saved to test_report.json")

def main():
    """Main test runner function"""
    parser = argparse.ArgumentParser(description='MK13 MVP Backend Test Runner')
    parser.add_argument('--fix', action='store_true', help='Fix formatting issues automatically')
    parser.add_argument('--no-coverage', action='store_true', help='Skip coverage reporting')
    parser.add_argument('--verbose', action='store_true', help='Verbose test output')
    parser.add_argument('--quick', action='store_true', help='Run only essential checks')
    parser.add_argument('--security-only', action='store_true', help='Run only security checks')
    parser.add_argument('--tests-only', action='store_true', help='Run only tests')
    
    args = parser.parse_args()
    
    print("🚀 MK13 MVP Backend Test Runner")
    print("=" * 50)
    
    # Check dependencies first
    if not check_dependencies():
        sys.exit(1)
    
    results = {}
    
    if args.security_only:
        results['Security Checks'] = run_security_checks()
    elif args.tests_only:
        results['Unit Tests'] = run_unit_tests(
            coverage=not args.no_coverage,
            verbose=args.verbose
        )
        if not args.quick:
            results['Integration Tests'] = run_integration_tests()
    else:
        # Run all checks
        results['Code Formatting'] = run_code_formatting(fix=args.fix)
        
        if not args.quick:
            results['Code Linting'] = run_linting()
            results['Type Checking'] = run_type_checking()
        
        results['Security Checks'] = run_security_checks()
        results['Unit Tests'] = run_unit_tests(
            coverage=not args.no_coverage,
            verbose=args.verbose
        )
        
        if not args.quick:
            results['Integration Tests'] = run_integration_tests()
    
    # Generate report
    generate_test_report(results)
    
    # Exit with appropriate code
    if all(results.values()):
        sys.exit(0)
    else:
        sys.exit(1)

if __name__ == '__main__':
    main()
