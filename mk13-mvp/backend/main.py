"""
MK13 MVP FastAPI Backend
Personal AI Assistant System Backend
"""

from fastapi import FastAPI, HTTPException, Depends, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import socketio
import uvicorn
import os
from dotenv import load_dotenv
import logging
from datetime import datetime
from database import initialize_database, get_managers
from ai_service import initialize_ai_service, get_ai_service
from google_workspace import initialize_google_services, get_google_services
from celery_app import queue_ai_request, get_task_status, start_email_monitoring_for_user
from llm_pool import initialize_llm_pool
from llm_router import initialize_llm_router
from context_detection import initialize_context_detector
from proactive_preparation import initialize_preparation_engine
from context_hierarchy import initialize_context_hierarchy
from autonomy_system import initialize_autonomy_system
from security import initialize_security, get_security_manager, get_current_user, rate_limit
from auth import initialize_auth, get_auth_manager, get_current_user_from_session
from typing import Dict, Any

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="MK13 MVP Backend",
    description="Personal AI Assistant System Backend",
    version="1.0.0"
)

# Initialize security first
security_manager = initialize_security()
auth_manager = initialize_auth()

# Configure CORS with security settings
cors_config = security_manager.get_cors_config()
app.add_middleware(
    CORSMiddleware,
    **cors_config
)

# Add security headers middleware
@app.middleware("http")
async def add_security_headers(request: Request, call_next):
    response = await call_next(request)
    security_headers = security_manager.get_security_headers()
    for header, value in security_headers.items():
        response.headers[header] = value
    return response

# Initialize database and AI service on startup
@app.on_event("startup")
async def startup_event():
    """Initialize database connections and AI service on startup"""
    try:
        initialize_database()
        logger.info("Database initialized successfully")

        initialize_llm_pool()
        logger.info("LLM Pool initialized successfully")

        initialize_llm_router()
        logger.info("LLM Router initialized successfully")

        initialize_context_detector()
        logger.info("Context Detector initialized successfully")

        initialize_preparation_engine()
        logger.info("Preparation Engine initialized successfully")

        initialize_context_hierarchy()
        logger.info("Context Hierarchy initialized successfully")

        initialize_autonomy_system()
        logger.info("Autonomy System initialized successfully")

        initialize_ai_service()
        logger.info("AI service initialized successfully")

        initialize_google_services()
        logger.info("Google Workspace services initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize services: {e}")
        raise

# Create Socket.IO server
sio = socketio.AsyncServer(
    async_mode='asgi',
    cors_allowed_origins="*"
)

# Wrap FastAPI app with Socket.IO
socket_app = socketio.ASGIApp(sio, app)

# Socket.IO utility functions
async def cleanup_user_session(sid: str):
    """Clean up user session data"""
    try:
        # Remove from all rooms
        rooms = sio.manager.get_rooms(sid)
        for room in rooms:
            await sio.leave_room(sid, room)
        logger.info(f"Cleaned up session for {sid}")
    except Exception as e:
        logger.error(f"Error cleaning up session: {e}")

async def send_notification_to_user(user_id: str, notification_type: str, data: Dict[str, Any]):
    """Send notification to all connected clients of a user"""
    try:
        await sio.emit('notification', {
            'type': notification_type,
            'data': data,
            'timestamp': datetime.utcnow().isoformat()
        }, room=f"user_{user_id}")
        logger.info(f"Sent {notification_type} notification to user {user_id}")
    except Exception as e:
        logger.error(f"Error sending notification: {e}")

# Health check endpoint
@app.get("/")
async def root():
    """Root endpoint for health check"""
    return {"message": "MK13 MVP Backend is running", "status": "healthy"}

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "MK13 MVP Backend",
        "version": "1.0.0"
    }

# Socket.IO event handlers
@sio.event
async def connect(sid, environ):
    """Handle client connection"""
    logger.info(f"Client {sid} connected")
    await sio.emit('connection_response', {'status': 'connected'}, room=sid)

@sio.event
async def disconnect(sid):
    """Handle client disconnection"""
    logger.info(f"Client {sid} disconnected")
    # Clean up user session if needed
    await cleanup_user_session(sid)

@sio.event
async def join_user_room(sid, data):
    """Join user to their personal room for notifications"""
    try:
        user_id = data.get('user_id')
        if user_id:
            await sio.enter_room(sid, f"user_{user_id}")
            logger.info(f"Client {sid} joined room for user {user_id}")
            await sio.emit('room_joined', {'user_id': user_id}, room=sid)
        else:
            await sio.emit('error', {'message': 'User ID required'}, room=sid)
    except Exception as e:
        logger.error(f"Error joining user room: {e}")
        await sio.emit('error', {'message': 'Failed to join room'}, room=sid)

@sio.event
async def ai_chat_request(sid, data):
    """Handle AI chat request via Socket.IO"""
    try:
        user_id = data.get('user_id')
        message = data.get('message')
        context_id = data.get('context_id')

        if not user_id or not message:
            await sio.emit('error', {'message': 'User ID and message required'}, room=sid)
            return

        # Process AI request
        ai_service = get_ai_service()
        result = await ai_service.process_text_input(user_id, message, context_id)

        # Send response back to client
        await sio.emit('ai_response', {
            'response': result['response'],
            'suggestions': result['suggestions'],
            'context_id': result['context_id'],
            'timestamp': result['timestamp']
        }, room=sid)

        # Also send to user's room for other connected devices
        await sio.emit('ai_notification', {
            'type': 'ai_response',
            'data': result
        }, room=f"user_{user_id}")

    except Exception as e:
        logger.error(f"Error processing AI chat request: {e}")
        await sio.emit('error', {'message': 'Failed to process AI request'}, room=sid)

@sio.event
async def request_suggestions(sid, data):
    """Request proactive suggestions"""
    try:
        user_id = data.get('user_id')
        context_data = data.get('context_data', {})

        if not user_id:
            await sio.emit('error', {'message': 'User ID required'}, room=sid)
            return

        # Generate suggestions
        ai_service = get_ai_service()
        suggestions = await ai_service.generate_proactive_suggestions(user_id, context_data)

        # Send suggestions back
        await sio.emit('suggestions_response', {
            'suggestions': suggestions,
            'timestamp': datetime.utcnow().isoformat()
        }, room=sid)

    except Exception as e:
        logger.error(f"Error generating suggestions: {e}")
        await sio.emit('error', {'message': 'Failed to generate suggestions'}, room=sid)

@sio.event
async def feedback_submission(sid, data):
    """Handle feedback submission"""
    try:
        user_id = data.get('user_id')
        action_id = data.get('action_id')
        feedback_type = data.get('feedback_type')
        content = data.get('content', {})

        if not all([user_id, action_id, feedback_type]):
            await sio.emit('error', {'message': 'User ID, action ID, and feedback type required'}, room=sid)
            return

        # Store feedback
        managers = get_managers()
        feedback = await managers["feedback"].create_feedback(user_id, action_id, feedback_type, content)

        # Confirm feedback received
        await sio.emit('feedback_confirmed', {
            'feedback_id': feedback['id'],
            'timestamp': feedback['timestamp']
        }, room=sid)

    except Exception as e:
        logger.error(f"Error submitting feedback: {e}")
        await sio.emit('error', {'message': 'Failed to submit feedback'}, room=sid)

# API Routes

# Authentication Endpoints
@app.get("/api/auth/google")
async def google_auth_url(user_id: str = None):
    """Get Google OAuth authorization URL"""
    try:
        auth_data = auth_manager.generate_google_auth_url(user_id)
        return {"success": True, **auth_data}
    except Exception as e:
        logger.error(f"Error generating Google auth URL: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/auth/google/callback")
async def google_auth_callback(code: str, state: str):
    """Handle Google OAuth callback"""
    try:
        result = await auth_manager.handle_google_callback(code, state)
        return {"success": True, **result}
    except Exception as e:
        logger.error(f"Error handling Google callback: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/auth/logout")
async def logout(request: Request):
    """Logout user"""
    try:
        session_token = request.headers.get('X-Session-Token')
        if session_token:
            success = await auth_manager.logout(session_token)
            return {"success": success, "message": "Logged out successfully"}
        return {"success": False, "message": "No session token provided"}
    except Exception as e:
        logger.error(f"Error during logout: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/auth/me")
async def get_current_user_info(user_data: Dict[str, Any] = Depends(get_current_user_from_session)):
    """Get current user information"""
    try:
        return {"success": True, "user": user_data}
    except Exception as e:
        logger.error(f"Error getting user info: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# User Management Endpoints
@app.post("/api/users")
async def create_user(user_data: Dict[str, Any]):
    """Create a new user"""
    try:
        managers = get_managers()
        user = await managers["users"].create_user(
            email=user_data["email"],
            name=user_data["name"],
            google_token=user_data.get("google_token")
        )
        return {"success": True, "user": user}
    except Exception as e:
        logger.error(f"Error creating user: {e}")
        raise HTTPException(status_code=400, detail=str(e))

@app.get("/api/users/{email}")
async def get_user(email: str):
    """Get user by email"""
    try:
        managers = get_managers()
        user = await managers["users"].get_user_by_email(email)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        return {"success": True, "user": user}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting user: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Context Management Endpoints
@app.post("/api/contexts")
async def create_context(context_data: Dict[str, Any]):
    """Create a new context"""
    try:
        managers = get_managers()
        context = await managers["contexts"].create_context(
            user_id=context_data["user_id"],
            name=context_data["name"],
            state=context_data["state"],
            metadata=context_data.get("metadata")
        )
        return {"success": True, "context": context}
    except Exception as e:
        logger.error(f"Error creating context: {e}")
        raise HTTPException(status_code=400, detail=str(e))

@app.get("/api/users/{user_id}/contexts")
async def get_user_contexts(user_id: str):
    """Get all contexts for a user"""
    try:
        managers = get_managers()
        contexts = await managers["contexts"].get_user_contexts(user_id)
        return {"success": True, "contexts": contexts}
    except Exception as e:
        logger.error(f"Error getting user contexts: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Feedback Endpoints
@app.post("/api/feedback")
async def create_feedback(feedback_data: Dict[str, Any]):
    """Create feedback entry"""
    try:
        managers = get_managers()
        feedback = await managers["feedback"].create_feedback(
            user_id=feedback_data["user_id"],
            action_id=feedback_data["action_id"],
            feedback_type=feedback_data["feedback_type"],
            content=feedback_data.get("content")
        )
        return {"success": True, "feedback": feedback}
    except Exception as e:
        logger.error(f"Error creating feedback: {e}")
        raise HTTPException(status_code=400, detail=str(e))

# AI Interaction Endpoints
@app.post("/api/ai/chat")
@rate_limit('ai')
async def ai_chat(request: Request, request_data: Dict[str, Any], user_data: Dict[str, Any] = Depends(get_current_user_from_session)):
    """Process text input through AI"""
    try:
        ai_service = get_ai_service()
        result = await ai_service.process_text_input(
            user_id=user_data["user_id"],
            message=security_manager.sanitize_input(request_data["message"]),
            context_id=request_data.get("context_id")
        )
        return {"success": True, "result": result}
    except Exception as e:
        logger.error(f"Error in AI chat: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/ai/voice")
async def ai_voice(request_data: Dict[str, Any]):
    """Process voice input through AI (placeholder)"""
    try:
        ai_service = get_ai_service()
        result = await ai_service.process_voice_input(
            user_id=request_data["user_id"],
            audio_data=request_data.get("audio_data", b""),
            context_id=request_data.get("context_id")
        )
        return {"success": True, "result": result}
    except Exception as e:
        logger.error(f"Error in AI voice: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/ai/suggestions")
async def ai_suggestions(request_data: Dict[str, Any]):
    """Generate proactive suggestions"""
    try:
        ai_service = get_ai_service()
        suggestions = await ai_service.generate_proactive_suggestions(
            user_id=request_data["user_id"],
            context_data=request_data.get("context_data", {})
        )
        return {"success": True, "suggestions": suggestions}
    except Exception as e:
        logger.error(f"Error generating suggestions: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Google Workspace Integration Endpoints
@app.get("/api/auth/google")
async def google_auth(user_id: str):
    """Get Google OAuth authorization URL"""
    try:
        google_services = get_google_services()
        auth_url = google_services["workspace"].get_authorization_url(user_id)
        return {"success": True, "auth_url": auth_url}
    except Exception as e:
        logger.error(f"Error getting Google auth URL: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/auth/google/callback")
async def google_callback(code: str, state: str):
    """Handle Google OAuth callback"""
    try:
        google_services = get_google_services()
        result = await google_services["workspace"].handle_oauth_callback(code, state)
        return {"success": True, "result": result}
    except Exception as e:
        logger.error(f"Error handling Google callback: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/gmail/emails/{user_id}")
async def get_emails(user_id: str, max_results: int = 10):
    """Get recent emails for user"""
    try:
        google_services = get_google_services()
        emails = await google_services["gmail"].get_recent_emails(user_id, max_results)
        return {"success": True, "emails": emails}
    except Exception as e:
        logger.error(f"Error getting emails: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/gmail/send")
async def send_email(email_data: Dict[str, Any]):
    """Send email"""
    try:
        google_services = get_google_services()
        result = await google_services["gmail"].send_email(
            user_id=email_data["user_id"],
            to=email_data["to"],
            subject=email_data["subject"],
            body=email_data["body"]
        )
        return {"success": True, "result": result}
    except Exception as e:
        logger.error(f"Error sending email: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/calendar/events/{user_id}")
async def get_calendar_events(user_id: str, max_results: int = 10):
    """Get upcoming calendar events"""
    try:
        google_services = get_google_services()
        events = await google_services["calendar"].get_upcoming_events(user_id, max_results)
        return {"success": True, "events": events}
    except Exception as e:
        logger.error(f"Error getting calendar events: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/drive/files/{user_id}")
async def get_drive_files(user_id: str, max_results: int = 10):
    """Get recent Drive files"""
    try:
        google_services = get_google_services()
        files = await google_services["drive"].get_recent_files(user_id, max_results)
        return {"success": True, "files": files}
    except Exception as e:
        logger.error(f"Error getting Drive files: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Background Tasks / Celery Endpoints
@app.post("/api/tasks/ai-request")
async def queue_ai_task(request_data: Dict[str, Any]):
    """Queue an AI request for background processing"""
    try:
        task = queue_ai_request(
            user_id=request_data["user_id"],
            message=request_data["message"],
            context_id=request_data.get("context_id")
        )
        return {"success": True, "task_id": task.id}
    except Exception as e:
        logger.error(f"Error queuing AI task: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/tasks/{task_id}/status")
async def get_task_status_endpoint(task_id: str):
    """Get status of a background task"""
    try:
        status = get_task_status(task_id)
        return {"success": True, "task_status": status}
    except Exception as e:
        logger.error(f"Error getting task status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/tasks/email-monitoring")
async def start_email_monitoring(request_data: Dict[str, Any]):
    """Start email monitoring for a user"""
    try:
        task = start_email_monitoring_for_user(request_data["user_id"])
        return {"success": True, "task_id": task.id}
    except Exception as e:
        logger.error(f"Error starting email monitoring: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Real-time Communication Endpoints
@app.post("/api/notifications/send")
async def send_notification(notification_data: Dict[str, Any]):
    """Send real-time notification to user"""
    try:
        user_id = notification_data["user_id"]
        notification_type = notification_data["type"]
        data = notification_data.get("data", {})

        await send_notification_to_user(user_id, notification_type, data)
        return {"success": True, "message": "Notification sent"}
    except Exception as e:
        logger.error(f"Error sending notification: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/socketio/rooms")
async def get_active_rooms():
    """Get active Socket.IO rooms (for debugging)"""
    try:
        # This is a simplified version - in production you'd want proper room management
        return {"success": True, "message": "Room information would be available here"}
    except Exception as e:
        logger.error(f"Error getting rooms: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# LLM Pool Management Endpoints
@app.get("/api/llm/pool/status")
async def get_llm_pool_status():
    """Get LLM pool status and statistics"""
    try:
        from llm_pool import get_llm_pool
        from llm_router import get_llm_router

        pool = get_llm_pool()
        router = get_llm_router()

        pool_status = pool.get_pool_status()
        routing_stats = router.get_routing_stats()

        return {
            "success": True,
            "pool_status": pool_status,
            "routing_stats": routing_stats
        }
    except Exception as e:
        logger.error(f"Error getting LLM pool status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/llm/pool/mode")
async def set_operation_mode(request_data: Dict[str, Any]):
    """Set LLM pool operation mode"""
    try:
        from llm_pool import get_llm_pool, OperationMode

        mode_str = request_data.get("mode", "balanced")
        mode = OperationMode(mode_str)

        pool = get_llm_pool()
        pool.set_operation_mode(mode)

        return {"success": True, "mode": mode.value}
    except Exception as e:
        logger.error(f"Error setting operation mode: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/llm/test")
async def test_llm_providers(request_data: Dict[str, Any]):
    """Test LLM providers with a simple request"""
    try:
        from llm_pool import get_llm_pool, TaskType

        test_message = request_data.get("message", "Hello, please respond with 'Test successful'")
        task_type = TaskType(request_data.get("task_type", "real_time_chat"))

        pool = get_llm_pool()

        messages = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": test_message}
        ]

        response = await pool.generate_response(
            messages=messages,
            task_type=task_type,
            requirements={"max_tokens": 100}
        )

        return {"success": True, "response": response}
    except Exception as e:
        logger.error(f"Error testing LLM providers: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Context Management Endpoints
@app.get("/api/context/hierarchy/{user_id}")
async def get_context_hierarchy(user_id: str):
    """Get context hierarchy for user"""
    try:
        from context_hierarchy import get_context_hierarchy_manager

        hierarchy_manager = get_context_hierarchy_manager()
        hierarchy = hierarchy_manager.get_context_hierarchy(user_id)

        return {"success": True, "hierarchy": hierarchy}
    except Exception as e:
        logger.error(f"Error getting context hierarchy: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/context/switch")
async def switch_context(request_data: Dict[str, Any]):
    """Switch user context"""
    try:
        from context_hierarchy import get_context_hierarchy_manager

        user_id = request_data["user_id"]
        target_context_id = request_data["target_context_id"]
        trigger = request_data.get("trigger", "manual")
        force = request_data.get("force", False)

        hierarchy_manager = get_context_hierarchy_manager()
        result = await hierarchy_manager.switch_context(user_id, target_context_id, trigger, force)

        return {"success": True, "result": result}
    except Exception as e:
        logger.error(f"Error switching context: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/context/create")
async def create_context_hierarchy(request_data: Dict[str, Any]):
    """Create new context in hierarchy"""
    try:
        from context_hierarchy import get_context_hierarchy_manager, ContextPriority

        user_id = request_data["user_id"]
        context_name = request_data["context_name"]
        priority = ContextPriority(request_data.get("priority", "medium"))
        parent_context_id = request_data.get("parent_context_id")
        context_data = request_data.get("context_data", {})

        hierarchy_manager = get_context_hierarchy_manager()
        context_id = await hierarchy_manager.create_context_hierarchy(
            user_id, context_name, priority, parent_context_id, context_data
        )

        return {"success": True, "context_id": context_id}
    except Exception as e:
        logger.error(f"Error creating context hierarchy: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Autonomy System Endpoints
@app.post("/api/autonomy/evaluate")
async def evaluate_autonomous_action(request_data: Dict[str, Any]):
    """Evaluate whether to take an autonomous action"""
    try:
        from autonomy_system import get_autonomy_system, ActionType

        user_id = request_data["user_id"]
        action_type = ActionType(request_data["action_type"])
        action_data = request_data["action_data"]
        context = request_data.get("context", {})

        autonomy_system = get_autonomy_system()
        evaluation = await autonomy_system.evaluate_action(user_id, action_type, action_data, context)

        return {"success": True, "evaluation": evaluation}
    except Exception as e:
        logger.error(f"Error evaluating autonomous action: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/autonomy/execute")
async def execute_autonomous_action(request_data: Dict[str, Any]):
    """Execute an autonomous action"""
    try:
        from autonomy_system import get_autonomy_system, ActionType, AutonomyLevel

        user_id = request_data["user_id"]
        action_type = ActionType(request_data["action_type"])
        action_data = request_data["action_data"]
        autonomy_level = AutonomyLevel(request_data["autonomy_level"])
        confidence = request_data["confidence"]

        autonomy_system = get_autonomy_system()
        result = await autonomy_system.execute_action(
            user_id, action_type, action_data, autonomy_level, confidence
        )

        return {"success": True, "result": result}
    except Exception as e:
        logger.error(f"Error executing autonomous action: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/autonomy/stats/{user_id}")
async def get_autonomy_stats(user_id: str):
    """Get autonomy system statistics"""
    try:
        from autonomy_system import get_autonomy_system

        autonomy_system = get_autonomy_system()
        stats = autonomy_system.get_autonomy_stats(user_id)

        return {"success": True, "stats": stats}
    except Exception as e:
        logger.error(f"Error getting autonomy stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Context Detection Endpoints
@app.post("/api/context/detect")
async def detect_context(request_data: Dict[str, Any]):
    """Detect context based on trigger data"""
    try:
        from context_detection import get_context_detector, ContextTrigger

        trigger_type = ContextTrigger(request_data["trigger_type"])
        trigger_data = request_data["trigger_data"]
        user_id = request_data["user_id"]

        context_detector = get_context_detector()
        detected_contexts = await context_detector.detect_context(trigger_type, trigger_data, user_id)

        return {"success": True, "detected_contexts": [
            {
                "context_name": ctx.context_name,
                "confidence": ctx.confidence,
                "trigger": ctx.trigger.value,
                "suggested_actions": ctx.suggested_actions,
                "timestamp": ctx.timestamp.isoformat()
            }
            for ctx in detected_contexts
        ]}
    except Exception as e:
        logger.error(f"Error detecting context: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/context/detection/stats")
async def get_detection_stats():
    """Get context detection statistics"""
    try:
        from context_detection import get_context_detector

        context_detector = get_context_detector()
        stats = context_detector.get_detection_stats()

        return {"success": True, "stats": stats}
    except Exception as e:
        logger.error(f"Error getting detection stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    # Get configuration from environment variables
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", 8000))
    debug = os.getenv("DEBUG", "false").lower() == "true"
    
    logger.info(f"Starting MK13 MVP Backend on {host}:{port}")
    
    uvicorn.run(
        "main:socket_app",
        host=host,
        port=port,
        reload=debug,
        log_level="info"
    )
