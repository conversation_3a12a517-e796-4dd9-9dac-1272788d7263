"""
WebRTC Signaling Server for Real-time Collaboration
Handles peer-to-peer connection establishment for voice, video, and screen sharing
"""

import asyncio
import json
import logging
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass, asdict
from enum import Enum
import websockets
from websockets.server import WebSocketServerProtocol

logger = logging.getLogger(__name__)

class SignalingEventType(Enum):
    OFFER = "offer"
    ANSWER = "answer"
    ICE_CANDIDATE = "ice_candidate"
    PEER_JOINED = "peer_joined"
    PEER_LEFT = "peer_left"
    MEDIA_STATE_CHANGED = "media_state_changed"
    SCREEN_SHARE_STARTED = "screen_share_started"
    SCREEN_SHARE_STOPPED = "screen_share_stopped"

class MediaType(Enum):
    AUDIO = "audio"
    VIDEO = "video"
    SCREEN = "screen"
    DATA = "data"

@dataclass
class PeerConnection:
    peer_id: str
    user_id: str
    workspace_id: str
    websocket: WebSocketServerProtocol
    connected_at: datetime
    last_activity: datetime
    media_state: Dict[str, bool]  # audio, video, screen
    ice_candidates: List[Dict[str, Any]]
    connection_state: str

@dataclass
class SignalingRoom:
    room_id: str
    workspace_id: str
    created_at: datetime
    peers: Dict[str, PeerConnection]
    active_offers: Dict[str, Dict[str, Any]]
    media_constraints: Dict[str, Any]

class WebRTCSignalingServer:
    """WebRTC signaling server for peer-to-peer connections"""
    
    def __init__(self):
        self.signaling_rooms: Dict[str, SignalingRoom] = {}
        self.peer_connections: Dict[str, PeerConnection] = {}
        self.websocket_to_peer: Dict[WebSocketServerProtocol, str] = {}
        
        # Default media constraints
        self.default_constraints = {
            'audio': {
                'enabled': True,
                'echoCancellation': True,
                'noiseSuppression': True,
                'autoGainControl': True
            },
            'video': {
                'enabled': True,
                'width': {'min': 320, 'ideal': 1280, 'max': 1920},
                'height': {'min': 240, 'ideal': 720, 'max': 1080},
                'frameRate': {'min': 15, 'ideal': 30, 'max': 60}
            },
            'screen': {
                'enabled': True,
                'width': {'min': 640, 'ideal': 1920, 'max': 3840},
                'height': {'min': 480, 'ideal': 1080, 'max': 2160},
                'frameRate': {'min': 5, 'ideal': 15, 'max': 30}
            }
        }
        
    async def create_signaling_room(self, workspace_id: str, room_config: Dict[str, Any] = None) -> SignalingRoom:
        """Create a new signaling room for a workspace"""
        try:
            room_id = f"room_{workspace_id}_{str(uuid.uuid4())[:8]}"
            
            room = SignalingRoom(
                room_id=room_id,
                workspace_id=workspace_id,
                created_at=datetime.utcnow(),
                peers={},
                active_offers={},
                media_constraints=room_config or self.default_constraints
            )
            
            self.signaling_rooms[room_id] = room
            
            logger.info(f"Created signaling room {room_id} for workspace {workspace_id}")
            return room
            
        except Exception as e:
            logger.error(f"Failed to create signaling room: {e}")
            raise

    async def join_signaling_room(
        self, 
        room_id: str, 
        user_id: str, 
        websocket: WebSocketServerProtocol
    ) -> PeerConnection:
        """Join a signaling room"""
        try:
            if room_id not in self.signaling_rooms:
                raise ValueError(f"Signaling room {room_id} not found")
            
            room = self.signaling_rooms[room_id]
            peer_id = str(uuid.uuid4())
            
            # Create peer connection
            peer_connection = PeerConnection(
                peer_id=peer_id,
                user_id=user_id,
                workspace_id=room.workspace_id,
                websocket=websocket,
                connected_at=datetime.utcnow(),
                last_activity=datetime.utcnow(),
                media_state={
                    'audio': False,
                    'video': False,
                    'screen': False
                },
                ice_candidates=[],
                connection_state='new'
            )
            
            # Add to room and global tracking
            room.peers[peer_id] = peer_connection
            self.peer_connections[peer_id] = peer_connection
            self.websocket_to_peer[websocket] = peer_id
            
            # Notify other peers
            await self.broadcast_to_room(room_id, {
                'type': SignalingEventType.PEER_JOINED.value,
                'peer_id': peer_id,
                'user_id': user_id,
                'media_state': peer_connection.media_state,
                'timestamp': datetime.utcnow().isoformat()
            }, exclude_peer=peer_id)
            
            # Send room state to new peer
            await self.send_room_state(peer_connection, room)
            
            logger.info(f"Peer {peer_id} ({user_id}) joined room {room_id}")
            return peer_connection
            
        except Exception as e:
            logger.error(f"Failed to join signaling room: {e}")
            raise

    async def handle_signaling_message(self, websocket: WebSocketServerProtocol, message: str):
        """Handle incoming signaling message"""
        try:
            data = json.loads(message)
            message_type = data.get('type')
            
            # Get peer connection
            if websocket not in self.websocket_to_peer:
                logger.warning("Received message from unknown peer")
                return
            
            peer_id = self.websocket_to_peer[websocket]
            peer_connection = self.peer_connections[peer_id]
            
            # Update last activity
            peer_connection.last_activity = datetime.utcnow()
            
            # Handle different message types
            if message_type == SignalingEventType.OFFER.value:
                await self.handle_offer(peer_connection, data)
            elif message_type == SignalingEventType.ANSWER.value:
                await self.handle_answer(peer_connection, data)
            elif message_type == SignalingEventType.ICE_CANDIDATE.value:
                await self.handle_ice_candidate(peer_connection, data)
            elif message_type == SignalingEventType.MEDIA_STATE_CHANGED.value:
                await self.handle_media_state_change(peer_connection, data)
            elif message_type == SignalingEventType.SCREEN_SHARE_STARTED.value:
                await self.handle_screen_share_started(peer_connection, data)
            elif message_type == SignalingEventType.SCREEN_SHARE_STOPPED.value:
                await self.handle_screen_share_stopped(peer_connection, data)
            else:
                logger.warning(f"Unknown signaling message type: {message_type}")
                
        except json.JSONDecodeError:
            logger.warning(f"Invalid JSON in signaling message: {message}")
        except Exception as e:
            logger.error(f"Error handling signaling message: {e}")

    async def handle_offer(self, peer_connection: PeerConnection, data: Dict[str, Any]):
        """Handle WebRTC offer"""
        try:
            target_peer_id = data.get('target_peer_id')
            offer = data.get('offer')
            
            if not target_peer_id or not offer:
                logger.warning("Invalid offer message")
                return
            
            # Find target peer
            target_peer = self.peer_connections.get(target_peer_id)
            if not target_peer:
                logger.warning(f"Target peer {target_peer_id} not found")
                return
            
            # Store offer
            room = self.get_room_for_peer(peer_connection.peer_id)
            if room:
                offer_key = f"{peer_connection.peer_id}->{target_peer_id}"
                room.active_offers[offer_key] = {
                    'from_peer': peer_connection.peer_id,
                    'to_peer': target_peer_id,
                    'offer': offer,
                    'timestamp': datetime.utcnow().isoformat()
                }
            
            # Forward offer to target peer
            await self.send_to_peer(target_peer, {
                'type': SignalingEventType.OFFER.value,
                'from_peer_id': peer_connection.peer_id,
                'from_user_id': peer_connection.user_id,
                'offer': offer,
                'timestamp': datetime.utcnow().isoformat()
            })
            
            logger.info(f"Forwarded offer from {peer_connection.peer_id} to {target_peer_id}")
            
        except Exception as e:
            logger.error(f"Error handling offer: {e}")

    async def handle_answer(self, peer_connection: PeerConnection, data: Dict[str, Any]):
        """Handle WebRTC answer"""
        try:
            target_peer_id = data.get('target_peer_id')
            answer = data.get('answer')
            
            if not target_peer_id or not answer:
                logger.warning("Invalid answer message")
                return
            
            # Find target peer
            target_peer = self.peer_connections.get(target_peer_id)
            if not target_peer:
                logger.warning(f"Target peer {target_peer_id} not found")
                return
            
            # Forward answer to target peer
            await self.send_to_peer(target_peer, {
                'type': SignalingEventType.ANSWER.value,
                'from_peer_id': peer_connection.peer_id,
                'from_user_id': peer_connection.user_id,
                'answer': answer,
                'timestamp': datetime.utcnow().isoformat()
            })
            
            logger.info(f"Forwarded answer from {peer_connection.peer_id} to {target_peer_id}")
            
        except Exception as e:
            logger.error(f"Error handling answer: {e}")

    async def handle_ice_candidate(self, peer_connection: PeerConnection, data: Dict[str, Any]):
        """Handle ICE candidate"""
        try:
            target_peer_id = data.get('target_peer_id')
            candidate = data.get('candidate')
            
            if not target_peer_id or not candidate:
                logger.warning("Invalid ICE candidate message")
                return
            
            # Store ICE candidate
            peer_connection.ice_candidates.append({
                'candidate': candidate,
                'target_peer': target_peer_id,
                'timestamp': datetime.utcnow().isoformat()
            })
            
            # Find target peer
            target_peer = self.peer_connections.get(target_peer_id)
            if not target_peer:
                logger.warning(f"Target peer {target_peer_id} not found")
                return
            
            # Forward ICE candidate to target peer
            await self.send_to_peer(target_peer, {
                'type': SignalingEventType.ICE_CANDIDATE.value,
                'from_peer_id': peer_connection.peer_id,
                'from_user_id': peer_connection.user_id,
                'candidate': candidate,
                'timestamp': datetime.utcnow().isoformat()
            })
            
        except Exception as e:
            logger.error(f"Error handling ICE candidate: {e}")

    async def handle_media_state_change(self, peer_connection: PeerConnection, data: Dict[str, Any]):
        """Handle media state change (audio/video on/off)"""
        try:
            media_state = data.get('media_state', {})
            
            # Update peer's media state
            for media_type, enabled in media_state.items():
                if media_type in peer_connection.media_state:
                    peer_connection.media_state[media_type] = enabled
            
            # Broadcast to room
            room = self.get_room_for_peer(peer_connection.peer_id)
            if room:
                await self.broadcast_to_room(room.room_id, {
                    'type': SignalingEventType.MEDIA_STATE_CHANGED.value,
                    'peer_id': peer_connection.peer_id,
                    'user_id': peer_connection.user_id,
                    'media_state': peer_connection.media_state,
                    'timestamp': datetime.utcnow().isoformat()
                }, exclude_peer=peer_connection.peer_id)
            
            logger.info(f"Media state changed for peer {peer_connection.peer_id}: {media_state}")
            
        except Exception as e:
            logger.error(f"Error handling media state change: {e}")

    async def handle_screen_share_started(self, peer_connection: PeerConnection, data: Dict[str, Any]):
        """Handle screen share started"""
        try:
            # Update media state
            peer_connection.media_state['screen'] = True
            
            # Broadcast to room
            room = self.get_room_for_peer(peer_connection.peer_id)
            if room:
                await self.broadcast_to_room(room.room_id, {
                    'type': SignalingEventType.SCREEN_SHARE_STARTED.value,
                    'peer_id': peer_connection.peer_id,
                    'user_id': peer_connection.user_id,
                    'screen_info': data.get('screen_info', {}),
                    'timestamp': datetime.utcnow().isoformat()
                }, exclude_peer=peer_connection.peer_id)
            
            logger.info(f"Screen share started for peer {peer_connection.peer_id}")
            
        except Exception as e:
            logger.error(f"Error handling screen share started: {e}")

    async def handle_screen_share_stopped(self, peer_connection: PeerConnection, data: Dict[str, Any]):
        """Handle screen share stopped"""
        try:
            # Update media state
            peer_connection.media_state['screen'] = False
            
            # Broadcast to room
            room = self.get_room_for_peer(peer_connection.peer_id)
            if room:
                await self.broadcast_to_room(room.room_id, {
                    'type': SignalingEventType.SCREEN_SHARE_STOPPED.value,
                    'peer_id': peer_connection.peer_id,
                    'user_id': peer_connection.user_id,
                    'timestamp': datetime.utcnow().isoformat()
                }, exclude_peer=peer_connection.peer_id)
            
            logger.info(f"Screen share stopped for peer {peer_connection.peer_id}")
            
        except Exception as e:
            logger.error(f"Error handling screen share stopped: {e}")

    async def send_room_state(self, peer_connection: PeerConnection, room: SignalingRoom):
        """Send current room state to a peer"""
        try:
            # Get other peers in room
            other_peers = []
            for other_peer_id, other_peer in room.peers.items():
                if other_peer_id != peer_connection.peer_id:
                    other_peers.append({
                        'peer_id': other_peer_id,
                        'user_id': other_peer.user_id,
                        'media_state': other_peer.media_state,
                        'connection_state': other_peer.connection_state
                    })
            
            await self.send_to_peer(peer_connection, {
                'type': 'room_state',
                'room_id': room.room_id,
                'your_peer_id': peer_connection.peer_id,
                'peers': other_peers,
                'media_constraints': room.media_constraints,
                'timestamp': datetime.utcnow().isoformat()
            })
            
        except Exception as e:
            logger.error(f"Error sending room state: {e}")

    async def send_to_peer(self, peer_connection: PeerConnection, message: Dict[str, Any]):
        """Send message to a specific peer"""
        try:
            if peer_connection.websocket:
                await peer_connection.websocket.send(json.dumps(message))
        except websockets.exceptions.ConnectionClosed:
            logger.info(f"Peer {peer_connection.peer_id} connection closed")
            await self.handle_peer_disconnect(peer_connection)
        except Exception as e:
            logger.error(f"Error sending message to peer {peer_connection.peer_id}: {e}")

    async def broadcast_to_room(
        self, 
        room_id: str, 
        message: Dict[str, Any], 
        exclude_peer: Optional[str] = None
    ):
        """Broadcast message to all peers in a room"""
        try:
            if room_id not in self.signaling_rooms:
                return
            
            room = self.signaling_rooms[room_id]
            
            for peer_id, peer_connection in room.peers.items():
                if exclude_peer and peer_id == exclude_peer:
                    continue
                
                await self.send_to_peer(peer_connection, message)
                
        except Exception as e:
            logger.error(f"Error broadcasting to room {room_id}: {e}")

    async def handle_peer_disconnect(self, peer_connection: PeerConnection):
        """Handle peer disconnection"""
        try:
            peer_id = peer_connection.peer_id
            
            # Find and remove from room
            room = self.get_room_for_peer(peer_id)
            if room:
                if peer_id in room.peers:
                    del room.peers[peer_id]
                
                # Notify other peers
                await self.broadcast_to_room(room.room_id, {
                    'type': SignalingEventType.PEER_LEFT.value,
                    'peer_id': peer_id,
                    'user_id': peer_connection.user_id,
                    'timestamp': datetime.utcnow().isoformat()
                })
                
                # Clean up empty room
                if not room.peers:
                    del self.signaling_rooms[room.room_id]
                    logger.info(f"Cleaned up empty signaling room {room.room_id}")
            
            # Remove from global tracking
            if peer_id in self.peer_connections:
                del self.peer_connections[peer_id]
            
            if peer_connection.websocket in self.websocket_to_peer:
                del self.websocket_to_peer[peer_connection.websocket]
            
            logger.info(f"Peer {peer_id} disconnected")
            
        except Exception as e:
            logger.error(f"Error handling peer disconnect: {e}")

    def get_room_for_peer(self, peer_id: str) -> Optional[SignalingRoom]:
        """Get the room containing a specific peer"""
        for room in self.signaling_rooms.values():
            if peer_id in room.peers:
                return room
        return None

    async def cleanup_inactive_connections(self):
        """Clean up inactive connections"""
        try:
            current_time = datetime.utcnow()
            inactive_threshold = timedelta(minutes=30)
            
            inactive_peers = []
            
            for peer_id, peer_connection in self.peer_connections.items():
                if current_time - peer_connection.last_activity > inactive_threshold:
                    inactive_peers.append(peer_connection)
            
            for peer_connection in inactive_peers:
                await self.handle_peer_disconnect(peer_connection)
            
            if inactive_peers:
                logger.info(f"Cleaned up {len(inactive_peers)} inactive peer connections")
                
        except Exception as e:
            logger.error(f"Error cleaning up inactive connections: {e}")

# Global WebRTC signaling server instance
webrtc_signaling_server = None

def initialize_webrtc_signaling():
    """Initialize the global WebRTC signaling server"""
    global webrtc_signaling_server
    webrtc_signaling_server = WebRTCSignalingServer()
    return webrtc_signaling_server

def get_webrtc_signaling_server():
    """Get the global WebRTC signaling server instance"""
    return webrtc_signaling_server
