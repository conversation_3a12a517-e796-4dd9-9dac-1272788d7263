"""
Enhanced Context Intelligence System for MK13 MVP
Advanced context detection with cross-application awareness, temporal patterns, and device synchronization
"""

import asyncio
import json
import logging
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import numpy as np
from collections import defaultdict, deque
import psutil
import platform
from database import get_managers
from ai_service import get_ai_service

logger = logging.getLogger(__name__)

class ContextType(Enum):
    WORK = "work"
    PERSONAL = "personal"
    MEETING = "meeting"
    FOCUS = "focus"
    TRAVEL = "travel"
    BREAK = "break"
    LEARNING = "learning"
    CREATIVE = "creative"

class ContextConfidence(Enum):
    LOW = 0.3
    MEDIUM = 0.6
    HIGH = 0.8
    VERY_HIGH = 0.95

@dataclass
class ApplicationContext:
    name: str
    window_title: str
    process_id: int
    cpu_usage: float
    memory_usage: float
    active_time: float
    last_interaction: datetime
    category: str
    productivity_score: float

@dataclass
class TemporalPattern:
    pattern_id: str
    user_id: str
    context_type: ContextType
    time_patterns: Dict[str, Any]  # hour, day_of_week, etc.
    frequency: float
    confidence: float
    last_occurrence: datetime
    next_predicted: Optional[datetime]

@dataclass
class MeetingContext:
    meeting_id: str
    title: str
    participants: List[str]
    start_time: datetime
    end_time: datetime
    location: Optional[str]
    meeting_type: str  # video, in-person, phone
    agenda_topics: List[str]
    preparation_status: str
    context_relevance: float

@dataclass
class DeviceContext:
    device_id: str
    device_type: str  # desktop, mobile, tablet
    location: Optional[Dict[str, float]]
    network_info: Dict[str, Any]
    battery_level: Optional[float]
    is_charging: Optional[bool]
    active_applications: List[ApplicationContext]
    last_sync: datetime
    sync_status: str

@dataclass
class EnhancedContext:
    id: str
    user_id: str
    primary_type: ContextType
    confidence: float
    timestamp: datetime
    
    # Application awareness
    active_applications: List[ApplicationContext]
    dominant_application: Optional[ApplicationContext]
    application_transitions: List[Dict[str, Any]]
    
    # Temporal patterns
    temporal_patterns: List[TemporalPattern]
    time_of_day_factor: float
    day_of_week_factor: float
    seasonal_factor: float
    
    # Meeting context
    current_meeting: Optional[MeetingContext]
    upcoming_meetings: List[MeetingContext]
    meeting_preparation_needed: bool
    
    # Cross-device context
    device_contexts: List[DeviceContext]
    primary_device: Optional[DeviceContext]
    context_continuity: Dict[str, Any]
    
    # Environmental factors
    location_context: Optional[Dict[str, Any]]
    weather_context: Optional[Dict[str, Any]]
    calendar_context: Dict[str, Any]
    
    # AI insights
    ai_insights: Dict[str, Any]
    predicted_next_context: Optional[ContextType]
    context_stability: float
    interruption_likelihood: float

class EnhancedContextIntelligence:
    """Advanced context intelligence system with cross-application awareness and temporal patterns"""
    
    def __init__(self):
        self.active_contexts: Dict[str, EnhancedContext] = {}
        self.temporal_patterns: Dict[str, List[TemporalPattern]] = defaultdict(list)
        self.application_monitor = ApplicationMonitor()
        self.meeting_analyzer = MeetingAnalyzer()
        self.device_synchronizer = DeviceSynchronizer()
        self.pattern_learner = TemporalPatternLearner()
        self.ai_service = None
        
        # Context history for pattern learning
        self.context_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        
        # Application categories for productivity scoring
        self.app_categories = {
            'development': ['code', 'terminal', 'git', 'docker', 'kubernetes'],
            'communication': ['slack', 'teams', 'zoom', 'email', 'discord'],
            'productivity': ['notion', 'obsidian', 'excel', 'word', 'sheets'],
            'design': ['figma', 'photoshop', 'sketch', 'canva'],
            'browser': ['chrome', 'firefox', 'safari', 'edge'],
            'entertainment': ['spotify', 'youtube', 'netflix', 'games'],
            'system': ['finder', 'explorer', 'settings', 'control panel']
        }
        
    async def initialize(self):
        """Initialize the enhanced context intelligence system"""
        try:
            self.ai_service = get_ai_service()
            await self.application_monitor.initialize()
            await self.meeting_analyzer.initialize()
            await self.device_synchronizer.initialize()
            await self.pattern_learner.initialize()
            
            # Load existing temporal patterns
            await self.load_temporal_patterns()
            
            logger.info("Enhanced Context Intelligence System initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Enhanced Context Intelligence: {e}")
            raise

    async def detect_enhanced_context(self, user_id: str) -> EnhancedContext:
        """Detect comprehensive context with all intelligence layers"""
        try:
            # Gather all context data
            app_contexts = await self.application_monitor.get_current_applications()
            meeting_context = await self.meeting_analyzer.get_current_meeting_context(user_id)
            device_contexts = await self.device_synchronizer.get_device_contexts(user_id)
            temporal_data = await self.pattern_learner.analyze_temporal_patterns(user_id)
            
            # Determine primary context type
            primary_type, confidence = await self.determine_primary_context(
                app_contexts, meeting_context, temporal_data
            )
            
            # Get AI insights
            ai_insights = await self.generate_ai_insights(
                user_id, app_contexts, meeting_context, temporal_data
            )
            
            # Create enhanced context
            enhanced_context = EnhancedContext(
                id=str(uuid.uuid4()),
                user_id=user_id,
                primary_type=primary_type,
                confidence=confidence,
                timestamp=datetime.utcnow(),
                
                # Application data
                active_applications=app_contexts,
                dominant_application=self.get_dominant_application(app_contexts),
                application_transitions=await self.get_application_transitions(user_id),
                
                # Temporal data
                temporal_patterns=self.temporal_patterns.get(user_id, []),
                time_of_day_factor=self.calculate_time_factor(),
                day_of_week_factor=self.calculate_day_factor(),
                seasonal_factor=self.calculate_seasonal_factor(),
                
                # Meeting data
                current_meeting=meeting_context.get('current'),
                upcoming_meetings=meeting_context.get('upcoming', []),
                meeting_preparation_needed=meeting_context.get('preparation_needed', False),
                
                # Device data
                device_contexts=device_contexts,
                primary_device=self.get_primary_device(device_contexts),
                context_continuity=await self.analyze_context_continuity(user_id, device_contexts),
                
                # Environmental data
                location_context=await self.get_location_context(user_id),
                weather_context=await self.get_weather_context(user_id),
                calendar_context=await self.get_calendar_context(user_id),
                
                # AI insights
                ai_insights=ai_insights,
                predicted_next_context=ai_insights.get('predicted_next'),
                context_stability=ai_insights.get('stability', 0.5),
                interruption_likelihood=ai_insights.get('interruption_likelihood', 0.3)
            )
            
            # Store context and update patterns
            await self.store_context(enhanced_context)
            await self.update_temporal_patterns(enhanced_context)
            
            # Update active contexts
            self.active_contexts[user_id] = enhanced_context
            
            return enhanced_context
            
        except Exception as e:
            logger.error(f"Failed to detect enhanced context for user {user_id}: {e}")
            raise

    async def determine_primary_context(
        self, 
        app_contexts: List[ApplicationContext],
        meeting_context: Dict[str, Any],
        temporal_data: Dict[str, Any]
    ) -> Tuple[ContextType, float]:
        """Determine the primary context type with confidence score"""
        
        context_scores = defaultdict(float)
        
        # Meeting context has highest priority
        if meeting_context.get('current'):
            return ContextType.MEETING, ContextConfidence.VERY_HIGH.value
        
        # Application-based context detection
        for app in app_contexts:
            if app.category == 'development':
                context_scores[ContextType.WORK] += app.productivity_score * 0.8
            elif app.category == 'communication':
                context_scores[ContextType.WORK] += app.productivity_score * 0.6
            elif app.category == 'productivity':
                context_scores[ContextType.WORK] += app.productivity_score * 0.7
            elif app.category == 'design':
                context_scores[ContextType.CREATIVE] += app.productivity_score * 0.8
            elif app.category == 'entertainment':
                context_scores[ContextType.BREAK] += app.productivity_score * 0.9
        
        # Temporal pattern influence
        current_hour = datetime.now().hour
        current_day = datetime.now().weekday()
        
        for pattern in temporal_data.get('patterns', []):
            if self.matches_temporal_pattern(pattern, current_hour, current_day):
                context_scores[pattern.context_type] += pattern.confidence * 0.5
        
        # Determine primary context
        if not context_scores:
            return ContextType.PERSONAL, ContextConfidence.LOW.value
        
        primary_context = max(context_scores.items(), key=lambda x: x[1])
        confidence = min(primary_context[1], ContextConfidence.VERY_HIGH.value)
        
        return primary_context[0], confidence

    async def generate_ai_insights(
        self,
        user_id: str,
        app_contexts: List[ApplicationContext],
        meeting_context: Dict[str, Any],
        temporal_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate AI-powered insights about the current context"""
        
        try:
            # Prepare context data for AI analysis
            context_summary = {
                'active_applications': [
                    {
                        'name': app.name,
                        'category': app.category,
                        'active_time': app.active_time,
                        'productivity_score': app.productivity_score
                    }
                    for app in app_contexts
                ],
                'current_time': datetime.now().isoformat(),
                'meeting_status': 'in_meeting' if meeting_context.get('current') else 'no_meeting',
                'upcoming_meetings': len(meeting_context.get('upcoming', [])),
                'historical_patterns': [
                    {
                        'context_type': pattern.context_type.value,
                        'frequency': pattern.frequency,
                        'confidence': pattern.confidence
                    }
                    for pattern in temporal_data.get('patterns', [])
                ]
            }
            
            # Generate AI insights
            prompt = f"""
            Analyze the following user context data and provide insights:
            
            Context Data: {json.dumps(context_summary, indent=2)}
            
            Please provide:
            1. Predicted next context type
            2. Context stability score (0-1)
            3. Interruption likelihood (0-1)
            4. Productivity recommendations
            5. Potential context switches in the next hour
            
            Respond in JSON format.
            """
            
            ai_response = await self.ai_service.process_text_input(
                user_id=user_id,
                message=prompt,
                context_id=None
            )
            
            # Parse AI response
            try:
                insights = json.loads(ai_response['response'])
            except json.JSONDecodeError:
                # Fallback to basic insights
                insights = {
                    'predicted_next': None,
                    'stability': 0.5,
                    'interruption_likelihood': 0.3,
                    'recommendations': [],
                    'potential_switches': []
                }
            
            return insights
            
        except Exception as e:
            logger.error(f"Failed to generate AI insights: {e}")
            return {
                'predicted_next': None,
                'stability': 0.5,
                'interruption_likelihood': 0.3,
                'recommendations': [],
                'potential_switches': []
            }

    def get_dominant_application(self, app_contexts: List[ApplicationContext]) -> Optional[ApplicationContext]:
        """Get the most dominant application based on usage metrics"""
        if not app_contexts:
            return None
        
        # Score applications based on active time and CPU usage
        scored_apps = []
        for app in app_contexts:
            score = (app.active_time * 0.6) + (app.cpu_usage * 0.3) + (app.productivity_score * 0.1)
            scored_apps.append((app, score))
        
        return max(scored_apps, key=lambda x: x[1])[0]

    def calculate_time_factor(self) -> float:
        """Calculate time of day influence factor"""
        current_hour = datetime.now().hour
        
        # Work hours (9-17) have higher work context probability
        if 9 <= current_hour <= 17:
            return 0.8
        # Evening hours (18-22) mixed context
        elif 18 <= current_hour <= 22:
            return 0.5
        # Night/early morning (23-8) personal/break context
        else:
            return 0.2

    def calculate_day_factor(self) -> float:
        """Calculate day of week influence factor"""
        current_day = datetime.now().weekday()
        
        # Monday-Friday: work context more likely
        if 0 <= current_day <= 4:
            return 0.8
        # Weekend: personal context more likely
        else:
            return 0.3

    def calculate_seasonal_factor(self) -> float:
        """Calculate seasonal influence factor"""
        current_month = datetime.now().month
        
        # Adjust for seasonal patterns (simplified)
        if current_month in [12, 1, 2]:  # Winter
            return 0.6
        elif current_month in [6, 7, 8]:  # Summer
            return 0.4
        else:  # Spring/Fall
            return 0.7

    async def store_context(self, context: EnhancedContext):
        """Store enhanced context in database"""
        try:
            managers = get_managers()
            
            context_data = {
                'id': context.id,
                'user_id': context.user_id,
                'primary_type': context.primary_type.value,
                'confidence': context.confidence,
                'timestamp': context.timestamp.isoformat(),
                'context_data': json.dumps(asdict(context)),
                'ai_insights': json.dumps(context.ai_insights)
            }
            
            await managers['contexts'].create_context(
                user_id=context.user_id,
                name=f"Enhanced Context - {context.primary_type.value}",
                state=context_data,
                metadata={'enhanced': True, 'version': '2.0'}
            )
            
            # Add to history for pattern learning
            self.context_history[context.user_id].append(context)
            
        except Exception as e:
            logger.error(f"Failed to store enhanced context: {e}")

    async def update_temporal_patterns(self, context: EnhancedContext):
        """Update temporal patterns based on new context"""
        try:
            await self.pattern_learner.update_patterns(context)
        except Exception as e:
            logger.error(f"Failed to update temporal patterns: {e}")

# Global enhanced context intelligence instance
enhanced_context_intelligence = None

async def initialize_enhanced_context_intelligence():
    """Initialize the global enhanced context intelligence system"""
    global enhanced_context_intelligence
    enhanced_context_intelligence = EnhancedContextIntelligence()
    await enhanced_context_intelligence.initialize()
    return enhanced_context_intelligence

def get_enhanced_context_intelligence():
    """Get the global enhanced context intelligence instance"""
    return enhanced_context_intelligence
