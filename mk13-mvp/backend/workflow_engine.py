"""
Advanced Workflow Automation Engine for MK13 MVP
Provides visual workflow builder with AI-powered suggestions and execution
"""

import json
import uuid
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
from enum import Enum
import logging
from database import get_managers
from ai_service import get_ai_service
from google_workspace import get_google_services

logger = logging.getLogger(__name__)

class TriggerType(Enum):
    CONTEXT_CHANGE = "context_change"
    TIME_BASED = "time_based"
    EMAIL_RECEIVED = "email_received"
    CALENDAR_EVENT = "calendar_event"
    VOICE_COMMAND = "voice_command"
    MANUAL = "manual"
    WEBHOOK = "webhook"
    FILE_CHANGE = "file_change"
    LOCATION_CHANGE = "location_change"

class ActionType(Enum):
    SEND_EMAIL = "send_email"
    CREATE_CALENDAR_EVENT = "create_calendar_event"
    AI_RESPONSE = "ai_response"
    NOTIFICATION = "notification"
    API_CALL = "api_call"
    FILE_OPERATION = "file_operation"
    CONTEXT_SWITCH = "context_switch"
    DELAY = "delay"
    CONDITION = "condition"
    LOOP = "loop"

@dataclass
class WorkflowNode:
    id: str
    type: str
    name: str
    description: str
    parameters: Dict[str, Any]
    position: Dict[str, float]  # x, y coordinates for visual editor
    connections: List[str]  # IDs of connected nodes

@dataclass
class WorkflowTrigger:
    type: TriggerType
    conditions: Dict[str, Any]
    enabled: bool = True

@dataclass
class Workflow:
    id: str
    name: str
    description: str
    user_id: str
    trigger: WorkflowTrigger
    nodes: List[WorkflowNode]
    enabled: bool
    created_at: datetime
    updated_at: datetime
    last_run: Optional[datetime] = None
    run_count: int = 0
    success_rate: float = 0.0
    tags: List[str] = None

    def __post_init__(self):
        if self.tags is None:
            self.tags = []

class WorkflowEngine:
    """Advanced workflow automation engine with visual builder support"""
    
    def __init__(self):
        self.active_workflows: Dict[str, Workflow] = {}
        self.running_executions: Dict[str, Dict] = {}
        self.ai_service = None
        self.google_services = None
        
    async def initialize(self):
        """Initialize the workflow engine"""
        try:
            self.ai_service = get_ai_service()
            self.google_services = get_google_services()
            await self.load_active_workflows()
            logger.info("Workflow engine initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize workflow engine: {e}")
            raise

    async def load_active_workflows(self):
        """Load all active workflows from database"""
        try:
            managers = get_managers()
            workflows_data = await managers['workflows'].get_active_workflows()
            
            for workflow_data in workflows_data:
                workflow = self._deserialize_workflow(workflow_data)
                self.active_workflows[workflow.id] = workflow
                
            logger.info(f"Loaded {len(self.active_workflows)} active workflows")
        except Exception as e:
            logger.error(f"Failed to load workflows: {e}")

    def _deserialize_workflow(self, data: Dict) -> Workflow:
        """Convert database data to Workflow object"""
        trigger = WorkflowTrigger(
            type=TriggerType(data['trigger']['type']),
            conditions=data['trigger']['conditions'],
            enabled=data['trigger'].get('enabled', True)
        )
        
        nodes = [
            WorkflowNode(
                id=node['id'],
                type=node['type'],
                name=node['name'],
                description=node['description'],
                parameters=node['parameters'],
                position=node['position'],
                connections=node['connections']
            )
            for node in data['nodes']
        ]
        
        return Workflow(
            id=data['id'],
            name=data['name'],
            description=data['description'],
            user_id=data['user_id'],
            trigger=trigger,
            nodes=nodes,
            enabled=data['enabled'],
            created_at=datetime.fromisoformat(data['created_at']),
            updated_at=datetime.fromisoformat(data['updated_at']),
            last_run=datetime.fromisoformat(data['last_run']) if data.get('last_run') else None,
            run_count=data.get('run_count', 0),
            success_rate=data.get('success_rate', 0.0),
            tags=data.get('tags', [])
        )

    async def create_workflow(self, user_id: str, workflow_data: Dict) -> Workflow:
        """Create a new workflow"""
        try:
            workflow_id = str(uuid.uuid4())
            now = datetime.utcnow()
            
            # Validate workflow structure
            await self._validate_workflow_structure(workflow_data)
            
            # Create workflow object
            workflow = Workflow(
                id=workflow_id,
                name=workflow_data['name'],
                description=workflow_data['description'],
                user_id=user_id,
                trigger=WorkflowTrigger(
                    type=TriggerType(workflow_data['trigger']['type']),
                    conditions=workflow_data['trigger']['conditions'],
                    enabled=workflow_data['trigger'].get('enabled', True)
                ),
                nodes=[
                    WorkflowNode(**node) for node in workflow_data['nodes']
                ],
                enabled=workflow_data.get('enabled', True),
                created_at=now,
                updated_at=now,
                tags=workflow_data.get('tags', [])
            )
            
            # Save to database
            managers = get_managers()
            await managers['workflows'].create_workflow(asdict(workflow))
            
            # Add to active workflows if enabled
            if workflow.enabled:
                self.active_workflows[workflow_id] = workflow
            
            logger.info(f"Created workflow {workflow_id} for user {user_id}")
            return workflow
            
        except Exception as e:
            logger.error(f"Failed to create workflow: {e}")
            raise

    async def update_workflow(self, workflow_id: str, updates: Dict) -> Workflow:
        """Update an existing workflow"""
        try:
            managers = get_managers()
            
            # Validate updates
            if 'nodes' in updates:
                await self._validate_workflow_structure(updates)
            
            # Update in database
            updates['updated_at'] = datetime.utcnow().isoformat()
            await managers['workflows'].update_workflow(workflow_id, updates)
            
            # Reload workflow
            workflow_data = await managers['workflows'].get_workflow(workflow_id)
            workflow = self._deserialize_workflow(workflow_data)
            
            # Update active workflows
            if workflow.enabled:
                self.active_workflows[workflow_id] = workflow
            elif workflow_id in self.active_workflows:
                del self.active_workflows[workflow_id]
            
            logger.info(f"Updated workflow {workflow_id}")
            return workflow
            
        except Exception as e:
            logger.error(f"Failed to update workflow {workflow_id}: {e}")
            raise

    async def delete_workflow(self, workflow_id: str):
        """Delete a workflow"""
        try:
            managers = get_managers()
            await managers['workflows'].delete_workflow(workflow_id)
            
            # Remove from active workflows
            if workflow_id in self.active_workflows:
                del self.active_workflows[workflow_id]
            
            logger.info(f"Deleted workflow {workflow_id}")
            
        except Exception as e:
            logger.error(f"Failed to delete workflow {workflow_id}: {e}")
            raise

    async def execute_workflow(self, workflow_id: str, trigger_data: Dict = None) -> Dict:
        """Execute a workflow"""
        try:
            if workflow_id not in self.active_workflows:
                raise ValueError(f"Workflow {workflow_id} not found or not active")
            
            workflow = self.active_workflows[workflow_id]
            execution_id = str(uuid.uuid4())
            
            # Create execution context
            execution_context = {
                'id': execution_id,
                'workflow_id': workflow_id,
                'user_id': workflow.user_id,
                'started_at': datetime.utcnow(),
                'trigger_data': trigger_data or {},
                'variables': {},
                'current_node': None,
                'status': 'running',
                'results': [],
                'errors': []
            }
            
            self.running_executions[execution_id] = execution_context
            
            try:
                # Execute workflow nodes
                result = await self._execute_workflow_nodes(workflow, execution_context)
                
                # Update execution status
                execution_context['status'] = 'completed'
                execution_context['completed_at'] = datetime.utcnow()
                
                # Update workflow statistics
                await self._update_workflow_stats(workflow_id, True)
                
                logger.info(f"Successfully executed workflow {workflow_id}")
                return result
                
            except Exception as e:
                execution_context['status'] = 'failed'
                execution_context['error'] = str(e)
                execution_context['completed_at'] = datetime.utcnow()
                
                # Update workflow statistics
                await self._update_workflow_stats(workflow_id, False)
                
                logger.error(f"Workflow {workflow_id} execution failed: {e}")
                raise
            
            finally:
                # Clean up execution context
                if execution_id in self.running_executions:
                    del self.running_executions[execution_id]
                    
        except Exception as e:
            logger.error(f"Failed to execute workflow {workflow_id}: {e}")
            raise

    async def _execute_workflow_nodes(self, workflow: Workflow, context: Dict) -> Dict:
        """Execute workflow nodes in sequence"""
        results = []
        
        # Find start node (node with no incoming connections)
        start_nodes = [
            node for node in workflow.nodes 
            if not any(node.id in other.connections for other in workflow.nodes)
        ]
        
        if not start_nodes:
            raise ValueError("No start node found in workflow")
        
        # Execute from start node
        for start_node in start_nodes:
            result = await self._execute_node(start_node, workflow, context)
            results.append(result)
        
        return {
            'execution_id': context['id'],
            'workflow_id': workflow.id,
            'results': results,
            'variables': context['variables']
        }

    async def _execute_node(self, node: WorkflowNode, workflow: Workflow, context: Dict) -> Dict:
        """Execute a single workflow node"""
        context['current_node'] = node.id
        
        try:
            logger.info(f"Executing node {node.id} ({node.type})")
            
            # Execute based on node type
            if node.type == ActionType.SEND_EMAIL.value:
                result = await self._execute_email_action(node, context)
            elif node.type == ActionType.CREATE_CALENDAR_EVENT.value:
                result = await self._execute_calendar_action(node, context)
            elif node.type == ActionType.AI_RESPONSE.value:
                result = await self._execute_ai_action(node, context)
            elif node.type == ActionType.NOTIFICATION.value:
                result = await self._execute_notification_action(node, context)
            elif node.type == ActionType.API_CALL.value:
                result = await self._execute_api_action(node, context)
            elif node.type == ActionType.CONDITION.value:
                result = await self._execute_condition_action(node, context)
            elif node.type == ActionType.DELAY.value:
                result = await self._execute_delay_action(node, context)
            else:
                raise ValueError(f"Unknown node type: {node.type}")
            
            # Store result in context
            context['variables'][f"node_{node.id}"] = result
            context['results'].append({
                'node_id': node.id,
                'node_name': node.name,
                'result': result,
                'timestamp': datetime.utcnow().isoformat()
            })
            
            # Execute connected nodes
            for connection_id in node.connections:
                connected_node = next(
                    (n for n in workflow.nodes if n.id == connection_id), None
                )
                if connected_node:
                    await self._execute_node(connected_node, workflow, context)
            
            return result
            
        except Exception as e:
            error_msg = f"Node {node.id} execution failed: {e}"
            context['errors'].append({
                'node_id': node.id,
                'error': error_msg,
                'timestamp': datetime.utcnow().isoformat()
            })
            logger.error(error_msg)
            raise

    async def _execute_email_action(self, node: WorkflowNode, context: Dict) -> Dict:
        """Execute email sending action"""
        params = node.parameters
        
        # Process template variables
        to_email = self._process_template(params.get('to', ''), context)
        subject = self._process_template(params.get('subject', ''), context)
        body = self._process_template(params.get('body', ''), context)
        
        # Send email via Google Workspace
        result = await self.google_services.send_email(
            to=[to_email],
            subject=subject,
            body=body,
            user_id=context['user_id']
        )
        
        return {
            'action': 'email_sent',
            'to': to_email,
            'subject': subject,
            'message_id': result.get('id')
        }

    async def _execute_ai_action(self, node: WorkflowNode, context: Dict) -> Dict:
        """Execute AI response action"""
        params = node.parameters
        
        # Process prompt template
        prompt = self._process_template(params.get('prompt', ''), context)
        
        # Get AI response
        result = await self.ai_service.process_text_input(
            user_id=context['user_id'],
            message=prompt,
            context_id=context.get('context_id')
        )
        
        return {
            'action': 'ai_response',
            'prompt': prompt,
            'response': result['response'],
            'model': result.get('model_used')
        }

    def _process_template(self, template: str, context: Dict) -> str:
        """Process template variables in strings"""
        # Simple template processing - replace {{variable}} with values
        import re
        
        def replace_var(match):
            var_name = match.group(1)
            if var_name in context['variables']:
                return str(context['variables'][var_name])
            elif var_name in context['trigger_data']:
                return str(context['trigger_data'][var_name])
            return match.group(0)  # Return original if not found
        
        return re.sub(r'\{\{(\w+)\}\}', replace_var, template)

    async def _validate_workflow_structure(self, workflow_data: Dict):
        """Validate workflow structure"""
        # Check required fields
        required_fields = ['name', 'trigger', 'nodes']
        for field in required_fields:
            if field not in workflow_data:
                raise ValueError(f"Missing required field: {field}")
        
        # Validate trigger
        trigger = workflow_data['trigger']
        if 'type' not in trigger:
            raise ValueError("Trigger must have a type")
        
        try:
            TriggerType(trigger['type'])
        except ValueError:
            raise ValueError(f"Invalid trigger type: {trigger['type']}")
        
        # Validate nodes
        nodes = workflow_data['nodes']
        if not nodes:
            raise ValueError("Workflow must have at least one node")
        
        node_ids = set()
        for node in nodes:
            if 'id' not in node:
                raise ValueError("Node must have an id")
            
            if node['id'] in node_ids:
                raise ValueError(f"Duplicate node id: {node['id']}")
            
            node_ids.add(node['id'])
            
            # Validate node connections
            for connection_id in node.get('connections', []):
                if connection_id not in node_ids and connection_id not in [n['id'] for n in nodes]:
                    raise ValueError(f"Invalid connection: {connection_id}")

    async def _update_workflow_stats(self, workflow_id: str, success: bool):
        """Update workflow execution statistics"""
        try:
            managers = get_managers()
            workflow = self.active_workflows.get(workflow_id)
            
            if workflow:
                workflow.run_count += 1
                workflow.last_run = datetime.utcnow()
                
                # Update success rate
                if success:
                    workflow.success_rate = (
                        (workflow.success_rate * (workflow.run_count - 1) + 1) / workflow.run_count
                    )
                else:
                    workflow.success_rate = (
                        workflow.success_rate * (workflow.run_count - 1) / workflow.run_count
                    )
                
                # Save to database
                await managers['workflows'].update_workflow(workflow_id, {
                    'run_count': workflow.run_count,
                    'last_run': workflow.last_run.isoformat(),
                    'success_rate': workflow.success_rate
                })
                
        except Exception as e:
            logger.error(f"Failed to update workflow stats: {e}")

# Global workflow engine instance
workflow_engine = None

async def initialize_workflow_engine():
    """Initialize the global workflow engine"""
    global workflow_engine
    workflow_engine = WorkflowEngine()
    await workflow_engine.initialize()
    return workflow_engine

def get_workflow_engine():
    """Get the global workflow engine instance"""
    return workflow_engine
