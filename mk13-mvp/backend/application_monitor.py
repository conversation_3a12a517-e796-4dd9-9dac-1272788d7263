"""
Application Monitor for Enhanced Context Intelligence
Cross-application awareness and activity tracking
"""

import asyncio
import psutil
import platform
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from collections import defaultdict, deque
import subprocess
import json

logger = logging.getLogger(__name__)

@dataclass
class ApplicationContext:
    name: str
    window_title: str
    process_id: int
    cpu_usage: float
    memory_usage: float
    active_time: float
    last_interaction: datetime
    category: str
    productivity_score: float

class ApplicationMonitor:
    """Monitor and analyze application usage for context detection"""
    
    def __init__(self):
        self.active_applications: Dict[int, ApplicationContext] = {}
        self.application_history: deque = deque(maxlen=1000)
        self.focus_tracking: Dict[str, float] = defaultdict(float)
        self.transition_patterns: List[Dict[str, Any]] = []
        self.monitoring_active = False
        
        # Application categories and productivity scores
        self.app_categories = {
            'development': {
                'keywords': ['code', 'terminal', 'git', 'docker', 'kubernetes', 'vim', 'emacs', 'intellij', 'vscode', 'sublime'],
                'productivity_score': 0.9
            },
            'communication': {
                'keywords': ['slack', 'teams', 'zoom', 'email', 'discord', 'telegram', 'whatsapp', 'skype'],
                'productivity_score': 0.7
            },
            'productivity': {
                'keywords': ['notion', 'obsidian', 'excel', 'word', 'sheets', 'docs', 'powerpoint', 'keynote'],
                'productivity_score': 0.8
            },
            'design': {
                'keywords': ['figma', 'photoshop', 'sketch', 'canva', 'illustrator', 'indesign', 'blender'],
                'productivity_score': 0.85
            },
            'browser': {
                'keywords': ['chrome', 'firefox', 'safari', 'edge', 'opera', 'brave'],
                'productivity_score': 0.5  # Depends on content
            },
            'entertainment': {
                'keywords': ['spotify', 'youtube', 'netflix', 'games', 'steam', 'twitch', 'music'],
                'productivity_score': 0.2
            },
            'system': {
                'keywords': ['finder', 'explorer', 'settings', 'control panel', 'activity monitor', 'task manager'],
                'productivity_score': 0.3
            },
            'research': {
                'keywords': ['pdf', 'reader', 'research', 'papers', 'mendeley', 'zotero'],
                'productivity_score': 0.8
            }
        }
        
        # Platform-specific implementations
        self.system = platform.system().lower()
        
    async def initialize(self):
        """Initialize the application monitor"""
        try:
            logger.info(f"Initializing Application Monitor for {self.system}")
            
            # Start monitoring task
            asyncio.create_task(self.monitor_applications())
            self.monitoring_active = True
            
            logger.info("Application Monitor initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Application Monitor: {e}")
            raise

    async def monitor_applications(self):
        """Continuously monitor application usage"""
        while self.monitoring_active:
            try:
                await self.scan_active_applications()
                await self.update_focus_tracking()
                await self.detect_application_transitions()
                
                # Sleep for monitoring interval
                await asyncio.sleep(5)  # 5-second intervals
                
            except Exception as e:
                logger.error(f"Error in application monitoring: {e}")
                await asyncio.sleep(10)  # Longer sleep on error

    async def scan_active_applications(self):
        """Scan and catalog currently active applications"""
        try:
            current_apps = {}
            
            # Get all running processes
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']):
                try:
                    proc_info = proc.info
                    if proc_info['name'] and proc_info['cpu_percent'] is not None:
                        
                        # Get additional process information
                        window_title = await self.get_window_title(proc_info['pid'])
                        category = self.categorize_application(proc_info['name'])
                        productivity_score = self.calculate_productivity_score(
                            proc_info['name'], window_title, category
                        )
                        
                        app_context = ApplicationContext(
                            name=proc_info['name'],
                            window_title=window_title,
                            process_id=proc_info['pid'],
                            cpu_usage=proc_info['cpu_percent'],
                            memory_usage=proc_info['memory_percent'],
                            active_time=self.get_active_time(proc_info['pid']),
                            last_interaction=datetime.utcnow(),
                            category=category,
                            productivity_score=productivity_score
                        )
                        
                        current_apps[proc_info['pid']] = app_context
                        
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            # Update active applications
            self.active_applications = current_apps
            
            # Add to history
            self.application_history.append({
                'timestamp': datetime.utcnow(),
                'applications': list(current_apps.values())
            })
            
        except Exception as e:
            logger.error(f"Failed to scan active applications: {e}")

    async def get_window_title(self, pid: int) -> str:
        """Get window title for a process (platform-specific)"""
        try:
            if self.system == 'darwin':  # macOS
                return await self.get_macos_window_title(pid)
            elif self.system == 'linux':
                return await self.get_linux_window_title(pid)
            elif self.system == 'windows':
                return await self.get_windows_window_title(pid)
            else:
                return ""
        except Exception:
            return ""

    async def get_macos_window_title(self, pid: int) -> str:
        """Get window title on macOS using AppleScript"""
        try:
            script = f'''
            tell application "System Events"
                set frontApp to first application process whose unix id is {pid}
                if exists frontApp then
                    set frontWindow to first window of frontApp
                    if exists frontWindow then
                        return name of frontWindow
                    end if
                end if
            end tell
            '''
            
            result = subprocess.run(
                ['osascript', '-e', script],
                capture_output=True,
                text=True,
                timeout=2
            )
            
            return result.stdout.strip() if result.returncode == 0 else ""
            
        except Exception:
            return ""

    async def get_linux_window_title(self, pid: int) -> str:
        """Get window title on Linux using xdotool/wmctrl"""
        try:
            # Try xdotool first
            result = subprocess.run(
                ['xdotool', 'search', '--pid', str(pid), 'getwindowname'],
                capture_output=True,
                text=True,
                timeout=2
            )
            
            if result.returncode == 0:
                return result.stdout.strip()
            
            # Fallback to wmctrl
            result = subprocess.run(
                ['wmctrl', '-l', '-p'],
                capture_output=True,
                text=True,
                timeout=2
            )
            
            for line in result.stdout.split('\n'):
                if str(pid) in line:
                    parts = line.split(None, 4)
                    if len(parts) >= 5:
                        return parts[4]
            
            return ""
            
        except Exception:
            return ""

    async def get_windows_window_title(self, pid: int) -> str:
        """Get window title on Windows using PowerShell"""
        try:
            script = f'''
            Get-Process -Id {pid} | ForEach-Object {{
                $_.MainWindowTitle
            }}
            '''
            
            result = subprocess.run(
                ['powershell', '-Command', script],
                capture_output=True,
                text=True,
                timeout=2
            )
            
            return result.stdout.strip() if result.returncode == 0 else ""
            
        except Exception:
            return ""

    def categorize_application(self, app_name: str) -> str:
        """Categorize application based on name"""
        app_name_lower = app_name.lower()
        
        for category, config in self.app_categories.items():
            for keyword in config['keywords']:
                if keyword in app_name_lower:
                    return category
        
        return 'other'

    def calculate_productivity_score(self, app_name: str, window_title: str, category: str) -> float:
        """Calculate productivity score for an application"""
        base_score = self.app_categories.get(category, {}).get('productivity_score', 0.5)
        
        # Adjust based on window title for browsers
        if category == 'browser' and window_title:
            title_lower = window_title.lower()
            
            # Work-related sites
            if any(keyword in title_lower for keyword in ['github', 'stackoverflow', 'docs', 'documentation', 'jira', 'confluence']):
                return 0.8
            # Social media
            elif any(keyword in title_lower for keyword in ['facebook', 'twitter', 'instagram', 'reddit', 'tiktok']):
                return 0.1
            # Video streaming
            elif any(keyword in title_lower for keyword in ['youtube', 'netflix', 'twitch', 'video']):
                return 0.2
            # News/reading
            elif any(keyword in title_lower for keyword in ['news', 'article', 'blog', 'medium']):
                return 0.4
        
        return base_score

    def get_active_time(self, pid: int) -> float:
        """Get active time for a process"""
        try:
            proc = psutil.Process(pid)
            return time.time() - proc.create_time()
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            return 0.0

    async def update_focus_tracking(self):
        """Update focus tracking for applications"""
        try:
            # Get currently focused application
            focused_app = await self.get_focused_application()
            
            if focused_app:
                self.focus_tracking[focused_app.name] += 5.0  # 5 seconds of focus
                
        except Exception as e:
            logger.error(f"Failed to update focus tracking: {e}")

    async def get_focused_application(self) -> Optional[ApplicationContext]:
        """Get the currently focused application"""
        try:
            if self.system == 'darwin':
                return await self.get_macos_focused_app()
            elif self.system == 'linux':
                return await self.get_linux_focused_app()
            elif self.system == 'windows':
                return await self.get_windows_focused_app()
            else:
                return None
        except Exception:
            return None

    async def get_macos_focused_app(self) -> Optional[ApplicationContext]:
        """Get focused application on macOS"""
        try:
            script = '''
            tell application "System Events"
                set frontApp to first application process whose frontmost is true
                return {name of frontApp, unix id of frontApp}
            end tell
            '''
            
            result = subprocess.run(
                ['osascript', '-e', script],
                capture_output=True,
                text=True,
                timeout=2
            )
            
            if result.returncode == 0:
                output = result.stdout.strip()
                # Parse the output to get app name and PID
                # This is a simplified version
                for app in self.active_applications.values():
                    if app.name in output:
                        return app
            
            return None
            
        except Exception:
            return None

    async def detect_application_transitions(self):
        """Detect patterns in application transitions"""
        try:
            if len(self.application_history) < 2:
                return
            
            current = self.application_history[-1]
            previous = self.application_history[-2]
            
            # Find application changes
            current_apps = {app.name for app in current['applications']}
            previous_apps = {app.name for app in previous['applications']}
            
            new_apps = current_apps - previous_apps
            closed_apps = previous_apps - current_apps
            
            if new_apps or closed_apps:
                transition = {
                    'timestamp': current['timestamp'],
                    'new_apps': list(new_apps),
                    'closed_apps': list(closed_apps),
                    'transition_type': self.classify_transition(new_apps, closed_apps)
                }
                
                self.transition_patterns.append(transition)
                
                # Keep only recent transitions
                if len(self.transition_patterns) > 100:
                    self.transition_patterns = self.transition_patterns[-100:]
                    
        except Exception as e:
            logger.error(f"Failed to detect application transitions: {e}")

    def classify_transition(self, new_apps: set, closed_apps: set) -> str:
        """Classify the type of application transition"""
        if not new_apps and closed_apps:
            return 'cleanup'
        elif new_apps and not closed_apps:
            return 'expansion'
        elif new_apps and closed_apps:
            return 'switch'
        else:
            return 'unknown'

    async def get_current_applications(self) -> List[ApplicationContext]:
        """Get current active applications"""
        return list(self.active_applications.values())

    async def get_application_transitions(self, user_id: str) -> List[Dict[str, Any]]:
        """Get recent application transitions"""
        # Return recent transitions (last 10)
        return self.transition_patterns[-10:] if self.transition_patterns else []

    def get_productivity_summary(self) -> Dict[str, Any]:
        """Get productivity summary based on application usage"""
        if not self.active_applications:
            return {'total_score': 0.0, 'category_breakdown': {}}
        
        category_scores = defaultdict(list)
        total_score = 0.0
        
        for app in self.active_applications.values():
            category_scores[app.category].append(app.productivity_score)
            total_score += app.productivity_score
        
        # Calculate averages
        category_averages = {
            category: sum(scores) / len(scores)
            for category, scores in category_scores.items()
        }
        
        return {
            'total_score': total_score / len(self.active_applications),
            'category_breakdown': category_averages,
            'focus_distribution': dict(self.focus_tracking)
        }
