"""
Security Module for MK13 MVP
Implements comprehensive security measures including encryption, authentication, and validation
"""

import os
import jwt
import bcrypt
import secrets
import hashlib
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend
import base64
import logging
from functools import wraps
from fastapi import HTTPException, Request, Depends
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
import re

logger = logging.getLogger(__name__)

class SecurityManager:
    """Comprehensive security manager for MK13 MVP"""
    
    def __init__(self):
        self.jwt_secret = os.getenv('JWT_SECRET_KEY', self._generate_secret_key())
        self.encryption_key = os.getenv('ENCRYPTION_KEY', self._generate_encryption_key())
        self.fernet = Fernet(self.encryption_key.encode() if isinstance(self.encryption_key, str) else self.encryption_key)
        self.security = HTTPBearer()
        
        # Security configuration
        self.jwt_algorithm = 'HS256'
        self.jwt_expiration_hours = 24
        self.max_login_attempts = 5
        self.lockout_duration_minutes = 30
        self.password_min_length = 8
        self.session_timeout_hours = 8
        
        # Rate limiting
        self.rate_limits = {
            'login': {'requests': 5, 'window': 300},  # 5 requests per 5 minutes
            'api': {'requests': 100, 'window': 60},   # 100 requests per minute
            'ai': {'requests': 20, 'window': 60},     # 20 AI requests per minute
        }
        
        # Track failed attempts and rate limits
        self.failed_attempts = {}
        self.rate_limit_tracker = {}
        
        logger.info("Security Manager initialized")

    def _generate_secret_key(self) -> str:
        """Generate a secure secret key"""
        return secrets.token_urlsafe(32)
    
    def _generate_encryption_key(self) -> bytes:
        """Generate a secure encryption key"""
        return Fernet.generate_key()

    # Authentication & Authorization
    
    def hash_password(self, password: str) -> str:
        """Hash password using bcrypt"""
        salt = bcrypt.gensalt()
        hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
        return hashed.decode('utf-8')
    
    def verify_password(self, password: str, hashed: str) -> bool:
        """Verify password against hash"""
        return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
    
    def generate_jwt_token(self, user_id: str, email: str, additional_claims: Dict[str, Any] = None) -> str:
        """Generate JWT token for user"""
        now = datetime.utcnow()
        payload = {
            'user_id': user_id,
            'email': email,
            'iat': now,
            'exp': now + timedelta(hours=self.jwt_expiration_hours),
            'jti': secrets.token_urlsafe(16),  # JWT ID for token revocation
        }
        
        if additional_claims:
            payload.update(additional_claims)
        
        token = jwt.encode(payload, self.jwt_secret, algorithm=self.jwt_algorithm)
        logger.info(f"Generated JWT token for user {user_id}")
        return token
    
    def verify_jwt_token(self, token: str) -> Dict[str, Any]:
        """Verify and decode JWT token"""
        try:
            payload = jwt.decode(token, self.jwt_secret, algorithms=[self.jwt_algorithm])
            return payload
        except jwt.ExpiredSignatureError:
            raise HTTPException(status_code=401, detail="Token has expired")
        except jwt.InvalidTokenError:
            raise HTTPException(status_code=401, detail="Invalid token")
    
    def generate_api_key(self, user_id: str) -> str:
        """Generate API key for user"""
        key_data = f"{user_id}:{secrets.token_urlsafe(32)}:{datetime.utcnow().isoformat()}"
        return base64.b64encode(key_data.encode()).decode()
    
    def verify_api_key(self, api_key: str) -> Optional[str]:
        """Verify API key and return user_id"""
        try:
            decoded = base64.b64decode(api_key.encode()).decode()
            parts = decoded.split(':')
            if len(parts) >= 2:
                return parts[0]  # user_id
        except Exception:
            pass
        return None

    # Encryption & Data Protection
    
    def encrypt_sensitive_data(self, data: str) -> str:
        """Encrypt sensitive data using AES-256"""
        encrypted = self.fernet.encrypt(data.encode())
        return base64.b64encode(encrypted).decode()
    
    def decrypt_sensitive_data(self, encrypted_data: str) -> str:
        """Decrypt sensitive data"""
        try:
            encrypted_bytes = base64.b64decode(encrypted_data.encode())
            decrypted = self.fernet.decrypt(encrypted_bytes)
            return decrypted.decode()
        except Exception as e:
            logger.error(f"Decryption failed: {e}")
            raise HTTPException(status_code=500, detail="Decryption failed")
    
    def hash_data(self, data: str) -> str:
        """Create SHA-256 hash of data"""
        return hashlib.sha256(data.encode()).hexdigest()
    
    def generate_secure_token(self, length: int = 32) -> str:
        """Generate cryptographically secure random token"""
        return secrets.token_urlsafe(length)

    # Input Validation & Sanitization
    
    def validate_email(self, email: str) -> bool:
        """Validate email format"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    def validate_password_strength(self, password: str) -> Dict[str, Any]:
        """Validate password strength"""
        issues = []
        
        if len(password) < self.password_min_length:
            issues.append(f"Password must be at least {self.password_min_length} characters")
        
        if not re.search(r'[A-Z]', password):
            issues.append("Password must contain at least one uppercase letter")
        
        if not re.search(r'[a-z]', password):
            issues.append("Password must contain at least one lowercase letter")
        
        if not re.search(r'\d', password):
            issues.append("Password must contain at least one number")
        
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            issues.append("Password must contain at least one special character")
        
        return {
            'valid': len(issues) == 0,
            'issues': issues,
            'strength': 'strong' if len(issues) == 0 else 'weak'
        }
    
    def sanitize_input(self, data: str) -> str:
        """Sanitize user input to prevent injection attacks"""
        # Remove potentially dangerous characters
        sanitized = re.sub(r'[<>"\';\\]', '', data)
        return sanitized.strip()
    
    def validate_user_id(self, user_id: str) -> bool:
        """Validate user ID format"""
        # Allow alphanumeric, hyphens, and underscores
        pattern = r'^[a-zA-Z0-9_-]+$'
        return re.match(pattern, user_id) is not None and len(user_id) <= 50

    # Rate Limiting & Brute Force Protection
    
    def check_rate_limit(self, identifier: str, limit_type: str = 'api') -> bool:
        """Check if request is within rate limits"""
        if limit_type not in self.rate_limits:
            return True
        
        limit_config = self.rate_limits[limit_type]
        now = datetime.utcnow()
        
        # Clean old entries
        cutoff = now - timedelta(seconds=limit_config['window'])
        
        if identifier not in self.rate_limit_tracker:
            self.rate_limit_tracker[identifier] = []
        
        # Remove old requests
        self.rate_limit_tracker[identifier] = [
            timestamp for timestamp in self.rate_limit_tracker[identifier]
            if timestamp > cutoff
        ]
        
        # Check if under limit
        if len(self.rate_limit_tracker[identifier]) >= limit_config['requests']:
            logger.warning(f"Rate limit exceeded for {identifier} ({limit_type})")
            return False
        
        # Add current request
        self.rate_limit_tracker[identifier].append(now)
        return True
    
    def record_failed_login(self, identifier: str) -> bool:
        """Record failed login attempt and check if account should be locked"""
        now = datetime.utcnow()
        
        if identifier not in self.failed_attempts:
            self.failed_attempts[identifier] = []
        
        # Clean old attempts
        cutoff = now - timedelta(minutes=self.lockout_duration_minutes)
        self.failed_attempts[identifier] = [
            timestamp for timestamp in self.failed_attempts[identifier]
            if timestamp > cutoff
        ]
        
        # Add current attempt
        self.failed_attempts[identifier].append(now)
        
        # Check if account should be locked
        if len(self.failed_attempts[identifier]) >= self.max_login_attempts:
            logger.warning(f"Account locked due to failed attempts: {identifier}")
            return True  # Account is locked
        
        return False  # Account is not locked
    
    def clear_failed_attempts(self, identifier: str):
        """Clear failed login attempts for identifier"""
        if identifier in self.failed_attempts:
            del self.failed_attempts[identifier]

    # Security Headers & CORS
    
    def get_security_headers(self) -> Dict[str, str]:
        """Get security headers for HTTP responses"""
        return {
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY',
            'X-XSS-Protection': '1; mode=block',
            'Strict-Transport-Security': 'max-age=********; includeSubDomains',
            'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'",
            'Referrer-Policy': 'strict-origin-when-cross-origin',
            'Permissions-Policy': 'geolocation=(), microphone=(), camera=()'
        }
    
    def get_cors_config(self) -> Dict[str, Any]:
        """Get CORS configuration"""
        allowed_origins = os.getenv('ALLOWED_ORIGINS', 'http://localhost:3000').split(',')
        
        return {
            'allow_origins': allowed_origins,
            'allow_credentials': True,
            'allow_methods': ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
            'allow_headers': ['*'],
            'expose_headers': ['X-Total-Count']
        }

    # Session Management
    
    def generate_session_token(self) -> str:
        """Generate secure session token"""
        return secrets.token_urlsafe(32)
    
    def validate_session_token(self, token: str, user_id: str) -> bool:
        """Validate session token (implement with database/cache)"""
        # This would typically check against a database or cache
        # For now, return True as placeholder
        return True

# Global security manager instance
security_manager = SecurityManager()

# Dependency functions for FastAPI

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security_manager.security)) -> Dict[str, Any]:
    """Get current authenticated user from JWT token"""
    token = credentials.credentials
    payload = security_manager.verify_jwt_token(token)
    return payload

async def verify_api_key_dependency(request: Request) -> str:
    """Verify API key from request headers"""
    api_key = request.headers.get('X-API-Key')
    if not api_key:
        raise HTTPException(status_code=401, detail="API key required")
    
    user_id = security_manager.verify_api_key(api_key)
    if not user_id:
        raise HTTPException(status_code=401, detail="Invalid API key")
    
    return user_id

def require_auth(func):
    """Decorator to require authentication for endpoints"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        # This would be implemented with FastAPI dependencies
        return await func(*args, **kwargs)
    return wrapper

def rate_limit(limit_type: str = 'api'):
    """Decorator for rate limiting"""
    def decorator(func):
        @wraps(func)
        async def wrapper(request: Request, *args, **kwargs):
            # Get identifier (IP address or user ID)
            identifier = request.client.host
            
            if not security_manager.check_rate_limit(identifier, limit_type):
                raise HTTPException(status_code=429, detail="Rate limit exceeded")
            
            return await func(request, *args, **kwargs)
        return wrapper
    return decorator

# Initialize security manager
def initialize_security():
    """Initialize security manager"""
    global security_manager
    logger.info("Security system initialized")
    return security_manager

def get_security_manager():
    """Get security manager instance"""
    return security_manager
