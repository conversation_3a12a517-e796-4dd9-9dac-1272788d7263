"""
Temporal Pattern Learner for Enhanced Context Intelligence
Learn and predict user behavior patterns over time
"""

import asyncio
import json
import logging
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import numpy as np
from enum import Enum
from database import get_managers

logger = logging.getLogger(__name__)

class ContextType(Enum):
    WORK = "work"
    PERSONAL = "personal"
    MEETING = "meeting"
    FOCUS = "focus"
    TRAVEL = "travel"
    BREAK = "break"
    LEARNING = "learning"
    CREATIVE = "creative"

@dataclass
class TemporalPattern:
    pattern_id: str
    user_id: str
    context_type: ContextType
    time_patterns: Dict[str, Any]  # hour, day_of_week, etc.
    frequency: float
    confidence: float
    last_occurrence: datetime
    next_predicted: Optional[datetime]
    pattern_strength: float
    seasonal_factors: Dict[str, float]

@dataclass
class PatternPrediction:
    context_type: ContextType
    confidence: float
    predicted_time: datetime
    duration_estimate: int  # minutes
    factors: List[str]
    pattern_ids: List[str]

class TemporalPatternLearner:
    """Learn temporal patterns from user behavior and predict future contexts"""
    
    def __init__(self):
        self.user_patterns: Dict[str, List[TemporalPattern]] = defaultdict(list)
        self.context_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=10000))
        self.learning_window = timedelta(days=30)  # Learn from last 30 days
        self.min_pattern_occurrences = 3
        self.pattern_cache: Dict[str, Dict] = {}
        
        # Time-based features for pattern learning
        self.time_features = [
            'hour', 'day_of_week', 'day_of_month', 'week_of_year',
            'month', 'quarter', 'is_weekend', 'is_holiday'
        ]
        
        # Context transition patterns
        self.transition_patterns: Dict[str, Dict] = defaultdict(dict)
        
    async def initialize(self):
        """Initialize the temporal pattern learner"""
        try:
            # Load existing patterns from database
            await self.load_patterns_from_database()
            
            # Start pattern learning task
            asyncio.create_task(self.pattern_learning_loop())
            
            logger.info("Temporal Pattern Learner initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Temporal Pattern Learner: {e}")
            raise

    async def pattern_learning_loop(self):
        """Continuous pattern learning loop"""
        while True:
            try:
                # Learn patterns every hour
                await self.learn_patterns_for_all_users()
                await asyncio.sleep(3600)  # 1 hour
                
            except Exception as e:
                logger.error(f"Error in pattern learning loop: {e}")
                await asyncio.sleep(1800)  # 30 minutes on error

    async def analyze_temporal_patterns(self, user_id: str) -> Dict[str, Any]:
        """Analyze temporal patterns for a user"""
        try:
            # Get user patterns
            patterns = self.user_patterns.get(user_id, [])
            
            # Get current time features
            current_features = self.extract_time_features(datetime.utcnow())
            
            # Find matching patterns
            matching_patterns = await self.find_matching_patterns(patterns, current_features)
            
            # Generate predictions
            predictions = await self.generate_predictions(user_id, matching_patterns)
            
            # Calculate pattern confidence
            pattern_confidence = self.calculate_pattern_confidence(matching_patterns)
            
            return {
                'patterns': [asdict(p) for p in patterns],
                'matching_patterns': [asdict(p) for p in matching_patterns],
                'predictions': [asdict(p) for p in predictions],
                'pattern_confidence': pattern_confidence,
                'next_context_prediction': predictions[0] if predictions else None
            }
            
        except Exception as e:
            logger.error(f"Failed to analyze temporal patterns for user {user_id}: {e}")
            return {'patterns': [], 'matching_patterns': [], 'predictions': []}

    def extract_time_features(self, timestamp: datetime) -> Dict[str, Any]:
        """Extract time-based features from timestamp"""
        try:
            features = {
                'hour': timestamp.hour,
                'day_of_week': timestamp.weekday(),
                'day_of_month': timestamp.day,
                'week_of_year': timestamp.isocalendar()[1],
                'month': timestamp.month,
                'quarter': (timestamp.month - 1) // 3 + 1,
                'is_weekend': timestamp.weekday() >= 5,
                'is_holiday': self.is_holiday(timestamp),
                'time_of_day': self.get_time_of_day(timestamp.hour),
                'season': self.get_season(timestamp.month)
            }
            
            return features
            
        except Exception as e:
            logger.error(f"Failed to extract time features: {e}")
            return {}

    def is_holiday(self, timestamp: datetime) -> bool:
        """Check if timestamp is a holiday (simplified)"""
        # This would integrate with a holiday API or database
        # For now, return False
        return False

    def get_time_of_day(self, hour: int) -> str:
        """Get time of day category"""
        if 5 <= hour < 12:
            return 'morning'
        elif 12 <= hour < 17:
            return 'afternoon'
        elif 17 <= hour < 21:
            return 'evening'
        else:
            return 'night'

    def get_season(self, month: int) -> str:
        """Get season from month"""
        if month in [12, 1, 2]:
            return 'winter'
        elif month in [3, 4, 5]:
            return 'spring'
        elif month in [6, 7, 8]:
            return 'summer'
        else:
            return 'fall'

    async def update_patterns(self, enhanced_context):
        """Update patterns based on new context"""
        try:
            user_id = enhanced_context.user_id
            
            # Add to context history
            self.context_history[user_id].append({
                'timestamp': enhanced_context.timestamp,
                'context_type': enhanced_context.primary_type,
                'confidence': enhanced_context.confidence,
                'features': self.extract_time_features(enhanced_context.timestamp)
            })
            
            # Trigger pattern learning for this user
            await self.learn_patterns_for_user(user_id)
            
        except Exception as e:
            logger.error(f"Failed to update patterns: {e}")

    async def learn_patterns_for_all_users(self):
        """Learn patterns for all users with recent activity"""
        try:
            # Get users with recent context history
            active_users = [
                user_id for user_id, history in self.context_history.items()
                if history and (datetime.utcnow() - history[-1]['timestamp']).days <= 1
            ]
            
            for user_id in active_users:
                await self.learn_patterns_for_user(user_id)
                
        except Exception as e:
            logger.error(f"Failed to learn patterns for all users: {e}")

    async def learn_patterns_for_user(self, user_id: str):
        """Learn temporal patterns for a specific user"""
        try:
            history = list(self.context_history[user_id])
            
            if len(history) < self.min_pattern_occurrences:
                return
            
            # Group contexts by type
            context_groups = defaultdict(list)
            for entry in history:
                context_groups[entry['context_type']].append(entry)
            
            # Learn patterns for each context type
            new_patterns = []
            for context_type, contexts in context_groups.items():
                if len(contexts) >= self.min_pattern_occurrences:
                    patterns = await self.discover_patterns(user_id, context_type, contexts)
                    new_patterns.extend(patterns)
            
            # Update user patterns
            self.user_patterns[user_id] = new_patterns
            
            # Save to database
            await self.save_patterns_to_database(user_id, new_patterns)
            
            logger.info(f"Learned {len(new_patterns)} patterns for user {user_id}")
            
        except Exception as e:
            logger.error(f"Failed to learn patterns for user {user_id}: {e}")

    async def discover_patterns(self, user_id: str, context_type: ContextType, contexts: List[Dict]) -> List[TemporalPattern]:
        """Discover temporal patterns in context data"""
        try:
            patterns = []
            
            # Group by time features
            feature_groups = defaultdict(list)
            
            for context in contexts:
                features = context['features']
                
                # Create feature combinations
                combinations = [
                    ('hour', features['hour']),
                    ('day_of_week', features['day_of_week']),
                    ('time_of_day', features['time_of_day']),
                    ('hour_day_combo', (features['hour'], features['day_of_week'])),
                    ('weekend', features['is_weekend'])
                ]
                
                for combo_name, combo_value in combinations:
                    feature_groups[combo_name].append((combo_value, context))
            
            # Analyze each feature group
            for feature_name, feature_data in feature_groups.items():
                pattern = await self.analyze_feature_pattern(
                    user_id, context_type, feature_name, feature_data
                )
                
                if pattern:
                    patterns.append(pattern)
            
            return patterns
            
        except Exception as e:
            logger.error(f"Failed to discover patterns: {e}")
            return []

    async def analyze_feature_pattern(
        self, 
        user_id: str, 
        context_type: ContextType, 
        feature_name: str, 
        feature_data: List[Tuple]
    ) -> Optional[TemporalPattern]:
        """Analyze a specific feature pattern"""
        try:
            # Group by feature value
            value_groups = defaultdict(list)
            for value, context in feature_data:
                value_groups[value].append(context)
            
            # Find the most frequent value
            most_frequent_value = max(value_groups.keys(), key=lambda v: len(value_groups[v]))
            most_frequent_contexts = value_groups[most_frequent_value]
            
            # Check if pattern is significant
            frequency = len(most_frequent_contexts) / len(feature_data)
            
            if frequency < 0.3:  # At least 30% frequency
                return None
            
            # Calculate confidence based on consistency
            timestamps = [ctx['timestamp'] for ctx in most_frequent_contexts]
            confidence = self.calculate_pattern_confidence_from_timestamps(timestamps)
            
            # Calculate pattern strength
            pattern_strength = frequency * confidence
            
            if pattern_strength < 0.2:
                return None
            
            # Create pattern
            pattern = TemporalPattern(
                pattern_id=str(uuid.uuid4()),
                user_id=user_id,
                context_type=context_type,
                time_patterns={
                    'feature_name': feature_name,
                    'feature_value': most_frequent_value,
                    'frequency': frequency,
                    'occurrences': len(most_frequent_contexts)
                },
                frequency=frequency,
                confidence=confidence,
                last_occurrence=max(timestamps),
                next_predicted=self.predict_next_occurrence(feature_name, most_frequent_value, timestamps),
                pattern_strength=pattern_strength,
                seasonal_factors=self.calculate_seasonal_factors(most_frequent_contexts)
            )
            
            return pattern
            
        except Exception as e:
            logger.error(f"Failed to analyze feature pattern: {e}")
            return None

    def calculate_pattern_confidence_from_timestamps(self, timestamps: List[datetime]) -> float:
        """Calculate confidence based on timestamp consistency"""
        try:
            if len(timestamps) < 2:
                return 0.5
            
            # Calculate time intervals
            sorted_timestamps = sorted(timestamps)
            intervals = []
            
            for i in range(1, len(sorted_timestamps)):
                interval = (sorted_timestamps[i] - sorted_timestamps[i-1]).total_seconds()
                intervals.append(interval)
            
            # Calculate coefficient of variation
            if not intervals:
                return 0.5
            
            mean_interval = np.mean(intervals)
            std_interval = np.std(intervals)
            
            if mean_interval == 0:
                return 0.5
            
            cv = std_interval / mean_interval
            
            # Convert to confidence (lower CV = higher confidence)
            confidence = max(0.1, min(1.0, 1.0 - cv))
            
            return confidence
            
        except Exception as e:
            logger.error(f"Failed to calculate pattern confidence: {e}")
            return 0.5

    def predict_next_occurrence(
        self, 
        feature_name: str, 
        feature_value: Any, 
        timestamps: List[datetime]
    ) -> Optional[datetime]:
        """Predict next occurrence of pattern"""
        try:
            if not timestamps:
                return None
            
            current_time = datetime.utcnow()
            last_occurrence = max(timestamps)
            
            # Simple prediction based on feature type
            if feature_name == 'hour':
                # Predict next day at same hour
                next_occurrence = current_time.replace(
                    hour=feature_value, minute=0, second=0, microsecond=0
                )
                if next_occurrence <= current_time:
                    next_occurrence += timedelta(days=1)
                return next_occurrence
                
            elif feature_name == 'day_of_week':
                # Predict next week on same day
                days_ahead = feature_value - current_time.weekday()
                if days_ahead <= 0:
                    days_ahead += 7
                return current_time + timedelta(days=days_ahead)
                
            elif feature_name == 'hour_day_combo':
                hour, day_of_week = feature_value
                days_ahead = day_of_week - current_time.weekday()
                if days_ahead <= 0:
                    days_ahead += 7
                
                next_occurrence = current_time + timedelta(days=days_ahead)
                next_occurrence = next_occurrence.replace(
                    hour=hour, minute=0, second=0, microsecond=0
                )
                return next_occurrence
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to predict next occurrence: {e}")
            return None

    def calculate_seasonal_factors(self, contexts: List[Dict]) -> Dict[str, float]:
        """Calculate seasonal factors for pattern"""
        try:
            seasonal_counts = defaultdict(int)
            
            for context in contexts:
                season = context['features'].get('season', 'unknown')
                seasonal_counts[season] += 1
            
            total_contexts = len(contexts)
            seasonal_factors = {
                season: count / total_contexts
                for season, count in seasonal_counts.items()
            }
            
            return seasonal_factors
            
        except Exception as e:
            logger.error(f"Failed to calculate seasonal factors: {e}")
            return {}

    async def find_matching_patterns(
        self, 
        patterns: List[TemporalPattern], 
        current_features: Dict[str, Any]
    ) -> List[TemporalPattern]:
        """Find patterns that match current time features"""
        try:
            matching_patterns = []
            
            for pattern in patterns:
                if self.pattern_matches_features(pattern, current_features):
                    matching_patterns.append(pattern)
            
            # Sort by pattern strength
            matching_patterns.sort(key=lambda p: p.pattern_strength, reverse=True)
            
            return matching_patterns
            
        except Exception as e:
            logger.error(f"Failed to find matching patterns: {e}")
            return []

    def pattern_matches_features(self, pattern: TemporalPattern, features: Dict[str, Any]) -> bool:
        """Check if pattern matches current features"""
        try:
            time_patterns = pattern.time_patterns
            feature_name = time_patterns.get('feature_name')
            feature_value = time_patterns.get('feature_value')
            
            if feature_name not in features:
                return False
            
            current_value = features[feature_name]
            
            # Handle different feature types
            if feature_name == 'hour_day_combo':
                return current_value == feature_value
            else:
                return current_value == feature_value
                
        except Exception as e:
            logger.error(f"Failed to check pattern match: {e}")
            return False

    async def generate_predictions(
        self, 
        user_id: str, 
        matching_patterns: List[TemporalPattern]
    ) -> List[PatternPrediction]:
        """Generate context predictions based on patterns"""
        try:
            predictions = []
            
            for pattern in matching_patterns[:5]:  # Top 5 patterns
                prediction = PatternPrediction(
                    context_type=pattern.context_type,
                    confidence=pattern.confidence * pattern.pattern_strength,
                    predicted_time=pattern.next_predicted or datetime.utcnow(),
                    duration_estimate=self.estimate_context_duration(pattern),
                    factors=[pattern.time_patterns.get('feature_name', 'unknown')],
                    pattern_ids=[pattern.pattern_id]
                )
                
                predictions.append(prediction)
            
            # Sort by confidence
            predictions.sort(key=lambda p: p.confidence, reverse=True)
            
            return predictions
            
        except Exception as e:
            logger.error(f"Failed to generate predictions: {e}")
            return []

    def estimate_context_duration(self, pattern: TemporalPattern) -> int:
        """Estimate context duration in minutes"""
        try:
            # Default durations by context type
            default_durations = {
                ContextType.WORK: 240,  # 4 hours
                ContextType.MEETING: 60,  # 1 hour
                ContextType.BREAK: 15,  # 15 minutes
                ContextType.FOCUS: 90,  # 1.5 hours
                ContextType.PERSONAL: 120,  # 2 hours
                ContextType.TRAVEL: 60,  # 1 hour
                ContextType.LEARNING: 90,  # 1.5 hours
                ContextType.CREATIVE: 120  # 2 hours
            }
            
            return default_durations.get(pattern.context_type, 60)
            
        except Exception as e:
            logger.error(f"Failed to estimate context duration: {e}")
            return 60

    def calculate_pattern_confidence(self, patterns: List[TemporalPattern]) -> float:
        """Calculate overall pattern confidence"""
        try:
            if not patterns:
                return 0.0
            
            # Weighted average of pattern strengths
            total_weight = sum(p.pattern_strength for p in patterns)
            
            if total_weight == 0:
                return 0.0
            
            weighted_confidence = sum(
                p.confidence * p.pattern_strength for p in patterns
            ) / total_weight
            
            return weighted_confidence
            
        except Exception as e:
            logger.error(f"Failed to calculate pattern confidence: {e}")
            return 0.0

    async def load_patterns_from_database(self):
        """Load existing patterns from database"""
        try:
            # This would load patterns from database
            # For now, initialize empty
            logger.info("Loaded temporal patterns from database")
            
        except Exception as e:
            logger.error(f"Failed to load patterns from database: {e}")

    async def save_patterns_to_database(self, user_id: str, patterns: List[TemporalPattern]):
        """Save patterns to database"""
        try:
            # This would save patterns to database
            # For now, just log
            logger.info(f"Saved {len(patterns)} patterns for user {user_id}")
            
        except Exception as e:
            logger.error(f"Failed to save patterns to database: {e}")

    def matches_temporal_pattern(self, pattern: TemporalPattern, current_hour: int, current_day: int) -> bool:
        """Check if current time matches a temporal pattern"""
        try:
            time_patterns = pattern.time_patterns
            feature_name = time_patterns.get('feature_name')
            feature_value = time_patterns.get('feature_value')
            
            if feature_name == 'hour':
                return current_hour == feature_value
            elif feature_name == 'day_of_week':
                return current_day == feature_value
            elif feature_name == 'hour_day_combo':
                hour, day = feature_value
                return current_hour == hour and current_day == day
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to match temporal pattern: {e}")
            return False
