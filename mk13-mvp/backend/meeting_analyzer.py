"""
Meeting Analyzer for Enhanced Context Intelligence
Advanced meeting context detection and analysis
"""

import asyncio
import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
import re
from google_workspace import get_google_services
from ai_service import get_ai_service

logger = logging.getLogger(__name__)

@dataclass
class MeetingContext:
    meeting_id: str
    title: str
    participants: List[str]
    start_time: datetime
    end_time: datetime
    location: Optional[str]
    meeting_type: str  # video, in-person, phone
    agenda_topics: List[str]
    preparation_status: str
    context_relevance: float
    meeting_url: Optional[str] = None
    organizer: Optional[str] = None
    description: Optional[str] = None
    attachments: List[Dict[str, Any]] = None

@dataclass
class MeetingPreparation:
    meeting_id: str
    preparation_items: List[Dict[str, Any]]
    completion_status: float
    estimated_prep_time: int  # minutes
    priority_level: str
    auto_generated: bool

class MeetingAnalyzer:
    """Analyze meeting context and provide intelligent preparation assistance"""
    
    def __init__(self):
        self.google_services = None
        self.ai_service = None
        self.active_meetings: Dict[str, MeetingContext] = {}
        self.meeting_patterns: Dict[str, Any] = {}
        self.preparation_cache: Dict[str, MeetingPreparation] = {}
        
        # Meeting type detection patterns
        self.meeting_type_patterns = {
            'video': [
                r'zoom\.us', r'meet\.google\.com', r'teams\.microsoft\.com',
                r'webex\.com', r'gotomeeting\.com', r'bluejeans\.com'
            ],
            'phone': [
                r'dial.in', r'conference.call', r'phone.number',
                r'\+\d{1,3}[-.\s]?\d{3,4}[-.\s]?\d{3,4}[-.\s]?\d{3,4}'
            ],
            'in-person': [
                r'room\s+\w+', r'building\s+\w+', r'floor\s+\d+',
                r'conference.room', r'meeting.room', r'office'
            ]
        }
        
        # Meeting importance indicators
        self.importance_keywords = {
            'high': ['urgent', 'critical', 'important', 'board', 'executive', 'ceo', 'cto'],
            'medium': ['review', 'planning', 'strategy', 'quarterly', 'monthly'],
            'low': ['casual', 'informal', 'coffee', 'catch-up', 'social']
        }
        
    async def initialize(self):
        """Initialize the meeting analyzer"""
        try:
            self.google_services = get_google_services()
            self.ai_service = get_ai_service()
            
            # Start meeting monitoring
            asyncio.create_task(self.monitor_meetings())
            
            logger.info("Meeting Analyzer initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Meeting Analyzer: {e}")
            raise

    async def monitor_meetings(self):
        """Continuously monitor meeting status"""
        while True:
            try:
                # Check for active meetings every 30 seconds
                await self.update_active_meetings()
                await asyncio.sleep(30)
                
            except Exception as e:
                logger.error(f"Error in meeting monitoring: {e}")
                await asyncio.sleep(60)

    async def get_current_meeting_context(self, user_id: str) -> Dict[str, Any]:
        """Get comprehensive meeting context for a user"""
        try:
            # Get calendar events
            calendar_events = await self.get_calendar_events(user_id)
            
            # Analyze current and upcoming meetings
            current_meeting = await self.detect_current_meeting(calendar_events)
            upcoming_meetings = await self.get_upcoming_meetings(calendar_events)
            
            # Check preparation status
            preparation_needed = await self.assess_preparation_needs(upcoming_meetings)
            
            # Generate meeting insights
            meeting_insights = await self.generate_meeting_insights(
                current_meeting, upcoming_meetings, user_id
            )
            
            return {
                'current': current_meeting,
                'upcoming': upcoming_meetings,
                'preparation_needed': preparation_needed,
                'insights': meeting_insights,
                'context_score': self.calculate_meeting_context_score(
                    current_meeting, upcoming_meetings
                )
            }
            
        except Exception as e:
            logger.error(f"Failed to get meeting context for user {user_id}: {e}")
            return {
                'current': None,
                'upcoming': [],
                'preparation_needed': False,
                'insights': {},
                'context_score': 0.0
            }

    async def get_calendar_events(self, user_id: str) -> List[Dict[str, Any]]:
        """Get calendar events from Google Calendar"""
        try:
            if not self.google_services:
                return []
            
            # Get events for today and tomorrow
            time_min = datetime.utcnow().isoformat() + 'Z'
            time_max = (datetime.utcnow() + timedelta(days=1)).isoformat() + 'Z'
            
            events = await self.google_services['calendar'].get_events(
                user_id=user_id,
                time_min=time_min,
                time_max=time_max,
                max_results=50
            )
            
            return events
            
        except Exception as e:
            logger.error(f"Failed to get calendar events: {e}")
            return []

    async def detect_current_meeting(self, calendar_events: List[Dict[str, Any]]) -> Optional[MeetingContext]:
        """Detect if user is currently in a meeting"""
        try:
            current_time = datetime.utcnow()
            
            for event in calendar_events:
                start_time = self.parse_datetime(event.get('start', {}).get('dateTime'))
                end_time = self.parse_datetime(event.get('end', {}).get('dateTime'))
                
                if not start_time or not end_time:
                    continue
                
                # Check if meeting is currently active
                if start_time <= current_time <= end_time:
                    meeting_context = await self.create_meeting_context(event)
                    
                    # Update active meetings
                    self.active_meetings[meeting_context.meeting_id] = meeting_context
                    
                    return meeting_context
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to detect current meeting: {e}")
            return None

    async def get_upcoming_meetings(self, calendar_events: List[Dict[str, Any]]) -> List[MeetingContext]:
        """Get upcoming meetings in the next 4 hours"""
        try:
            current_time = datetime.utcnow()
            cutoff_time = current_time + timedelta(hours=4)
            upcoming_meetings = []
            
            for event in calendar_events:
                start_time = self.parse_datetime(event.get('start', {}).get('dateTime'))
                
                if not start_time:
                    continue
                
                # Check if meeting is upcoming
                if current_time < start_time <= cutoff_time:
                    meeting_context = await self.create_meeting_context(event)
                    upcoming_meetings.append(meeting_context)
            
            # Sort by start time
            upcoming_meetings.sort(key=lambda m: m.start_time)
            
            return upcoming_meetings
            
        except Exception as e:
            logger.error(f"Failed to get upcoming meetings: {e}")
            return []

    async def create_meeting_context(self, event: Dict[str, Any]) -> MeetingContext:
        """Create meeting context from calendar event"""
        try:
            meeting_id = event.get('id', '')
            title = event.get('summary', 'Untitled Meeting')
            description = event.get('description', '')
            location = event.get('location', '')
            
            # Parse participants
            participants = []
            if 'attendees' in event:
                participants = [
                    attendee.get('email', '')
                    for attendee in event['attendees']
                    if attendee.get('email')
                ]
            
            # Detect meeting type
            meeting_type = self.detect_meeting_type(description, location)
            
            # Extract agenda topics
            agenda_topics = await self.extract_agenda_topics(title, description)
            
            # Calculate context relevance
            context_relevance = self.calculate_context_relevance(
                title, description, participants
            )
            
            # Parse times
            start_time = self.parse_datetime(event.get('start', {}).get('dateTime'))
            end_time = self.parse_datetime(event.get('end', {}).get('dateTime'))
            
            # Extract meeting URL
            meeting_url = self.extract_meeting_url(description, location)
            
            return MeetingContext(
                meeting_id=meeting_id,
                title=title,
                participants=participants,
                start_time=start_time,
                end_time=end_time,
                location=location,
                meeting_type=meeting_type,
                agenda_topics=agenda_topics,
                preparation_status='pending',
                context_relevance=context_relevance,
                meeting_url=meeting_url,
                organizer=event.get('organizer', {}).get('email'),
                description=description,
                attachments=event.get('attachments', [])
            )
            
        except Exception as e:
            logger.error(f"Failed to create meeting context: {e}")
            raise

    def detect_meeting_type(self, description: str, location: str) -> str:
        """Detect meeting type based on description and location"""
        text = f"{description} {location}".lower()
        
        for meeting_type, patterns in self.meeting_type_patterns.items():
            for pattern in patterns:
                if re.search(pattern, text, re.IGNORECASE):
                    return meeting_type
        
        return 'unknown'

    async def extract_agenda_topics(self, title: str, description: str) -> List[str]:
        """Extract agenda topics using AI"""
        try:
            if not self.ai_service:
                return []
            
            prompt = f"""
            Extract agenda topics from this meeting information:
            
            Title: {title}
            Description: {description}
            
            Return a JSON list of agenda topics (max 5 items).
            """
            
            response = await self.ai_service.process_text_input(
                user_id='system',
                message=prompt,
                context_id=None
            )
            
            try:
                topics = json.loads(response['response'])
                return topics if isinstance(topics, list) else []
            except json.JSONDecodeError:
                return []
                
        except Exception as e:
            logger.error(f"Failed to extract agenda topics: {e}")
            return []

    def calculate_context_relevance(self, title: str, description: str, participants: List[str]) -> float:
        """Calculate how relevant this meeting is to current context"""
        relevance_score = 0.5  # Base score
        
        text = f"{title} {description}".lower()
        
        # Check importance keywords
        for importance, keywords in self.importance_keywords.items():
            for keyword in keywords:
                if keyword in text:
                    if importance == 'high':
                        relevance_score += 0.3
                    elif importance == 'medium':
                        relevance_score += 0.2
                    elif importance == 'low':
                        relevance_score -= 0.1
        
        # Number of participants factor
        if len(participants) > 10:
            relevance_score += 0.2  # Large meetings are often important
        elif len(participants) == 1:
            relevance_score -= 0.1  # 1-on-1s might be less critical
        
        return min(max(relevance_score, 0.0), 1.0)

    def extract_meeting_url(self, description: str, location: str) -> Optional[str]:
        """Extract meeting URL from description or location"""
        text = f"{description} {location}"
        
        # Common meeting URL patterns
        url_patterns = [
            r'https?://[^\s]+zoom\.us/[^\s]+',
            r'https?://meet\.google\.com/[^\s]+',
            r'https?://[^\s]+\.webex\.com/[^\s]+',
            r'https?://teams\.microsoft\.com/[^\s]+',
        ]
        
        for pattern in url_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(0)
        
        return None

    async def assess_preparation_needs(self, upcoming_meetings: List[MeetingContext]) -> bool:
        """Assess if any upcoming meetings need preparation"""
        try:
            for meeting in upcoming_meetings:
                # Check if meeting is within next 2 hours
                time_until_meeting = (meeting.start_time - datetime.utcnow()).total_seconds() / 3600
                
                if time_until_meeting <= 2 and meeting.context_relevance > 0.6:
                    # Check if preparation exists
                    if meeting.meeting_id not in self.preparation_cache:
                        await self.generate_meeting_preparation(meeting)
                    
                    prep = self.preparation_cache.get(meeting.meeting_id)
                    if prep and prep.completion_status < 0.8:
                        return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to assess preparation needs: {e}")
            return False

    async def generate_meeting_preparation(self, meeting: MeetingContext) -> MeetingPreparation:
        """Generate AI-powered meeting preparation suggestions"""
        try:
            if not self.ai_service:
                return self.create_basic_preparation(meeting)
            
            prompt = f"""
            Generate meeting preparation items for:
            
            Meeting: {meeting.title}
            Participants: {', '.join(meeting.participants[:5])}
            Duration: {(meeting.end_time - meeting.start_time).total_seconds() / 3600:.1f} hours
            Description: {meeting.description or 'No description'}
            Agenda: {', '.join(meeting.agenda_topics)}
            
            Return JSON with:
            - preparation_items: [{{item, priority, estimated_time}}]
            - estimated_prep_time: total minutes
            - priority_level: high/medium/low
            """
            
            response = await self.ai_service.process_text_input(
                user_id='system',
                message=prompt,
                context_id=None
            )
            
            try:
                prep_data = json.loads(response['response'])
                
                preparation = MeetingPreparation(
                    meeting_id=meeting.meeting_id,
                    preparation_items=prep_data.get('preparation_items', []),
                    completion_status=0.0,
                    estimated_prep_time=prep_data.get('estimated_prep_time', 15),
                    priority_level=prep_data.get('priority_level', 'medium'),
                    auto_generated=True
                )
                
                self.preparation_cache[meeting.meeting_id] = preparation
                return preparation
                
            except json.JSONDecodeError:
                return self.create_basic_preparation(meeting)
                
        except Exception as e:
            logger.error(f"Failed to generate meeting preparation: {e}")
            return self.create_basic_preparation(meeting)

    def create_basic_preparation(self, meeting: MeetingContext) -> MeetingPreparation:
        """Create basic preparation items"""
        basic_items = [
            {'item': 'Review meeting agenda', 'priority': 'high', 'estimated_time': 5},
            {'item': 'Prepare talking points', 'priority': 'medium', 'estimated_time': 10},
            {'item': 'Check technical setup', 'priority': 'low', 'estimated_time': 3}
        ]
        
        preparation = MeetingPreparation(
            meeting_id=meeting.meeting_id,
            preparation_items=basic_items,
            completion_status=0.0,
            estimated_prep_time=18,
            priority_level='medium',
            auto_generated=True
        )
        
        self.preparation_cache[meeting.meeting_id] = preparation
        return preparation

    async def generate_meeting_insights(
        self, 
        current_meeting: Optional[MeetingContext],
        upcoming_meetings: List[MeetingContext],
        user_id: str
    ) -> Dict[str, Any]:
        """Generate AI insights about meeting context"""
        try:
            insights = {
                'meeting_load': len(upcoming_meetings),
                'focus_time_available': self.calculate_focus_time(upcoming_meetings),
                'preparation_urgency': 'low',
                'context_switches': 0
            }
            
            if current_meeting:
                insights['current_meeting_type'] = current_meeting.meeting_type
                insights['current_meeting_relevance'] = current_meeting.context_relevance
            
            # Calculate preparation urgency
            urgent_meetings = [
                m for m in upcoming_meetings 
                if (m.start_time - datetime.utcnow()).total_seconds() / 3600 <= 1
                and m.context_relevance > 0.7
            ]
            
            if urgent_meetings:
                insights['preparation_urgency'] = 'high'
            elif len(upcoming_meetings) > 3:
                insights['preparation_urgency'] = 'medium'
            
            return insights
            
        except Exception as e:
            logger.error(f"Failed to generate meeting insights: {e}")
            return {}

    def calculate_focus_time(self, upcoming_meetings: List[MeetingContext]) -> int:
        """Calculate available focus time between meetings (in minutes)"""
        if not upcoming_meetings:
            return 240  # 4 hours default
        
        current_time = datetime.utcnow()
        next_meeting = upcoming_meetings[0]
        
        time_until_next = (next_meeting.start_time - current_time).total_seconds() / 60
        return max(0, int(time_until_next - 15))  # Leave 15 min buffer

    def calculate_meeting_context_score(
        self, 
        current_meeting: Optional[MeetingContext],
        upcoming_meetings: List[MeetingContext]
    ) -> float:
        """Calculate overall meeting context score"""
        score = 0.0
        
        if current_meeting:
            score += 0.8  # High score for being in a meeting
        
        # Add score for upcoming meetings
        for meeting in upcoming_meetings:
            time_factor = max(0, 1 - (meeting.start_time - datetime.utcnow()).total_seconds() / 14400)  # 4 hours
            score += meeting.context_relevance * time_factor * 0.3
        
        return min(score, 1.0)

    def parse_datetime(self, datetime_str: Optional[str]) -> Optional[datetime]:
        """Parse datetime string from calendar event"""
        if not datetime_str:
            return None
        
        try:
            # Remove timezone info for simplicity
            if datetime_str.endswith('Z'):
                datetime_str = datetime_str[:-1]
            elif '+' in datetime_str:
                datetime_str = datetime_str.split('+')[0]
            
            return datetime.fromisoformat(datetime_str)
        except Exception:
            return None

    async def update_active_meetings(self):
        """Update active meetings status"""
        try:
            current_time = datetime.utcnow()
            
            # Remove ended meetings
            ended_meetings = [
                meeting_id for meeting_id, meeting in self.active_meetings.items()
                if meeting.end_time < current_time
            ]
            
            for meeting_id in ended_meetings:
                del self.active_meetings[meeting_id]
                
        except Exception as e:
            logger.error(f"Failed to update active meetings: {e}")
