"""
Comprehensive Security Tests for MK13 MVP
Tests authentication, encryption, rate limiting, and input validation
"""

import pytest
import asyncio
import json
import time
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient
from fastapi import HTTPException

# Import modules to test
from security import SecurityManager, initialize_security
from auth import AuthenticationManager, initialize_auth
from main import app

class TestSecurityManager:
    """Test SecurityManager functionality"""
    
    def setup_method(self):
        """Setup test environment"""
        self.security_manager = SecurityManager()
    
    def test_password_hashing(self):
        """Test password hashing and verification"""
        password = "TestPassword123!"
        
        # Test hashing
        hashed = self.security_manager.hash_password(password)
        assert hashed != password
        assert len(hashed) > 50  # bcrypt hashes are long
        
        # Test verification
        assert self.security_manager.verify_password(password, hashed)
        assert not self.security_manager.verify_password("wrong_password", hashed)
    
    def test_jwt_token_generation_and_verification(self):
        """Test JWT token generation and verification"""
        user_id = "test_user_123"
        email = "<EMAIL>"
        
        # Generate token
        token = self.security_manager.generate_jwt_token(user_id, email)
        assert isinstance(token, str)
        assert len(token) > 100  # JWT tokens are long
        
        # Verify token
        payload = self.security_manager.verify_jwt_token(token)
        assert payload['user_id'] == user_id
        assert payload['email'] == email
        assert 'iat' in payload
        assert 'exp' in payload
        assert 'jti' in payload
    
    def test_jwt_token_expiration(self):
        """Test JWT token expiration"""
        # Create a security manager with very short expiration
        security_manager = SecurityManager()
        security_manager.jwt_expiration_hours = 0.001  # ~3.6 seconds
        
        user_id = "test_user"
        email = "<EMAIL>"
        
        token = security_manager.generate_jwt_token(user_id, email)
        
        # Token should be valid immediately
        payload = security_manager.verify_jwt_token(token)
        assert payload['user_id'] == user_id
        
        # Wait for expiration
        time.sleep(4)
        
        # Token should be expired
        with pytest.raises(HTTPException) as exc_info:
            security_manager.verify_jwt_token(token)
        assert exc_info.value.status_code == 401
        assert "expired" in exc_info.value.detail.lower()
    
    def test_data_encryption_decryption(self):
        """Test data encryption and decryption"""
        sensitive_data = "This is sensitive information: API_KEY_12345"
        
        # Encrypt data
        encrypted = self.security_manager.encrypt_sensitive_data(sensitive_data)
        assert encrypted != sensitive_data
        assert len(encrypted) > len(sensitive_data)
        
        # Decrypt data
        decrypted = self.security_manager.decrypt_sensitive_data(encrypted)
        assert decrypted == sensitive_data
    
    def test_api_key_generation_and_verification(self):
        """Test API key generation and verification"""
        user_id = "test_user_456"
        
        # Generate API key
        api_key = self.security_manager.generate_api_key(user_id)
        assert isinstance(api_key, str)
        assert len(api_key) > 20
        
        # Verify API key
        verified_user_id = self.security_manager.verify_api_key(api_key)
        assert verified_user_id == user_id
        
        # Test invalid API key
        invalid_key = "invalid_key_123"
        assert self.security_manager.verify_api_key(invalid_key) is None
    
    def test_input_validation(self):
        """Test input validation functions"""
        # Test email validation
        assert self.security_manager.validate_email("<EMAIL>")
        assert self.security_manager.validate_email("<EMAIL>")
        assert not self.security_manager.validate_email("invalid_email")
        assert not self.security_manager.validate_email("@domain.com")
        assert not self.security_manager.validate_email("user@")
        
        # Test user ID validation
        assert self.security_manager.validate_user_id("user123")
        assert self.security_manager.validate_user_id("user_123-test")
        assert not self.security_manager.validate_user_id("user@123")
        assert not self.security_manager.validate_user_id("user 123")
        assert not self.security_manager.validate_user_id("a" * 51)  # Too long
    
    def test_password_strength_validation(self):
        """Test password strength validation"""
        # Strong password
        strong_password = "StrongPass123!"
        result = self.security_manager.validate_password_strength(strong_password)
        assert result['valid']
        assert result['strength'] == 'strong'
        assert len(result['issues']) == 0
        
        # Weak passwords
        weak_passwords = [
            "weak",  # Too short
            "weakpassword",  # No uppercase, numbers, special chars
            "WEAKPASSWORD",  # No lowercase, numbers, special chars
            "WeakPassword",  # No numbers, special chars
            "WeakPassword123",  # No special chars
        ]
        
        for weak_password in weak_passwords:
            result = self.security_manager.validate_password_strength(weak_password)
            assert not result['valid']
            assert result['strength'] == 'weak'
            assert len(result['issues']) > 0
    
    def test_input_sanitization(self):
        """Test input sanitization"""
        dangerous_inputs = [
            "<script>alert('xss')</script>",
            "'; DROP TABLE users; --",
            'user"name',
            "user\\name"
        ]
        
        for dangerous_input in dangerous_inputs:
            sanitized = self.security_manager.sanitize_input(dangerous_input)
            assert '<' not in sanitized
            assert '>' not in sanitized
            assert '"' not in sanitized
            assert "'" not in sanitized
            assert '\\' not in sanitized
    
    def test_rate_limiting(self):
        """Test rate limiting functionality"""
        identifier = "test_user_rate_limit"
        
        # Configure a very low limit for testing
        self.security_manager.rate_limits['test'] = {'requests': 3, 'window': 60}
        
        # First 3 requests should pass
        for i in range(3):
            assert self.security_manager.check_rate_limit(identifier, 'test')
        
        # 4th request should fail
        assert not self.security_manager.check_rate_limit(identifier, 'test')
        
        # Test with different identifier should pass
        assert self.security_manager.check_rate_limit("different_user", 'test')
    
    def test_failed_login_tracking(self):
        """Test failed login attempt tracking"""
        identifier = "test_user_login"
        
        # Configure low max attempts for testing
        self.security_manager.max_login_attempts = 3
        
        # First 2 failed attempts should not lock account
        for i in range(2):
            locked = self.security_manager.record_failed_login(identifier)
            assert not locked
        
        # 3rd failed attempt should lock account
        locked = self.security_manager.record_failed_login(identifier)
        assert locked
        
        # Clear attempts
        self.security_manager.clear_failed_attempts(identifier)
        locked = self.security_manager.record_failed_login(identifier)
        assert not locked
    
    def test_security_headers(self):
        """Test security headers generation"""
        headers = self.security_manager.get_security_headers()
        
        required_headers = [
            'X-Content-Type-Options',
            'X-Frame-Options',
            'X-XSS-Protection',
            'Strict-Transport-Security',
            'Content-Security-Policy',
            'Referrer-Policy'
        ]
        
        for header in required_headers:
            assert header in headers
            assert headers[header]  # Not empty
    
    def test_cors_configuration(self):
        """Test CORS configuration"""
        cors_config = self.security_manager.get_cors_config()
        
        assert 'allow_origins' in cors_config
        assert 'allow_credentials' in cors_config
        assert 'allow_methods' in cors_config
        assert 'allow_headers' in cors_config
        
        assert cors_config['allow_credentials'] is True
        assert isinstance(cors_config['allow_origins'], list)
        assert isinstance(cors_config['allow_methods'], list)

class TestAuthenticationManager:
    """Test AuthenticationManager functionality"""
    
    def setup_method(self):
        """Setup test environment"""
        with patch.dict('os.environ', {
            'GOOGLE_CLIENT_ID': 'test_client_id',
            'GOOGLE_CLIENT_SECRET': 'test_client_secret',
            'GOOGLE_REDIRECT_URI': 'http://localhost:8000/auth/google/callback'
        }):
            self.auth_manager = AuthenticationManager()
    
    def test_google_auth_url_generation(self):
        """Test Google OAuth URL generation"""
        user_id = "test_user"
        
        with patch.object(self.auth_manager, '_create_google_flow') as mock_flow:
            mock_flow_instance = Mock()
            mock_flow_instance.authorization_url.return_value = (
                "https://accounts.google.com/oauth2/auth?client_id=test",
                "test_state"
            )
            mock_flow.return_value = mock_flow_instance
            
            result = self.auth_manager.generate_google_auth_url(user_id)
            
            assert 'auth_url' in result
            assert 'state' in result
            assert result['auth_url'].startswith('https://accounts.google.com')
            assert result['state'] in self.auth_manager.oauth_states
    
    @pytest.mark.asyncio
    async def test_session_management(self):
        """Test session creation and validation"""
        user_id = "test_user"
        mock_credentials = Mock()
        
        # Create session
        session_token = await self.auth_manager._create_session(user_id, mock_credentials)
        assert isinstance(session_token, str)
        assert len(session_token) > 20
        
        # Validate session
        session_data = await self.auth_manager.validate_session(session_token)
        assert session_data is not None
        assert session_data['user_id'] == user_id
        
        # Test invalid session
        invalid_session = await self.auth_manager.validate_session("invalid_token")
        assert invalid_session is None
    
    @pytest.mark.asyncio
    async def test_session_expiration(self):
        """Test session expiration"""
        user_id = "test_user"
        mock_credentials = Mock()
        
        # Create session with short timeout
        self.auth_manager.session_timeout = timedelta(seconds=1)
        session_token = await self.auth_manager._create_session(user_id, mock_credentials)
        
        # Session should be valid immediately
        session_data = await self.auth_manager.validate_session(session_token)
        assert session_data is not None
        
        # Wait for expiration
        await asyncio.sleep(2)
        
        # Session should be expired
        session_data = await self.auth_manager.validate_session(session_token)
        assert session_data is None
    
    @pytest.mark.asyncio
    async def test_logout(self):
        """Test user logout"""
        user_id = "test_user"
        mock_credentials = Mock()
        
        # Create session
        session_token = await self.auth_manager._create_session(user_id, mock_credentials)
        
        # Verify session exists
        session_data = await self.auth_manager.validate_session(session_token)
        assert session_data is not None
        
        # Logout
        success = await self.auth_manager.logout(session_token)
        assert success
        
        # Session should be invalid after logout
        session_data = await self.auth_manager.validate_session(session_token)
        assert session_data is None

class TestIntegration:
    """Integration tests for security and authentication"""
    
    def setup_method(self):
        """Setup test client"""
        self.client = TestClient(app)
    
    def test_security_headers_in_response(self):
        """Test that security headers are included in responses"""
        response = self.client.get("/health")
        
        # Check for security headers
        assert 'X-Content-Type-Options' in response.headers
        assert 'X-Frame-Options' in response.headers
        assert 'X-XSS-Protection' in response.headers
        assert response.headers['X-Content-Type-Options'] == 'nosniff'
        assert response.headers['X-Frame-Options'] == 'DENY'
    
    def test_cors_configuration(self):
        """Test CORS configuration"""
        # Test preflight request
        response = self.client.options(
            "/api/health",
            headers={
                "Origin": "http://localhost:3000",
                "Access-Control-Request-Method": "GET"
            }
        )
        
        # Should have CORS headers
        assert 'Access-Control-Allow-Origin' in response.headers
        assert 'Access-Control-Allow-Methods' in response.headers
    
    def test_authentication_required_endpoints(self):
        """Test that protected endpoints require authentication"""
        # Test endpoint that requires authentication
        response = self.client.get("/api/auth/me")
        assert response.status_code == 401
        
        # Test with invalid token
        response = self.client.get(
            "/api/auth/me",
            headers={"X-Session-Token": "invalid_token"}
        )
        assert response.status_code == 401

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
