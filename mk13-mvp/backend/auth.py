"""
Authentication Module for MK13 MVP
Implements OAuth2 authentication with Google and session management
"""

import os
import json
import logging
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import Flow
from googleapiclient.discovery import build
from fastapi import HTTPEx<PERSON>, Request as FastAPIRequest, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import secrets
import asyncio
from security import get_security_manager, SecurityManager

logger = logging.getLogger(__name__)

class AuthenticationManager:
    """Manages authentication and authorization for MK13 MVP"""
    
    def __init__(self):
        self.security_manager = get_security_manager()
        self.google_client_id = os.getenv('GOOGLE_CLIENT_ID')
        self.google_client_secret = os.getenv('GOOGLE_CLIENT_SECRET')
        self.redirect_uri = os.getenv('GOOGLE_REDIRECT_URI', 'http://localhost:8000/auth/google/callback')
        
        # OAuth2 scopes for Google services
        self.google_scopes = [
            'openid',
            'email',
            'profile',
            'https://www.googleapis.com/auth/gmail.readonly',
            'https://www.googleapis.com/auth/gmail.send',
            'https://www.googleapis.com/auth/calendar.readonly',
            'https://www.googleapis.com/auth/calendar.events',
            'https://www.googleapis.com/auth/drive.readonly',
            'https://www.googleapis.com/auth/drive.file'
        ]
        
        # Session storage (in production, use Redis or database)
        self.active_sessions = {}
        self.oauth_states = {}
        
        # Security configuration
        self.session_timeout = timedelta(hours=8)
        self.refresh_token_timeout = timedelta(days=30)
        
        logger.info("Authentication Manager initialized")
    
    def _create_google_flow(self) -> Flow:
        """Create Google OAuth2 flow"""
        if not self.google_client_id or not self.google_client_secret:
            raise ValueError("Google OAuth2 credentials not configured")
        
        client_config = {
            "web": {
                "client_id": self.google_client_id,
                "client_secret": self.google_client_secret,
                "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                "token_uri": "https://oauth2.googleapis.com/token",
                "redirect_uris": [self.redirect_uri]
            }
        }
        
        flow = Flow.from_client_config(
            client_config,
            scopes=self.google_scopes,
            redirect_uri=self.redirect_uri
        )
        
        return flow
    
    def generate_google_auth_url(self, user_id: str = None) -> Dict[str, str]:
        """Generate Google OAuth2 authorization URL"""
        try:
            flow = self._create_google_flow()
            
            # Generate secure state parameter
            state = self.security_manager.generate_secure_token()
            
            # Store state for verification
            self.oauth_states[state] = {
                'user_id': user_id,
                'created_at': datetime.utcnow(),
                'expires_at': datetime.utcnow() + timedelta(minutes=10)
            }
            
            # Generate authorization URL
            auth_url, _ = flow.authorization_url(
                access_type='offline',
                include_granted_scopes='true',
                state=state,
                prompt='consent'  # Force consent to get refresh token
            )
            
            logger.info(f"Generated Google auth URL for user {user_id}")
            
            return {
                'auth_url': auth_url,
                'state': state
            }
            
        except Exception as e:
            logger.error(f"Error generating Google auth URL: {e}")
            raise HTTPException(status_code=500, detail="Failed to generate authorization URL")
    
    async def handle_google_callback(self, code: str, state: str) -> Dict[str, Any]:
        """Handle Google OAuth2 callback"""
        try:
            # Verify state parameter
            if state not in self.oauth_states:
                raise HTTPException(status_code=400, detail="Invalid state parameter")
            
            state_data = self.oauth_states[state]
            
            # Check if state has expired
            if datetime.utcnow() > state_data['expires_at']:
                del self.oauth_states[state]
                raise HTTPException(status_code=400, detail="Authorization request expired")
            
            # Exchange code for tokens
            flow = self._create_google_flow()
            flow.fetch_token(code=code)
            
            credentials = flow.credentials
            
            # Get user info from Google
            user_info = await self._get_google_user_info(credentials)
            
            # Create or update user
            user_data = await self._create_or_update_user(user_info, credentials)
            
            # Generate JWT token
            jwt_token = self.security_manager.generate_jwt_token(
                user_id=user_data['id'],
                email=user_data['email'],
                additional_claims={
                    'name': user_data['name'],
                    'picture': user_data.get('picture'),
                    'google_authenticated': True
                }
            )
            
            # Create session
            session_token = await self._create_session(user_data['id'], credentials)
            
            # Clean up state
            del self.oauth_states[state]
            
            logger.info(f"Successfully authenticated user {user_data['email']}")
            
            return {
                'user': user_data,
                'jwt_token': jwt_token,
                'session_token': session_token,
                'expires_in': 3600 * 24  # 24 hours
            }
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error handling Google callback: {e}")
            raise HTTPException(status_code=500, detail="Authentication failed")
    
    async def _get_google_user_info(self, credentials: Credentials) -> Dict[str, Any]:
        """Get user information from Google"""
        try:
            service = build('oauth2', 'v2', credentials=credentials)
            user_info = service.userinfo().get().execute()
            
            return {
                'google_id': user_info['id'],
                'email': user_info['email'],
                'name': user_info['name'],
                'picture': user_info.get('picture'),
                'verified_email': user_info.get('verified_email', False)
            }
            
        except Exception as e:
            logger.error(f"Error getting Google user info: {e}")
            raise HTTPException(status_code=500, detail="Failed to get user information")
    
    async def _create_or_update_user(self, user_info: Dict[str, Any], credentials: Credentials) -> Dict[str, Any]:
        """Create or update user in database"""
        try:
            from database import get_managers
            
            managers = get_managers()
            user_manager = managers['users']
            
            # Check if user exists
            existing_user = await user_manager.get_user_by_email(user_info['email'])
            
            # Encrypt and store Google credentials
            encrypted_credentials = self.security_manager.encrypt_sensitive_data(
                json.dumps({
                    'token': credentials.token,
                    'refresh_token': credentials.refresh_token,
                    'token_uri': credentials.token_uri,
                    'client_id': credentials.client_id,
                    'client_secret': credentials.client_secret,
                    'scopes': credentials.scopes
                })
            )
            
            if existing_user:
                # Update existing user
                updated_user = await user_manager.update_user(
                    user_id=existing_user['id'],
                    updates={
                        'name': user_info['name'],
                        'picture': user_info.get('picture'),
                        'google_token': encrypted_credentials,
                        'last_login': datetime.utcnow().isoformat(),
                        'verified_email': user_info.get('verified_email', False)
                    }
                )
                return updated_user
            else:
                # Create new user
                new_user = await user_manager.create_user(
                    email=user_info['email'],
                    name=user_info['name'],
                    google_token=encrypted_credentials,
                    metadata={
                        'google_id': user_info['google_id'],
                        'picture': user_info.get('picture'),
                        'verified_email': user_info.get('verified_email', False),
                        'created_via': 'google_oauth'
                    }
                )
                return new_user
                
        except Exception as e:
            logger.error(f"Error creating/updating user: {e}")
            raise HTTPException(status_code=500, detail="Failed to create user")
    
    async def _create_session(self, user_id: str, credentials: Credentials) -> str:
        """Create user session"""
        session_token = self.security_manager.generate_session_token()
        
        session_data = {
            'user_id': user_id,
            'created_at': datetime.utcnow(),
            'expires_at': datetime.utcnow() + self.session_timeout,
            'credentials': credentials,
            'last_activity': datetime.utcnow()
        }
        
        self.active_sessions[session_token] = session_data
        
        # Clean up expired sessions
        await self._cleanup_expired_sessions()
        
        return session_token
    
    async def _cleanup_expired_sessions(self):
        """Clean up expired sessions"""
        now = datetime.utcnow()
        expired_sessions = [
            token for token, data in self.active_sessions.items()
            if data['expires_at'] < now
        ]
        
        for token in expired_sessions:
            del self.active_sessions[token]
        
        if expired_sessions:
            logger.info(f"Cleaned up {len(expired_sessions)} expired sessions")
    
    async def validate_session(self, session_token: str) -> Optional[Dict[str, Any]]:
        """Validate session token"""
        if session_token not in self.active_sessions:
            return None
        
        session_data = self.active_sessions[session_token]
        
        # Check if session has expired
        if datetime.utcnow() > session_data['expires_at']:
            del self.active_sessions[session_token]
            return None
        
        # Update last activity
        session_data['last_activity'] = datetime.utcnow()
        
        return session_data
    
    async def refresh_google_credentials(self, user_id: str) -> Optional[Credentials]:
        """Refresh Google credentials for user"""
        try:
            from database import get_managers
            
            managers = get_managers()
            user = await managers['users'].get_user_by_id(user_id)
            
            if not user or not user.get('google_token'):
                return None
            
            # Decrypt stored credentials
            encrypted_token = user['google_token']
            decrypted_data = self.security_manager.decrypt_sensitive_data(encrypted_token)
            cred_data = json.loads(decrypted_data)
            
            # Create credentials object
            credentials = Credentials(
                token=cred_data['token'],
                refresh_token=cred_data['refresh_token'],
                token_uri=cred_data['token_uri'],
                client_id=cred_data['client_id'],
                client_secret=cred_data['client_secret'],
                scopes=cred_data['scopes']
            )
            
            # Refresh if needed
            if credentials.expired:
                credentials.refresh(Request())
                
                # Update stored credentials
                updated_cred_data = {
                    'token': credentials.token,
                    'refresh_token': credentials.refresh_token,
                    'token_uri': credentials.token_uri,
                    'client_id': credentials.client_id,
                    'client_secret': credentials.client_secret,
                    'scopes': credentials.scopes
                }
                
                encrypted_updated = self.security_manager.encrypt_sensitive_data(
                    json.dumps(updated_cred_data)
                )
                
                await managers['users'].update_user(
                    user_id=user_id,
                    updates={'google_token': encrypted_updated}
                )
            
            return credentials
            
        except Exception as e:
            logger.error(f"Error refreshing Google credentials: {e}")
            return None
    
    async def logout(self, session_token: str) -> bool:
        """Logout user and invalidate session"""
        if session_token in self.active_sessions:
            del self.active_sessions[session_token]
            logger.info("User logged out successfully")
            return True
        return False
    
    async def get_user_credentials(self, user_id: str) -> Optional[Credentials]:
        """Get Google credentials for user"""
        return await self.refresh_google_credentials(user_id)

# Global authentication manager instance
auth_manager = None

def initialize_auth():
    """Initialize authentication manager"""
    global auth_manager
    auth_manager = AuthenticationManager()
    logger.info("Authentication system initialized")
    return auth_manager

def get_auth_manager():
    """Get authentication manager instance"""
    return auth_manager

# FastAPI dependencies
async def get_current_user_from_session(request: FastAPIRequest) -> Dict[str, Any]:
    """Get current user from session token"""
    session_token = request.headers.get('X-Session-Token')
    if not session_token:
        raise HTTPException(status_code=401, detail="Session token required")
    
    session_data = await auth_manager.validate_session(session_token)
    if not session_data:
        raise HTTPException(status_code=401, detail="Invalid or expired session")
    
    return session_data

async def require_google_auth(user_data: Dict[str, Any] = Depends(get_current_user_from_session)) -> Dict[str, Any]:
    """Require Google authentication"""
    if not user_data.get('credentials'):
        raise HTTPException(status_code=401, detail="Google authentication required")
    
    return user_data
