"""
Real-time Collaboration Hub for MK13 MVP
WebRTC-based communication, shared workspaces, and collaborative AI sessions
"""

import asyncio
import json
import logging
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass, asdict
from enum import Enum
import websockets
from websockets.server import WebSocketServerProtocol
from database import get_managers
from ai_service import get_ai_service
from enhanced_context_intelligence import get_enhanced_context_intelligence

logger = logging.getLogger(__name__)

class CollaborationEventType(Enum):
    USER_JOINED = "user_joined"
    USER_LEFT = "user_left"
    CURSOR_MOVE = "cursor_move"
    TEXT_EDIT = "text_edit"
    VOICE_START = "voice_start"
    VOICE_END = "voice_end"
    SCREEN_SHARE = "screen_share"
    AI_REQUEST = "ai_request"
    AI_RESPONSE = "ai_response"
    CONTEXT_SYNC = "context_sync"
    WORKSPACE_UPDATE = "workspace_update"

class ParticipantRole(Enum):
    OWNER = "owner"
    COLLABORATOR = "collaborator"
    VIEWER = "viewer"
    AI_ASSISTANT = "ai_assistant"

@dataclass
class Participant:
    user_id: str
    session_id: str
    name: str
    role: ParticipantRole
    websocket: Optional[WebSocketServerProtocol]
    joined_at: datetime
    last_activity: datetime
    cursor_position: Optional[Dict[str, Any]]
    is_speaking: bool
    is_screen_sharing: bool
    context_data: Dict[str, Any]

@dataclass
class SharedWorkspace:
    workspace_id: str
    name: str
    owner_id: str
    created_at: datetime
    last_modified: datetime
    participants: Dict[str, Participant]
    content: Dict[str, Any]
    ai_context: Dict[str, Any]
    settings: Dict[str, Any]
    version: int

@dataclass
class CollaborationEvent:
    event_id: str
    event_type: CollaborationEventType
    workspace_id: str
    user_id: str
    timestamp: datetime
    data: Dict[str, Any]
    version: int

@dataclass
class AICollaborationSession:
    session_id: str
    workspace_id: str
    participants: List[str]
    ai_context: Dict[str, Any]
    conversation_history: List[Dict[str, Any]]
    active_requests: Dict[str, Any]
    created_at: datetime
    last_activity: datetime

class CollaborationHub:
    """Real-time collaboration hub with WebRTC and AI integration"""
    
    def __init__(self):
        self.active_workspaces: Dict[str, SharedWorkspace] = {}
        self.user_sessions: Dict[str, Set[str]] = {}  # user_id -> workspace_ids
        self.websocket_connections: Dict[str, WebSocketServerProtocol] = {}
        self.ai_sessions: Dict[str, AICollaborationSession] = {}
        self.event_history: Dict[str, List[CollaborationEvent]] = {}
        
        # WebRTC signaling
        self.webrtc_offers: Dict[str, Dict] = {}
        self.webrtc_answers: Dict[str, Dict] = {}
        self.ice_candidates: Dict[str, List[Dict]] = {}
        
        # AI service integration
        self.ai_service = None
        self.context_intelligence = None
        
    async def initialize(self):
        """Initialize the collaboration hub"""
        try:
            self.ai_service = get_ai_service()
            self.context_intelligence = get_enhanced_context_intelligence()
            
            logger.info("Collaboration Hub initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Collaboration Hub: {e}")
            raise

    async def create_workspace(self, owner_id: str, workspace_data: Dict[str, Any]) -> SharedWorkspace:
        """Create a new shared workspace"""
        try:
            workspace_id = str(uuid.uuid4())
            now = datetime.utcnow()
            
            # Create owner participant
            owner_participant = Participant(
                user_id=owner_id,
                session_id=str(uuid.uuid4()),
                name=workspace_data.get('owner_name', 'Owner'),
                role=ParticipantRole.OWNER,
                websocket=None,
                joined_at=now,
                last_activity=now,
                cursor_position=None,
                is_speaking=False,
                is_screen_sharing=False,
                context_data={}
            )
            
            workspace = SharedWorkspace(
                workspace_id=workspace_id,
                name=workspace_data.get('name', 'Untitled Workspace'),
                owner_id=owner_id,
                created_at=now,
                last_modified=now,
                participants={owner_id: owner_participant},
                content=workspace_data.get('content', {}),
                ai_context={
                    'enabled': workspace_data.get('ai_enabled', True),
                    'model': workspace_data.get('ai_model', 'gpt-4'),
                    'context_sharing': workspace_data.get('context_sharing', True)
                },
                settings=workspace_data.get('settings', {
                    'max_participants': 10,
                    'allow_voice': True,
                    'allow_screen_share': True,
                    'auto_save': True,
                    'version_history': True
                }),
                version=1
            )
            
            # Store workspace
            self.active_workspaces[workspace_id] = workspace
            
            # Update user sessions
            if owner_id not in self.user_sessions:
                self.user_sessions[owner_id] = set()
            self.user_sessions[owner_id].add(workspace_id)
            
            # Initialize event history
            self.event_history[workspace_id] = []
            
            # Create AI collaboration session
            if workspace.ai_context['enabled']:
                await self.create_ai_session(workspace_id, [owner_id])
            
            # Save to database
            await self.save_workspace_to_database(workspace)
            
            logger.info(f"Created workspace {workspace_id} for user {owner_id}")
            return workspace
            
        except Exception as e:
            logger.error(f"Failed to create workspace: {e}")
            raise

    async def join_workspace(self, workspace_id: str, user_id: str, user_data: Dict[str, Any]) -> Participant:
        """Join an existing workspace"""
        try:
            if workspace_id not in self.active_workspaces:
                raise ValueError(f"Workspace {workspace_id} not found")
            
            workspace = self.active_workspaces[workspace_id]
            
            # Check if user is already in workspace
            if user_id in workspace.participants:
                return workspace.participants[user_id]
            
            # Check participant limit
            if len(workspace.participants) >= workspace.settings.get('max_participants', 10):
                raise ValueError("Workspace is full")
            
            # Determine role
            role = ParticipantRole.COLLABORATOR
            if user_data.get('role'):
                role = ParticipantRole(user_data['role'])
            
            # Create participant
            participant = Participant(
                user_id=user_id,
                session_id=str(uuid.uuid4()),
                name=user_data.get('name', f'User {user_id[:8]}'),
                role=role,
                websocket=None,
                joined_at=datetime.utcnow(),
                last_activity=datetime.utcnow(),
                cursor_position=None,
                is_speaking=False,
                is_screen_sharing=False,
                context_data=user_data.get('context', {})
            )
            
            # Add to workspace
            workspace.participants[user_id] = participant
            workspace.last_modified = datetime.utcnow()
            
            # Update user sessions
            if user_id not in self.user_sessions:
                self.user_sessions[user_id] = set()
            self.user_sessions[user_id].add(workspace_id)
            
            # Add to AI session if enabled
            if workspace.ai_context['enabled'] and workspace_id in self.ai_sessions:
                self.ai_sessions[workspace_id].participants.append(user_id)
            
            # Broadcast user joined event
            await self.broadcast_event(workspace_id, CollaborationEvent(
                event_id=str(uuid.uuid4()),
                event_type=CollaborationEventType.USER_JOINED,
                workspace_id=workspace_id,
                user_id=user_id,
                timestamp=datetime.utcnow(),
                data={
                    'participant': asdict(participant),
                    'workspace_info': {
                        'name': workspace.name,
                        'participant_count': len(workspace.participants)
                    }
                },
                version=workspace.version
            ))
            
            logger.info(f"User {user_id} joined workspace {workspace_id}")
            return participant
            
        except Exception as e:
            logger.error(f"Failed to join workspace: {e}")
            raise

    async def leave_workspace(self, workspace_id: str, user_id: str):
        """Leave a workspace"""
        try:
            if workspace_id not in self.active_workspaces:
                return
            
            workspace = self.active_workspaces[workspace_id]
            
            if user_id not in workspace.participants:
                return
            
            participant = workspace.participants[user_id]
            
            # Close WebSocket connection
            if participant.websocket:
                await participant.websocket.close()
            
            # Remove from workspace
            del workspace.participants[user_id]
            workspace.last_modified = datetime.utcnow()
            
            # Update user sessions
            if user_id in self.user_sessions:
                self.user_sessions[user_id].discard(workspace_id)
                if not self.user_sessions[user_id]:
                    del self.user_sessions[user_id]
            
            # Remove from AI session
            if workspace_id in self.ai_sessions:
                ai_session = self.ai_sessions[workspace_id]
                if user_id in ai_session.participants:
                    ai_session.participants.remove(user_id)
            
            # Broadcast user left event
            await self.broadcast_event(workspace_id, CollaborationEvent(
                event_id=str(uuid.uuid4()),
                event_type=CollaborationEventType.USER_LEFT,
                workspace_id=workspace_id,
                user_id=user_id,
                timestamp=datetime.utcnow(),
                data={
                    'participant_name': participant.name,
                    'participant_count': len(workspace.participants)
                },
                version=workspace.version
            ))
            
            # Clean up empty workspace
            if not workspace.participants:
                await self.cleanup_workspace(workspace_id)
            
            logger.info(f"User {user_id} left workspace {workspace_id}")
            
        except Exception as e:
            logger.error(f"Failed to leave workspace: {e}")

    async def handle_websocket_connection(self, websocket: WebSocketServerProtocol, path: str):
        """Handle WebSocket connection for real-time collaboration"""
        try:
            # Parse connection parameters
            query_params = self.parse_websocket_path(path)
            workspace_id = query_params.get('workspace_id')
            user_id = query_params.get('user_id')
            
            if not workspace_id or not user_id:
                await websocket.close(code=4000, reason="Missing parameters")
                return
            
            # Validate workspace and user
            if workspace_id not in self.active_workspaces:
                await websocket.close(code=4004, reason="Workspace not found")
                return
            
            workspace = self.active_workspaces[workspace_id]
            if user_id not in workspace.participants:
                await websocket.close(code=4003, reason="User not in workspace")
                return
            
            # Associate WebSocket with participant
            participant = workspace.participants[user_id]
            participant.websocket = websocket
            self.websocket_connections[f"{workspace_id}:{user_id}"] = websocket
            
            logger.info(f"WebSocket connected: {user_id} in workspace {workspace_id}")
            
            # Send initial workspace state
            await self.send_workspace_state(websocket, workspace)
            
            # Handle messages
            async for message in websocket:
                try:
                    data = json.loads(message)
                    await self.handle_websocket_message(workspace_id, user_id, data)
                except json.JSONDecodeError:
                    logger.warning(f"Invalid JSON from {user_id}: {message}")
                except Exception as e:
                    logger.error(f"Error handling message from {user_id}: {e}")
            
        except websockets.exceptions.ConnectionClosed:
            logger.info(f"WebSocket connection closed for {user_id}")
        except Exception as e:
            logger.error(f"WebSocket error: {e}")
        finally:
            # Clean up connection
            if workspace_id and user_id:
                connection_key = f"{workspace_id}:{user_id}"
                if connection_key in self.websocket_connections:
                    del self.websocket_connections[connection_key]
                
                # Update participant
                if (workspace_id in self.active_workspaces and 
                    user_id in self.active_workspaces[workspace_id].participants):
                    self.active_workspaces[workspace_id].participants[user_id].websocket = None

    async def handle_websocket_message(self, workspace_id: str, user_id: str, data: Dict[str, Any]):
        """Handle incoming WebSocket message"""
        try:
            message_type = data.get('type')
            
            if message_type == 'cursor_move':
                await self.handle_cursor_move(workspace_id, user_id, data)
            elif message_type == 'text_edit':
                await self.handle_text_edit(workspace_id, user_id, data)
            elif message_type == 'voice_start':
                await self.handle_voice_start(workspace_id, user_id, data)
            elif message_type == 'voice_end':
                await self.handle_voice_end(workspace_id, user_id, data)
            elif message_type == 'screen_share':
                await self.handle_screen_share(workspace_id, user_id, data)
            elif message_type == 'ai_request':
                await self.handle_ai_request(workspace_id, user_id, data)
            elif message_type == 'webrtc_offer':
                await self.handle_webrtc_offer(workspace_id, user_id, data)
            elif message_type == 'webrtc_answer':
                await self.handle_webrtc_answer(workspace_id, user_id, data)
            elif message_type == 'ice_candidate':
                await self.handle_ice_candidate(workspace_id, user_id, data)
            else:
                logger.warning(f"Unknown message type: {message_type}")
                
        except Exception as e:
            logger.error(f"Error handling WebSocket message: {e}")

    async def handle_cursor_move(self, workspace_id: str, user_id: str, data: Dict[str, Any]):
        """Handle cursor movement"""
        try:
            workspace = self.active_workspaces[workspace_id]
            participant = workspace.participants[user_id]
            
            # Update cursor position
            participant.cursor_position = data.get('position')
            participant.last_activity = datetime.utcnow()
            
            # Broadcast to other participants
            event = CollaborationEvent(
                event_id=str(uuid.uuid4()),
                event_type=CollaborationEventType.CURSOR_MOVE,
                workspace_id=workspace_id,
                user_id=user_id,
                timestamp=datetime.utcnow(),
                data={
                    'position': participant.cursor_position,
                    'user_name': participant.name
                },
                version=workspace.version
            )
            
            await self.broadcast_event(workspace_id, event, exclude_user=user_id)
            
        except Exception as e:
            logger.error(f"Error handling cursor move: {e}")

    async def handle_text_edit(self, workspace_id: str, user_id: str, data: Dict[str, Any]):
        """Handle text editing with operational transformation"""
        try:
            workspace = self.active_workspaces[workspace_id]
            participant = workspace.participants[user_id]
            
            # Apply operational transformation
            transformed_operation = await self.apply_operational_transform(
                workspace, data.get('operation'), data.get('version', 0)
            )
            
            # Update workspace content
            await self.apply_text_operation(workspace, transformed_operation)
            
            # Increment version
            workspace.version += 1
            workspace.last_modified = datetime.utcnow()
            participant.last_activity = datetime.utcnow()
            
            # Broadcast to other participants
            event = CollaborationEvent(
                event_id=str(uuid.uuid4()),
                event_type=CollaborationEventType.TEXT_EDIT,
                workspace_id=workspace_id,
                user_id=user_id,
                timestamp=datetime.utcnow(),
                data={
                    'operation': transformed_operation,
                    'user_name': participant.name
                },
                version=workspace.version
            )
            
            await self.broadcast_event(workspace_id, event, exclude_user=user_id)
            
            # Auto-save if enabled
            if workspace.settings.get('auto_save', True):
                await self.save_workspace_to_database(workspace)
            
        except Exception as e:
            logger.error(f"Error handling text edit: {e}")

    async def create_ai_session(self, workspace_id: str, participants: List[str]) -> AICollaborationSession:
        """Create AI collaboration session"""
        try:
            session_id = str(uuid.uuid4())
            
            ai_session = AICollaborationSession(
                session_id=session_id,
                workspace_id=workspace_id,
                participants=participants.copy(),
                ai_context={
                    'model': 'gpt-4',
                    'temperature': 0.7,
                    'context_window': 4000,
                    'collaborative_mode': True
                },
                conversation_history=[],
                active_requests={},
                created_at=datetime.utcnow(),
                last_activity=datetime.utcnow()
            )
            
            self.ai_sessions[workspace_id] = ai_session
            
            logger.info(f"Created AI session {session_id} for workspace {workspace_id}")
            return ai_session
            
        except Exception as e:
            logger.error(f"Failed to create AI session: {e}")
            raise

    async def handle_ai_request(self, workspace_id: str, user_id: str, data: Dict[str, Any]):
        """Handle AI request in collaborative context"""
        try:
            if workspace_id not in self.ai_sessions:
                await self.create_ai_session(workspace_id, [user_id])
            
            ai_session = self.ai_sessions[workspace_id]
            request_id = str(uuid.uuid4())
            
            # Get enhanced context for all participants
            participant_contexts = {}
            workspace = self.active_workspaces[workspace_id]
            
            for participant_id in ai_session.participants:
                if self.context_intelligence:
                    try:
                        context = await self.context_intelligence.detect_enhanced_context(participant_id)
                        participant_contexts[participant_id] = {
                            'primary_type': context.primary_type.value,
                            'confidence': context.confidence,
                            'active_applications': [app.name for app in context.active_applications[:3]]
                        }
                    except Exception:
                        participant_contexts[participant_id] = {'primary_type': 'unknown'}
            
            # Prepare AI request with collaborative context
            ai_request = {
                'request_id': request_id,
                'user_id': user_id,
                'message': data.get('message', ''),
                'workspace_context': {
                    'workspace_id': workspace_id,
                    'workspace_name': workspace.name,
                    'participant_count': len(workspace.participants),
                    'participant_contexts': participant_contexts,
                    'workspace_content': workspace.content,
                    'conversation_history': ai_session.conversation_history[-10:]  # Last 10 messages
                },
                'timestamp': datetime.utcnow().isoformat()
            }
            
            # Store active request
            ai_session.active_requests[request_id] = ai_request
            
            # Process AI request
            asyncio.create_task(self.process_collaborative_ai_request(workspace_id, ai_request))
            
            # Broadcast AI request event
            event = CollaborationEvent(
                event_id=str(uuid.uuid4()),
                event_type=CollaborationEventType.AI_REQUEST,
                workspace_id=workspace_id,
                user_id=user_id,
                timestamp=datetime.utcnow(),
                data={
                    'request_id': request_id,
                    'message': data.get('message', ''),
                    'user_name': workspace.participants[user_id].name
                },
                version=workspace.version
            )
            
            await self.broadcast_event(workspace_id, event)
            
        except Exception as e:
            logger.error(f"Error handling AI request: {e}")

    async def process_collaborative_ai_request(self, workspace_id: str, ai_request: Dict[str, Any]):
        """Process AI request with collaborative context"""
        try:
            ai_session = self.ai_sessions[workspace_id]
            request_id = ai_request['request_id']
            
            # Prepare enhanced prompt with collaborative context
            enhanced_prompt = f"""
            You are an AI assistant helping in a collaborative workspace.
            
            Workspace: {ai_request['workspace_context']['workspace_name']}
            Participants: {ai_request['workspace_context']['participant_count']}
            
            Participant Contexts:
            {json.dumps(ai_request['workspace_context']['participant_contexts'], indent=2)}
            
            Recent Conversation:
            {json.dumps(ai_request['workspace_context']['conversation_history'], indent=2)}
            
            Current Request: {ai_request['message']}
            
            Please provide a helpful response considering the collaborative context and all participants.
            """
            
            # Get AI response
            ai_response = await self.ai_service.process_text_input(
                user_id=ai_request['user_id'],
                message=enhanced_prompt,
                context_id=workspace_id
            )
            
            # Add to conversation history
            ai_session.conversation_history.append({
                'user_id': ai_request['user_id'],
                'message': ai_request['message'],
                'response': ai_response['response'],
                'timestamp': ai_request['timestamp']
            })
            
            # Remove from active requests
            if request_id in ai_session.active_requests:
                del ai_session.active_requests[request_id]
            
            ai_session.last_activity = datetime.utcnow()
            
            # Broadcast AI response
            event = CollaborationEvent(
                event_id=str(uuid.uuid4()),
                event_type=CollaborationEventType.AI_RESPONSE,
                workspace_id=workspace_id,
                user_id='ai_assistant',
                timestamp=datetime.utcnow(),
                data={
                    'request_id': request_id,
                    'response': ai_response['response'],
                    'original_user': ai_request['user_id'],
                    'model_used': ai_response.get('model_used', 'gpt-4')
                },
                version=self.active_workspaces[workspace_id].version
            )
            
            await self.broadcast_event(workspace_id, event)
            
        except Exception as e:
            logger.error(f"Error processing collaborative AI request: {e}")

    async def broadcast_event(self, workspace_id: str, event: CollaborationEvent, exclude_user: Optional[str] = None):
        """Broadcast event to all participants in workspace"""
        try:
            if workspace_id not in self.active_workspaces:
                return
            
            workspace = self.active_workspaces[workspace_id]
            event_data = {
                'type': 'collaboration_event',
                'event': asdict(event)
            }
            
            # Add to event history
            self.event_history[workspace_id].append(event)
            
            # Keep only last 1000 events
            if len(self.event_history[workspace_id]) > 1000:
                self.event_history[workspace_id] = self.event_history[workspace_id][-1000:]
            
            # Send to all participants
            for user_id, participant in workspace.participants.items():
                if exclude_user and user_id == exclude_user:
                    continue
                
                if participant.websocket:
                    try:
                        await participant.websocket.send(json.dumps(event_data))
                    except websockets.exceptions.ConnectionClosed:
                        participant.websocket = None
                    except Exception as e:
                        logger.error(f"Error sending event to {user_id}: {e}")
            
        except Exception as e:
            logger.error(f"Error broadcasting event: {e}")

    def parse_websocket_path(self, path: str) -> Dict[str, str]:
        """Parse WebSocket connection path for parameters"""
        try:
            # Simple query parameter parsing
            if '?' not in path:
                return {}
            
            query_string = path.split('?', 1)[1]
            params = {}
            
            for param in query_string.split('&'):
                if '=' in param:
                    key, value = param.split('=', 1)
                    params[key] = value
            
            return params
            
        except Exception as e:
            logger.error(f"Error parsing WebSocket path: {e}")
            return {}

    async def save_workspace_to_database(self, workspace: SharedWorkspace):
        """Save workspace to database"""
        try:
            # This would save to database
            # For now, just log
            logger.info(f"Saved workspace {workspace.workspace_id} to database")
            
        except Exception as e:
            logger.error(f"Failed to save workspace to database: {e}")

    async def cleanup_workspace(self, workspace_id: str):
        """Clean up empty workspace"""
        try:
            if workspace_id in self.active_workspaces:
                del self.active_workspaces[workspace_id]
            
            if workspace_id in self.ai_sessions:
                del self.ai_sessions[workspace_id]
            
            if workspace_id in self.event_history:
                del self.event_history[workspace_id]
            
            logger.info(f"Cleaned up workspace {workspace_id}")
            
        except Exception as e:
            logger.error(f"Error cleaning up workspace: {e}")

# Global collaboration hub instance
collaboration_hub = None

async def initialize_collaboration_hub():
    """Initialize the global collaboration hub"""
    global collaboration_hub
    collaboration_hub = CollaborationHub()
    await collaboration_hub.initialize()
    return collaboration_hub

def get_collaboration_hub():
    """Get the global collaboration hub instance"""
    return collaboration_hub
