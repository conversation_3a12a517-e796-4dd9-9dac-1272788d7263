"""
Database models and operations for MK13 MVP
Handles Supabase integration and CRUD operations
"""

from supabase import create_client, Client
from typing import Optional, List, Dict, Any
import os
from datetime import datetime
import json
import logging
from cryptography.fernet import Fernet
import base64

logger = logging.getLogger(__name__)

class DatabaseManager:
    """Manages database connections and operations"""
    
    def __init__(self):
        self.supabase_url = os.getenv("SUPABASE_URL")
        self.supabase_key = os.getenv("SUPABASE_KEY")
        self.supabase_service_key = os.getenv("SUPABASE_SERVICE_KEY")
        self.encryption_key = os.getenv("ENCRYPTION_KEY")
        
        if not all([self.supabase_url, self.supabase_key]):
            raise ValueError("Missing required Supabase configuration")
        
        self.client: Client = create_client(self.supabase_url, self.supabase_key)
        self.service_client: Client = create_client(self.supabase_url, self.supabase_service_key) if self.supabase_service_key else None
        
        # Initialize encryption
        if self.encryption_key:
            self.cipher = Fernet(self.encryption_key.encode())
        else:
            logger.warning("No encryption key provided - sensitive data will not be encrypted")
            self.cipher = None
    
    def encrypt_data(self, data: str) -> str:
        """Encrypt sensitive data"""
        if not self.cipher:
            return data
        return self.cipher.encrypt(data.encode()).decode()
    
    def decrypt_data(self, encrypted_data: str) -> str:
        """Decrypt sensitive data"""
        if not self.cipher:
            return encrypted_data
        try:
            return self.cipher.decrypt(encrypted_data.encode()).decode()
        except Exception as e:
            logger.error(f"Failed to decrypt data: {e}")
            return encrypted_data

class UserManager:
    """Manages user operations"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
    
    async def create_user(self, email: str, name: str, google_token: Optional[str] = None) -> Dict[str, Any]:
        """Create a new user"""
        try:
            # Encrypt Google token if provided
            encrypted_token = None
            if google_token:
                encrypted_token = self.db.encrypt_data(google_token)
            
            user_data = {
                "email": email,
                "name": name,
                "google_token": encrypted_token,
                "created_at": datetime.utcnow().isoformat()
            }
            
            result = self.db.client.table("users").insert(user_data).execute()
            
            if result.data:
                logger.info(f"User created successfully: {email}")
                return result.data[0]
            else:
                raise Exception("Failed to create user")
                
        except Exception as e:
            logger.error(f"Error creating user: {e}")
            raise
    
    async def get_user_by_email(self, email: str) -> Optional[Dict[str, Any]]:
        """Get user by email"""
        try:
            result = self.db.client.table("users").select("*").eq("email", email).execute()
            
            if result.data:
                user = result.data[0]
                # Decrypt Google token if present
                if user.get("google_token"):
                    user["google_token"] = self.db.decrypt_data(user["google_token"])
                return user
            return None
            
        except Exception as e:
            logger.error(f"Error getting user by email: {e}")
            raise
    
    async def update_user(self, user_id: str, updates: Dict[str, Any]) -> Dict[str, Any]:
        """Update user information"""
        try:
            # Encrypt Google token if being updated
            if "google_token" in updates and updates["google_token"]:
                updates["google_token"] = self.db.encrypt_data(updates["google_token"])
            
            updates["updated_at"] = datetime.utcnow().isoformat()
            
            result = self.db.client.table("users").update(updates).eq("id", user_id).execute()
            
            if result.data:
                logger.info(f"User updated successfully: {user_id}")
                return result.data[0]
            else:
                raise Exception("Failed to update user")
                
        except Exception as e:
            logger.error(f"Error updating user: {e}")
            raise

class ContextManager:
    """Manages context operations"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
    
    async def create_context(self, user_id: str, name: str, state: Dict[str, Any], metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Create a new context"""
        try:
            context_data = {
                "user_id": user_id,
                "name": name,
                "state": json.dumps(state),
                "metadata": json.dumps(metadata) if metadata else None,
                "created_at": datetime.utcnow().isoformat()
            }
            
            result = self.db.client.table("contexts").insert(context_data).execute()
            
            if result.data:
                logger.info(f"Context created successfully: {name}")
                return result.data[0]
            else:
                raise Exception("Failed to create context")
                
        except Exception as e:
            logger.error(f"Error creating context: {e}")
            raise
    
    async def get_user_contexts(self, user_id: str) -> List[Dict[str, Any]]:
        """Get all contexts for a user"""
        try:
            result = self.db.client.table("contexts").select("*").eq("user_id", user_id).order("created_at", desc=True).execute()
            
            contexts = []
            for context in result.data:
                # Parse JSON fields
                context["state"] = json.loads(context["state"]) if context["state"] else {}
                context["metadata"] = json.loads(context["metadata"]) if context["metadata"] else {}
                contexts.append(context)
            
            return contexts
            
        except Exception as e:
            logger.error(f"Error getting user contexts: {e}")
            raise
    
    async def update_context(self, context_id: str, updates: Dict[str, Any]) -> Dict[str, Any]:
        """Update context"""
        try:
            # Convert dict fields to JSON strings
            if "state" in updates:
                updates["state"] = json.dumps(updates["state"])
            if "metadata" in updates:
                updates["metadata"] = json.dumps(updates["metadata"])
            
            updates["updated_at"] = datetime.utcnow().isoformat()
            
            result = self.db.client.table("contexts").update(updates).eq("id", context_id).execute()
            
            if result.data:
                logger.info(f"Context updated successfully: {context_id}")
                return result.data[0]
            else:
                raise Exception("Failed to update context")
                
        except Exception as e:
            logger.error(f"Error updating context: {e}")
            raise

class FeedbackManager:
    """Manages feedback operations"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
    
    async def create_feedback(self, user_id: str, action_id: str, feedback_type: str, content: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Create feedback entry"""
        try:
            feedback_data = {
                "user_id": user_id,
                "action_id": action_id,
                "feedback_type": feedback_type,
                "content": json.dumps(content) if content else None,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            result = self.db.client.table("feedback").insert(feedback_data).execute()
            
            if result.data:
                logger.info(f"Feedback created successfully: {feedback_type}")
                return result.data[0]
            else:
                raise Exception("Failed to create feedback")
                
        except Exception as e:
            logger.error(f"Error creating feedback: {e}")
            raise
    
    async def get_user_feedback(self, user_id: str, limit: int = 100) -> List[Dict[str, Any]]:
        """Get feedback for a user"""
        try:
            result = self.db.client.table("feedback").select("*").eq("user_id", user_id).order("timestamp", desc=True).limit(limit).execute()
            
            feedback_list = []
            for feedback in result.data:
                # Parse JSON content
                feedback["content"] = json.loads(feedback["content"]) if feedback["content"] else {}
                feedback_list.append(feedback)
            
            return feedback_list
            
        except Exception as e:
            logger.error(f"Error getting user feedback: {e}")
            raise

class WorkflowManager:
    """Manages workflow operations"""

    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager

    async def create_workflow(self, workflow_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new workflow"""
        try:
            # Prepare workflow data for insertion
            workflow_insert = {
                "name": workflow_data["name"],
                "description": workflow_data.get("description", ""),
                "user_id": workflow_data["user_id"],
                "trigger": json.dumps(workflow_data["trigger"]),
                "nodes": json.dumps(workflow_data["nodes"]),
                "enabled": workflow_data.get("enabled", True),
                "tags": workflow_data.get("tags", []),
                "created_at": datetime.utcnow().isoformat(),
                "updated_at": datetime.utcnow().isoformat()
            }

            result = self.db.client.table("workflows").insert(workflow_insert).execute()

            if result.data:
                workflow = result.data[0]
                # Parse JSON fields back
                workflow["trigger"] = json.loads(workflow["trigger"])
                workflow["nodes"] = json.loads(workflow["nodes"])
                logger.info(f"Workflow created successfully: {workflow['id']}")
                return workflow
            else:
                raise Exception("Failed to create workflow")

        except Exception as e:
            logger.error(f"Error creating workflow: {e}")
            raise

    async def get_workflow(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """Get a specific workflow"""
        try:
            result = self.db.client.table("workflows").select("*").eq("id", workflow_id).execute()

            if result.data:
                workflow = result.data[0]
                # Parse JSON fields
                workflow["trigger"] = json.loads(workflow["trigger"])
                workflow["nodes"] = json.loads(workflow["nodes"])
                return workflow

            return None

        except Exception as e:
            logger.error(f"Error getting workflow: {e}")
            raise

    async def get_user_workflows(self, user_id: str) -> List[Dict[str, Any]]:
        """Get all workflows for a user"""
        try:
            result = self.db.client.table("workflows").select("*").eq("user_id", user_id).order("updated_at", desc=True).execute()

            workflows = []
            for workflow in result.data:
                # Parse JSON fields
                workflow["trigger"] = json.loads(workflow["trigger"])
                workflow["nodes"] = json.loads(workflow["nodes"])
                workflows.append(workflow)

            return workflows

        except Exception as e:
            logger.error(f"Error getting user workflows: {e}")
            raise

    async def get_active_workflows(self) -> List[Dict[str, Any]]:
        """Get all active workflows"""
        try:
            result = self.db.client.table("workflows").select("*").eq("enabled", True).order("updated_at", desc=True).execute()

            workflows = []
            for workflow in result.data:
                # Parse JSON fields
                workflow["trigger"] = json.loads(workflow["trigger"])
                workflow["nodes"] = json.loads(workflow["nodes"])
                workflows.append(workflow)

            return workflows

        except Exception as e:
            logger.error(f"Error getting active workflows: {e}")
            raise

    async def update_workflow(self, workflow_id: str, updates: Dict[str, Any]) -> Dict[str, Any]:
        """Update a workflow"""
        try:
            # Prepare updates
            workflow_updates = {}

            for key, value in updates.items():
                if key in ["trigger", "nodes"]:
                    workflow_updates[key] = json.dumps(value)
                elif key in ["name", "description", "enabled", "last_run", "run_count", "success_rate", "tags"]:
                    workflow_updates[key] = value

            workflow_updates["updated_at"] = datetime.utcnow().isoformat()

            result = self.db.client.table("workflows").update(workflow_updates).eq("id", workflow_id).execute()

            if result.data:
                workflow = result.data[0]
                # Parse JSON fields back
                if "trigger" in workflow:
                    workflow["trigger"] = json.loads(workflow["trigger"])
                if "nodes" in workflow:
                    workflow["nodes"] = json.loads(workflow["nodes"])
                logger.info(f"Workflow updated successfully: {workflow_id}")
                return workflow
            else:
                raise Exception("Failed to update workflow")

        except Exception as e:
            logger.error(f"Error updating workflow: {e}")
            raise

    async def delete_workflow(self, workflow_id: str):
        """Delete a workflow"""
        try:
            result = self.db.client.table("workflows").delete().eq("id", workflow_id).execute()
            logger.info(f"Workflow deleted successfully: {workflow_id}")

        except Exception as e:
            logger.error(f"Error deleting workflow: {e}")
            raise

    async def create_workflow_execution(self, execution_data: Dict[str, Any]) -> str:
        """Create a workflow execution record"""
        try:
            execution_insert = {
                "workflow_id": execution_data["workflow_id"],
                "user_id": execution_data["user_id"],
                "trigger_data": json.dumps(execution_data.get("trigger_data", {})),
                "status": execution_data.get("status", "running"),
                "started_at": datetime.utcnow().isoformat()
            }

            result = self.db.client.table("workflow_executions").insert(execution_insert).execute()

            if result.data:
                execution_id = result.data[0]["id"]
                logger.info(f"Workflow execution created: {execution_id}")
                return execution_id
            else:
                raise Exception("Failed to create workflow execution")

        except Exception as e:
            logger.error(f"Error creating workflow execution: {e}")
            raise

    async def update_workflow_execution(self, execution_id: str, updates: Dict[str, Any]):
        """Update a workflow execution"""
        try:
            execution_updates = {}

            for key, value in updates.items():
                if key == "results":
                    execution_updates[key] = json.dumps(value)
                elif key in ["status", "completed_at", "error_message", "execution_time_ms"]:
                    execution_updates[key] = value

            result = self.db.client.table("workflow_executions").update(execution_updates).eq("id", execution_id).execute()
            logger.info(f"Workflow execution updated: {execution_id}")

        except Exception as e:
            logger.error(f"Error updating workflow execution: {e}")
            raise

# Global database manager instance
db_manager = None
user_manager = None
context_manager = None
feedback_manager = None
workflow_manager = None

def initialize_database():
    """Initialize database managers"""
    global db_manager, user_manager, context_manager, feedback_manager, workflow_manager

    db_manager = DatabaseManager()
    user_manager = UserManager(db_manager)
    context_manager = ContextManager(db_manager)
    feedback_manager = FeedbackManager(db_manager)
    workflow_manager = WorkflowManager(db_manager)

    logger.info("Database managers initialized successfully")

def get_managers():
    """Get database manager instances"""
    if not db_manager:
        initialize_database()

    return {
        "db": db_manager,
        "users": user_manager,
        "contexts": context_manager,
        "feedback": feedback_manager,
        "workflows": workflow_manager
    }
