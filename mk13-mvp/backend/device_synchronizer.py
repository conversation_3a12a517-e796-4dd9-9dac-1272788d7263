"""
Device Synchronizer for Enhanced Context Intelligence
Cross-device context synchronization and continuity
"""

import asyncio
import json
import logging
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
import hashlib
import platform
import psutil
from database import get_managers

logger = logging.getLogger(__name__)

@dataclass
class DeviceContext:
    device_id: str
    device_type: str  # desktop, mobile, tablet
    device_name: str
    platform: str  # windows, macos, linux, ios, android
    location: Optional[Dict[str, float]]
    network_info: Dict[str, Any]
    battery_level: Optional[float]
    is_charging: Optional[bool]
    active_applications: List[Dict[str, Any]]
    last_sync: datetime
    sync_status: str
    user_id: str
    context_state: Dict[str, Any]

@dataclass
class ContextContinuity:
    continuity_id: str
    user_id: str
    source_device: str
    target_device: str
    context_type: str
    context_data: Dict[str, Any]
    transfer_timestamp: datetime
    completion_status: str
    similarity_score: float

class DeviceSynchronizer:
    """Synchronize context across multiple devices for seamless experience"""
    
    def __init__(self):
        self.device_contexts: Dict[str, DeviceContext] = {}
        self.sync_queue: List[Dict[str, Any]] = []
        self.continuity_sessions: Dict[str, ContextContinuity] = {}
        self.device_fingerprint = None
        self.sync_interval = 30  # seconds
        self.is_syncing = False
        
        # Device type detection patterns
        self.device_patterns = {
            'mobile': ['iphone', 'android', 'mobile'],
            'tablet': ['ipad', 'tablet'],
            'desktop': ['windows', 'macos', 'linux', 'desktop'],
            'laptop': ['macbook', 'laptop', 'notebook']
        }
        
    async def initialize(self):
        """Initialize the device synchronizer"""
        try:
            # Generate device fingerprint
            self.device_fingerprint = await self.generate_device_fingerprint()
            
            # Start synchronization loop
            asyncio.create_task(self.sync_loop())
            
            logger.info(f"Device Synchronizer initialized for device: {self.device_fingerprint}")
        except Exception as e:
            logger.error(f"Failed to initialize Device Synchronizer: {e}")
            raise

    async def generate_device_fingerprint(self) -> str:
        """Generate unique device fingerprint"""
        try:
            # Collect device information
            system_info = {
                'platform': platform.system(),
                'machine': platform.machine(),
                'processor': platform.processor(),
                'hostname': platform.node()
            }
            
            # Add network interface info
            try:
                import netifaces
                interfaces = netifaces.interfaces()
                system_info['interfaces'] = interfaces[:3]  # First 3 interfaces
            except ImportError:
                system_info['interfaces'] = []
            
            # Create fingerprint hash
            fingerprint_data = json.dumps(system_info, sort_keys=True)
            fingerprint = hashlib.sha256(fingerprint_data.encode()).hexdigest()[:16]
            
            return fingerprint
            
        except Exception as e:
            logger.error(f"Failed to generate device fingerprint: {e}")
            return str(uuid.uuid4())[:16]

    async def sync_loop(self):
        """Main synchronization loop"""
        while True:
            try:
                if not self.is_syncing:
                    await self.perform_sync()
                
                await asyncio.sleep(self.sync_interval)
                
            except Exception as e:
                logger.error(f"Error in sync loop: {e}")
                await asyncio.sleep(60)  # Longer sleep on error

    async def perform_sync(self):
        """Perform device context synchronization"""
        try:
            self.is_syncing = True
            
            # Update local device context
            await self.update_local_device_context()
            
            # Sync with remote devices
            await self.sync_with_remote_devices()
            
            # Process continuity requests
            await self.process_continuity_queue()
            
            # Clean up old data
            await self.cleanup_old_data()
            
        except Exception as e:
            logger.error(f"Failed to perform sync: {e}")
        finally:
            self.is_syncing = False

    async def update_local_device_context(self):
        """Update context for the current device"""
        try:
            # Get current device information
            device_info = await self.get_current_device_info()
            
            # Get active applications (simplified for cross-platform)
            active_apps = await self.get_active_applications()
            
            # Get network information
            network_info = await self.get_network_info()
            
            # Get battery information (if available)
            battery_info = await self.get_battery_info()
            
            # Create device context
            device_context = DeviceContext(
                device_id=self.device_fingerprint,
                device_type=device_info['type'],
                device_name=device_info['name'],
                platform=device_info['platform'],
                location=await self.get_location_info(),
                network_info=network_info,
                battery_level=battery_info.get('level'),
                is_charging=battery_info.get('charging'),
                active_applications=active_apps,
                last_sync=datetime.utcnow(),
                sync_status='active',
                user_id='',  # Will be set when user context is available
                context_state=await self.get_context_state()
            )
            
            # Store locally
            self.device_contexts[self.device_fingerprint] = device_context
            
        except Exception as e:
            logger.error(f"Failed to update local device context: {e}")

    async def get_current_device_info(self) -> Dict[str, Any]:
        """Get current device information"""
        try:
            system = platform.system().lower()
            machine = platform.machine().lower()
            hostname = platform.node()
            
            # Determine device type
            device_type = 'desktop'  # Default
            for dev_type, patterns in self.device_patterns.items():
                for pattern in patterns:
                    if pattern in system or pattern in machine or pattern in hostname.lower():
                        device_type = dev_type
                        break
            
            return {
                'type': device_type,
                'name': hostname,
                'platform': system,
                'architecture': machine,
                'version': platform.version()
            }
            
        except Exception as e:
            logger.error(f"Failed to get device info: {e}")
            return {'type': 'unknown', 'name': 'unknown', 'platform': 'unknown'}

    async def get_active_applications(self) -> List[Dict[str, Any]]:
        """Get active applications (simplified cross-platform)"""
        try:
            apps = []
            
            # Get top processes by CPU usage
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent']):
                try:
                    proc_info = proc.info
                    if proc_info['cpu_percent'] and proc_info['cpu_percent'] > 1.0:
                        apps.append({
                            'name': proc_info['name'],
                            'pid': proc_info['pid'],
                            'cpu_usage': proc_info['cpu_percent']
                        })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            # Sort by CPU usage and return top 10
            apps.sort(key=lambda x: x['cpu_usage'], reverse=True)
            return apps[:10]
            
        except Exception as e:
            logger.error(f"Failed to get active applications: {e}")
            return []

    async def get_network_info(self) -> Dict[str, Any]:
        """Get network information"""
        try:
            network_info = {
                'connected': True,
                'connection_type': 'unknown',
                'ip_address': None,
                'ssid': None
            }
            
            # Get network interfaces
            net_io = psutil.net_io_counters()
            if net_io:
                network_info['bytes_sent'] = net_io.bytes_sent
                network_info['bytes_recv'] = net_io.bytes_recv
            
            # Try to get IP address
            try:
                import socket
                hostname = socket.gethostname()
                ip_address = socket.gethostbyname(hostname)
                network_info['ip_address'] = ip_address
            except Exception:
                pass
            
            return network_info
            
        except Exception as e:
            logger.error(f"Failed to get network info: {e}")
            return {'connected': False}

    async def get_battery_info(self) -> Dict[str, Any]:
        """Get battery information (if available)"""
        try:
            battery = psutil.sensors_battery()
            if battery:
                return {
                    'level': battery.percent,
                    'charging': battery.power_plugged,
                    'time_left': battery.secsleft if battery.secsleft != psutil.POWER_TIME_UNLIMITED else None
                }
            else:
                return {}
                
        except Exception:
            return {}

    async def get_location_info(self) -> Optional[Dict[str, float]]:
        """Get location information (placeholder for future implementation)"""
        # This would integrate with location services
        # For now, return None
        return None

    async def get_context_state(self) -> Dict[str, Any]:
        """Get current context state"""
        try:
            # This would integrate with the main context system
            # For now, return basic state
            return {
                'timestamp': datetime.utcnow().isoformat(),
                'active': True,
                'context_type': 'unknown'
            }
            
        except Exception as e:
            logger.error(f"Failed to get context state: {e}")
            return {}

    async def sync_with_remote_devices(self):
        """Sync context with other devices"""
        try:
            # Get user's other devices from database
            managers = get_managers()
            
            # This would query for other devices belonging to the same user
            # For now, we'll implement the database structure
            
            # Store current device context
            await self.store_device_context()
            
            # Retrieve other device contexts
            other_devices = await self.get_other_device_contexts()
            
            # Update local cache
            for device in other_devices:
                self.device_contexts[device.device_id] = device
                
        except Exception as e:
            logger.error(f"Failed to sync with remote devices: {e}")

    async def store_device_context(self):
        """Store device context in database"""
        try:
            if self.device_fingerprint not in self.device_contexts:
                return
            
            device_context = self.device_contexts[self.device_fingerprint]
            managers = get_managers()
            
            # Store in database (would need to extend database schema)
            context_data = {
                'device_id': device_context.device_id,
                'device_type': device_context.device_type,
                'device_name': device_context.device_name,
                'platform': device_context.platform,
                'context_data': json.dumps(asdict(device_context)),
                'last_sync': device_context.last_sync.isoformat(),
                'sync_status': device_context.sync_status
            }
            
            # This would use a device_contexts table
            logger.info(f"Stored device context for {device_context.device_id}")
            
        except Exception as e:
            logger.error(f"Failed to store device context: {e}")

    async def get_other_device_contexts(self) -> List[DeviceContext]:
        """Get context from other user devices"""
        try:
            # This would query the database for other devices
            # For now, return empty list
            return []
            
        except Exception as e:
            logger.error(f"Failed to get other device contexts: {e}")
            return []

    async def get_device_contexts(self, user_id: str) -> List[DeviceContext]:
        """Get all device contexts for a user"""
        try:
            # Update user_id for current device
            if self.device_fingerprint in self.device_contexts:
                self.device_contexts[self.device_fingerprint].user_id = user_id
            
            # Return all known device contexts
            user_devices = [
                device for device in self.device_contexts.values()
                if device.user_id == user_id or device.device_id == self.device_fingerprint
            ]
            
            return user_devices
            
        except Exception as e:
            logger.error(f"Failed to get device contexts for user {user_id}: {e}")
            return []

    async def analyze_context_continuity(self, user_id: str, device_contexts: List[DeviceContext]) -> Dict[str, Any]:
        """Analyze context continuity across devices"""
        try:
            continuity_analysis = {
                'active_devices': len(device_contexts),
                'primary_device': None,
                'context_switches': 0,
                'sync_health': 'good',
                'recommendations': []
            }
            
            if not device_contexts:
                return continuity_analysis
            
            # Find primary device (most recently active)
            primary_device = max(device_contexts, key=lambda d: d.last_sync)
            continuity_analysis['primary_device'] = primary_device.device_id
            
            # Analyze sync health
            current_time = datetime.utcnow()
            stale_devices = [
                d for d in device_contexts
                if (current_time - d.last_sync).total_seconds() > 300  # 5 minutes
            ]
            
            if stale_devices:
                continuity_analysis['sync_health'] = 'degraded'
                continuity_analysis['recommendations'].append(
                    f"{len(stale_devices)} devices need sync update"
                )
            
            # Detect context switches
            recent_switches = await self.detect_recent_context_switches(user_id)
            continuity_analysis['context_switches'] = len(recent_switches)
            
            return continuity_analysis
            
        except Exception as e:
            logger.error(f"Failed to analyze context continuity: {e}")
            return {'active_devices': 0, 'sync_health': 'error'}

    async def detect_recent_context_switches(self, user_id: str) -> List[Dict[str, Any]]:
        """Detect recent context switches between devices"""
        try:
            # This would analyze the continuity sessions
            # For now, return empty list
            return []
            
        except Exception as e:
            logger.error(f"Failed to detect context switches: {e}")
            return []

    async def request_context_continuity(
        self, 
        user_id: str, 
        source_device: str, 
        target_device: str, 
        context_data: Dict[str, Any]
    ) -> str:
        """Request context continuity between devices"""
        try:
            continuity_id = str(uuid.uuid4())
            
            continuity = ContextContinuity(
                continuity_id=continuity_id,
                user_id=user_id,
                source_device=source_device,
                target_device=target_device,
                context_type=context_data.get('type', 'unknown'),
                context_data=context_data,
                transfer_timestamp=datetime.utcnow(),
                completion_status='pending',
                similarity_score=0.0
            )
            
            self.continuity_sessions[continuity_id] = continuity
            
            # Add to sync queue
            self.sync_queue.append({
                'type': 'continuity_request',
                'continuity_id': continuity_id,
                'timestamp': datetime.utcnow()
            })
            
            logger.info(f"Context continuity requested: {continuity_id}")
            return continuity_id
            
        except Exception as e:
            logger.error(f"Failed to request context continuity: {e}")
            raise

    async def process_continuity_queue(self):
        """Process pending continuity requests"""
        try:
            processed_items = []
            
            for item in self.sync_queue:
                if item['type'] == 'continuity_request':
                    continuity_id = item['continuity_id']
                    
                    if continuity_id in self.continuity_sessions:
                        await self.process_continuity_request(continuity_id)
                        processed_items.append(item)
            
            # Remove processed items
            for item in processed_items:
                self.sync_queue.remove(item)
                
        except Exception as e:
            logger.error(f"Failed to process continuity queue: {e}")

    async def process_continuity_request(self, continuity_id: str):
        """Process a specific continuity request"""
        try:
            continuity = self.continuity_sessions[continuity_id]
            
            # Check if target device is available
            target_device = self.device_contexts.get(continuity.target_device)
            
            if target_device:
                # Calculate similarity score
                similarity_score = await self.calculate_context_similarity(
                    continuity.context_data,
                    target_device.context_state
                )
                
                continuity.similarity_score = similarity_score
                continuity.completion_status = 'completed'
                
                logger.info(f"Context continuity completed: {continuity_id}")
            else:
                continuity.completion_status = 'failed'
                logger.warning(f"Target device not available: {continuity.target_device}")
                
        except Exception as e:
            logger.error(f"Failed to process continuity request {continuity_id}: {e}")

    async def calculate_context_similarity(
        self, 
        source_context: Dict[str, Any], 
        target_context: Dict[str, Any]
    ) -> float:
        """Calculate similarity between contexts"""
        try:
            # Simple similarity calculation
            # In practice, this would be more sophisticated
            
            similarity_factors = []
            
            # Time similarity
            if 'timestamp' in source_context and 'timestamp' in target_context:
                source_time = datetime.fromisoformat(source_context['timestamp'])
                target_time = datetime.fromisoformat(target_context['timestamp'])
                time_diff = abs((source_time - target_time).total_seconds())
                time_similarity = max(0, 1 - time_diff / 3600)  # 1 hour max
                similarity_factors.append(time_similarity)
            
            # Context type similarity
            if source_context.get('context_type') == target_context.get('context_type'):
                similarity_factors.append(1.0)
            else:
                similarity_factors.append(0.5)
            
            return sum(similarity_factors) / len(similarity_factors) if similarity_factors else 0.0
            
        except Exception as e:
            logger.error(f"Failed to calculate context similarity: {e}")
            return 0.0

    async def cleanup_old_data(self):
        """Clean up old device contexts and continuity sessions"""
        try:
            current_time = datetime.utcnow()
            cutoff_time = current_time - timedelta(hours=24)
            
            # Remove old device contexts
            old_devices = [
                device_id for device_id, device in self.device_contexts.items()
                if device.last_sync < cutoff_time and device_id != self.device_fingerprint
            ]
            
            for device_id in old_devices:
                del self.device_contexts[device_id]
            
            # Remove old continuity sessions
            old_sessions = [
                session_id for session_id, session in self.continuity_sessions.items()
                if session.transfer_timestamp < cutoff_time
            ]
            
            for session_id in old_sessions:
                del self.continuity_sessions[session_id]
                
            if old_devices or old_sessions:
                logger.info(f"Cleaned up {len(old_devices)} old devices and {len(old_sessions)} old sessions")
                
        except Exception as e:
            logger.error(f"Failed to cleanup old data: {e}")
