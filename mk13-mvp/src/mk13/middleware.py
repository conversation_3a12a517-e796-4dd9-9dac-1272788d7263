"""
Production Middleware for MK13 AI Assistant
Security headers, rate limiting, request logging, and metrics collection
"""

import time
import uuid
from typing import Callable, Dict, Any, Optional
from datetime import datetime, timezone

import structlog
from fastapi import Request, Response, HTTPException
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp
from prometheus_client import Counter, Histogram, Gauge

from mk13.config import get_settings
from mk13.logging import set_correlation_id, set_request_id, set_user_id, clear_context

logger = structlog.get_logger(__name__)

# Prometheus metrics
REQUEST_COUNT = Counter(
    "mk13_http_requests_total",
    "Total HTTP requests",
    ["method", "endpoint", "status_code"]
)

REQUEST_DURATION = Histogram(
    "mk13_http_request_duration_seconds",
    "HTTP request duration in seconds",
    ["method", "endpoint"]
)

ACTIVE_REQUESTS = Gauge(
    "mk13_http_requests_active",
    "Number of active HTTP requests"
)

RATE_LIMIT_EXCEEDED = Counter(
    "mk13_rate_limit_exceeded_total",
    "Total rate limit exceeded events",
    ["client_ip", "endpoint"]
)


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """Middleware for comprehensive request/response logging with correlation IDs."""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.settings = get_settings()
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request with logging and correlation ID."""
        # Generate correlation and request IDs
        correlation_id = str(uuid.uuid4())
        request_id = str(uuid.uuid4())
        
        # Set context variables
        set_correlation_id(correlation_id)
        set_request_id(request_id)
        
        # Extract user ID from request if available
        user_id = None
        if hasattr(request.state, "user") and request.state.user:
            user_id = getattr(request.state.user, "id", None)
            if user_id:
                set_user_id(str(user_id))
        
        # Add headers to request
        request.state.correlation_id = correlation_id
        request.state.request_id = request_id
        
        start_time = time.time()
        
        # Log request
        logger.info(
            "Request started",
            method=request.method,
            url=str(request.url),
            client_ip=request.client.host if request.client else None,
            user_agent=request.headers.get("user-agent"),
            content_length=request.headers.get("content-length"),
        )
        
        try:
            # Process request
            response = await call_next(request)
            
            # Calculate duration
            duration = time.time() - start_time
            
            # Log response
            logger.info(
                "Request completed",
                status_code=response.status_code,
                duration_ms=round(duration * 1000, 2),
                response_size=response.headers.get("content-length"),
            )
            
            # Add correlation headers to response
            response.headers["X-Correlation-ID"] = correlation_id
            response.headers["X-Request-ID"] = request_id
            
            return response
            
        except Exception as exc:
            duration = time.time() - start_time
            
            logger.error(
                "Request failed",
                error=str(exc),
                error_type=type(exc).__name__,
                duration_ms=round(duration * 1000, 2),
                exc_info=True,
            )
            
            # Return error response with correlation ID
            error_response = JSONResponse(
                status_code=500,
                content={
                    "error": "Internal server error",
                    "correlation_id": correlation_id,
                    "request_id": request_id,
                }
            )
            error_response.headers["X-Correlation-ID"] = correlation_id
            error_response.headers["X-Request-ID"] = request_id
            
            return error_response
            
        finally:
            # Clear context
            clear_context()


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """Middleware for adding security headers to responses."""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.settings = get_settings()
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Add security headers to response."""
        response = await call_next(request)
        
        # Security headers
        security_headers = {
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            "Referrer-Policy": "strict-origin-when-cross-origin",
            "Permissions-Policy": "geolocation=(), microphone=(), camera=()",
        }
        
        # Add HSTS header for HTTPS
        if request.url.scheme == "https" or self.settings.is_production:
            security_headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        
        # Content Security Policy
        if self.settings.is_production:
            csp_directives = [
                "default-src 'self'",
                "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
                "style-src 'self' 'unsafe-inline'",
                "img-src 'self' data: https:",
                "font-src 'self' https:",
                "connect-src 'self' wss: https:",
                "media-src 'self'",
                "object-src 'none'",
                "base-uri 'self'",
                "form-action 'self'",
                "frame-ancestors 'none'",
            ]
            security_headers["Content-Security-Policy"] = "; ".join(csp_directives)
        
        # Add headers to response
        for header, value in security_headers.items():
            response.headers[header] = value
        
        return response


class RateLimitMiddleware(BaseHTTPMiddleware):
    """Simple in-memory rate limiting middleware."""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.settings = get_settings()
        self.request_counts: Dict[str, Dict[str, Any]] = {}
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Apply rate limiting based on client IP."""
        if not self.settings.rate_limit_enabled:
            return await call_next(request)
        
        client_ip = request.client.host if request.client else "unknown"
        current_time = datetime.now(timezone.utc)
        
        # Clean old entries
        self._cleanup_old_entries(current_time)
        
        # Check rate limit
        if self._is_rate_limited(client_ip, current_time):
            RATE_LIMIT_EXCEEDED.labels(
                client_ip=client_ip,
                endpoint=request.url.path
            ).inc()
            
            logger.warning(
                "Rate limit exceeded",
                client_ip=client_ip,
                endpoint=request.url.path,
                method=request.method,
            )
            
            return JSONResponse(
                status_code=429,
                content={
                    "error": "Rate limit exceeded",
                    "message": f"Too many requests. Limit: {self.settings.rate_limit_requests} per {self.settings.rate_limit_window} seconds",
                    "retry_after": self.settings.rate_limit_window,
                },
                headers={"Retry-After": str(self.settings.rate_limit_window)}
            )
        
        # Record request
        self._record_request(client_ip, current_time)
        
        return await call_next(request)
    
    def _is_rate_limited(self, client_ip: str, current_time: datetime) -> bool:
        """Check if client IP is rate limited."""
        if client_ip not in self.request_counts:
            return False
        
        client_data = self.request_counts[client_ip]
        window_start = current_time.timestamp() - self.settings.rate_limit_window
        
        # Count requests in current window
        recent_requests = [
            req_time for req_time in client_data["requests"]
            if req_time > window_start
        ]
        
        return len(recent_requests) >= self.settings.rate_limit_requests
    
    def _record_request(self, client_ip: str, current_time: datetime) -> None:
        """Record a request for the client IP."""
        if client_ip not in self.request_counts:
            self.request_counts[client_ip] = {"requests": []}
        
        self.request_counts[client_ip]["requests"].append(current_time.timestamp())
    
    def _cleanup_old_entries(self, current_time: datetime) -> None:
        """Clean up old request entries."""
        cutoff_time = current_time.timestamp() - (self.settings.rate_limit_window * 2)
        
        for client_ip in list(self.request_counts.keys()):
            client_data = self.request_counts[client_ip]
            client_data["requests"] = [
                req_time for req_time in client_data["requests"]
                if req_time > cutoff_time
            ]
            
            # Remove empty entries
            if not client_data["requests"]:
                del self.request_counts[client_ip]


class MetricsMiddleware(BaseHTTPMiddleware):
    """Middleware for collecting Prometheus metrics."""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.settings = get_settings()
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Collect metrics for the request."""
        if not self.settings.enable_metrics:
            return await call_next(request)
        
        method = request.method
        endpoint = request.url.path
        
        # Increment active requests
        ACTIVE_REQUESTS.inc()
        
        start_time = time.time()
        
        try:
            response = await call_next(request)
            status_code = str(response.status_code)
            
            # Record metrics
            REQUEST_COUNT.labels(
                method=method,
                endpoint=endpoint,
                status_code=status_code
            ).inc()
            
            REQUEST_DURATION.labels(
                method=method,
                endpoint=endpoint
            ).observe(time.time() - start_time)
            
            return response
            
        except Exception as exc:
            # Record error metrics
            REQUEST_COUNT.labels(
                method=method,
                endpoint=endpoint,
                status_code="500"
            ).inc()
            
            REQUEST_DURATION.labels(
                method=method,
                endpoint=endpoint
            ).observe(time.time() - start_time)
            
            raise exc
            
        finally:
            # Decrement active requests
            ACTIVE_REQUESTS.dec()
