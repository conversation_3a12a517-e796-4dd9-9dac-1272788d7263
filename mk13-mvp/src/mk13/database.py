"""
Production Database Management for MK13 AI Assistant
Enterprise-grade SQLAlchemy with connection pooling, monitoring, and migrations
"""

import asyncio
import time
from contextlib import asynccontextmanager
from datetime import datetime, timezone
from typing import Any, AsyncGenerator, Dict, List, Optional

import structlog
from sqlalchemy import create_engine, event, text
from sqlalchemy.ext.asyncio import (
    AsyncEngine,
    AsyncSession,
    async_sessionmaker,
    create_async_engine,
)
from sqlalchemy.orm import declarative_base, sessionmaker
from sqlalchemy.pool import QueuePool

from mk13.config import get_settings
from mk13.monitoring import DB_CONNECTIONS_ACTIVE, DB_CONNECTIONS_TOTAL, DB_QUERY_DURATION

logger = structlog.get_logger(__name__)

# SQLAlchemy base
Base = declarative_base()

# Global database instances
async_engine: Optional[AsyncEngine] = None
async_session_factory: Optional[async_sessionmaker[AsyncSession]] = None
sync_engine = None
sync_session_factory = None


class DatabaseManager:
    """Production database manager with connection pooling and monitoring."""
    
    def __init__(self):
        self.settings = get_settings()
        self.async_engine: Optional[AsyncEngine] = None
        self.async_session_factory: Optional[async_sessionmaker[AsyncSession]] = None
        self.sync_engine = None
        self.sync_session_factory = None
        
    async def initialize(self) -> None:
        """Initialize database connections and setup monitoring."""
        try:
            # Create async engine with connection pooling
            self.async_engine = create_async_engine(
                str(self.settings.database_url).replace("postgresql://", "postgresql+asyncpg://"),
                poolclass=QueuePool,
                pool_size=self.settings.database_pool_size,
                max_overflow=self.settings.database_max_overflow,
                pool_timeout=self.settings.database_pool_timeout,
                pool_recycle=self.settings.database_pool_recycle,
                pool_pre_ping=True,
                echo=self.settings.debug,
                future=True,
            )
            
            # Create sync engine for migrations and admin tasks
            self.sync_engine = create_engine(
                str(self.settings.database_url),
                poolclass=QueuePool,
                pool_size=10,
                max_overflow=20,
                pool_timeout=30,
                pool_recycle=3600,
                pool_pre_ping=True,
                echo=self.settings.debug,
                future=True,
            )
            
            # Create session factories
            self.async_session_factory = async_sessionmaker(
                bind=self.async_engine,
                class_=AsyncSession,
                expire_on_commit=False,
                autoflush=True,
                autocommit=False,
            )
            
            self.sync_session_factory = sessionmaker(
                bind=self.sync_engine,
                expire_on_commit=False,
                autoflush=True,
                autocommit=False,
            )
            
            # Setup connection monitoring
            self._setup_connection_monitoring()
            
            # Test connection
            await self._test_connection()
            
            logger.info(
                "Database initialized successfully",
                pool_size=self.settings.database_pool_size,
                max_overflow=self.settings.database_max_overflow,
                database_url=str(self.settings.database_url).split("@")[-1],  # Hide credentials
            )
            
        except Exception as e:
            logger.error("Failed to initialize database", error=str(e), exc_info=True)
            raise
    
    def _setup_connection_monitoring(self) -> None:
        """Setup SQLAlchemy event listeners for monitoring."""
        if not self.async_engine:
            return
        
        @event.listens_for(self.async_engine.sync_engine, "connect")
        def on_connect(dbapi_connection, connection_record):
            """Track new connections."""
            DB_CONNECTIONS_TOTAL.inc()
            DB_CONNECTIONS_ACTIVE.inc()
        
        @event.listens_for(self.async_engine.sync_engine, "close")
        def on_close(dbapi_connection, connection_record):
            """Track closed connections."""
            DB_CONNECTIONS_ACTIVE.dec()
        
        @event.listens_for(self.async_engine.sync_engine, "before_cursor_execute")
        def before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
            """Track query start time."""
            context._query_start_time = time.time()
        
        @event.listens_for(self.async_engine.sync_engine, "after_cursor_execute")
        def after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
            """Track query duration."""
            if hasattr(context, "_query_start_time"):
                duration = time.time() - context._query_start_time
                DB_QUERY_DURATION.observe(duration)
    
    async def _test_connection(self) -> None:
        """Test database connection."""
        if not self.async_engine:
            raise RuntimeError("Database engine not initialized")
        
        async with self.async_engine.begin() as conn:
            result = await conn.execute(text("SELECT 1"))
            assert result.scalar() == 1
        
        logger.info("Database connection test successful")
    
    @asynccontextmanager
    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """Get async database session with automatic cleanup."""
        if not self.async_session_factory:
            raise RuntimeError("Database not initialized")
        
        async with self.async_session_factory() as session:
            try:
                yield session
                await session.commit()
            except Exception:
                await session.rollback()
                raise
            finally:
                await session.close()
    
    async def execute_query(self, query: str, parameters: Optional[Dict[str, Any]] = None) -> Any:
        """Execute a raw SQL query."""
        async with self.get_session() as session:
            result = await session.execute(text(query), parameters or {})
            return result
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform database health check."""
        try:
            start_time = time.time()
            
            async with self.get_session() as session:
                result = await session.execute(text("SELECT 1 as health_check"))
                health_value = result.scalar()
            
            duration = time.time() - start_time
            
            if health_value == 1:
                return {
                    "status": "healthy",
                    "response_time_ms": round(duration * 1000, 2),
                    "details": "Database connection successful"
                }
            else:
                return {
                    "status": "unhealthy",
                    "details": "Unexpected health check result"
                }
                
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "details": "Database health check failed"
            }
    
    async def get_connection_stats(self) -> Dict[str, Any]:
        """Get database connection pool statistics."""
        if not self.async_engine:
            return {"error": "Database not initialized"}
        
        pool = self.async_engine.pool
        
        return {
            "pool_size": pool.size(),
            "checked_in": pool.checkedin(),
            "checked_out": pool.checkedout(),
            "overflow": pool.overflow(),
            "invalid": pool.invalid(),
        }
    
    async def close(self) -> None:
        """Close database connections."""
        if self.async_engine:
            await self.async_engine.dispose()
            logger.info("Async database engine disposed")
        
        if self.sync_engine:
            self.sync_engine.dispose()
            logger.info("Sync database engine disposed")


# Global database manager instance
db_manager: Optional[DatabaseManager] = None


async def initialize_database() -> DatabaseManager:
    """Initialize the global database manager."""
    global db_manager
    
    if db_manager is None:
        db_manager = DatabaseManager()
        await db_manager.initialize()
    
    return db_manager


def get_database_manager() -> DatabaseManager:
    """Get the global database manager instance."""
    if db_manager is None:
        raise RuntimeError("Database not initialized. Call initialize_database() first.")
    
    return db_manager


@asynccontextmanager
async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """Get database session (convenience function)."""
    manager = get_database_manager()
    async with manager.get_session() as session:
        yield session


# Legacy compatibility functions for existing code
class UserManager:
    """User management operations."""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
    
    async def create_user(self, email: str, name: str, google_token: Optional[str] = None) -> Dict[str, Any]:
        """Create a new user."""
        async with self.db_manager.get_session() as session:
            # This would use actual SQLAlchemy models
            user_data = {
                "id": f"user_{int(time.time())}",
                "email": email,
                "name": name,
                "google_token": google_token,
                "created_at": datetime.now(timezone.utc).isoformat(),
                "updated_at": datetime.now(timezone.utc).isoformat(),
            }
            
            logger.info("User created", user_id=user_data["id"], email=email)
            return user_data
    
    async def get_user_by_email(self, email: str) -> Optional[Dict[str, Any]]:
        """Get user by email."""
        async with self.db_manager.get_session() as session:
            # This would use actual SQLAlchemy queries
            logger.info("User lookup", email=email)
            return None  # Placeholder
    
    async def update_user(self, user_id: str, updates: Dict[str, Any]) -> Dict[str, Any]:
        """Update user information."""
        async with self.db_manager.get_session() as session:
            # This would use actual SQLAlchemy updates
            logger.info("User updated", user_id=user_id, updates=list(updates.keys()))
            return {"id": user_id, **updates}


class ContextManager:
    """Context management operations."""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
    
    async def create_context(self, user_id: str, name: str, state: Dict[str, Any], metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Create a new context."""
        async with self.db_manager.get_session() as session:
            context_data = {
                "id": f"context_{int(time.time())}",
                "user_id": user_id,
                "name": name,
                "state": state,
                "metadata": metadata or {},
                "created_at": datetime.now(timezone.utc).isoformat(),
                "updated_at": datetime.now(timezone.utc).isoformat(),
            }
            
            logger.info("Context created", context_id=context_data["id"], user_id=user_id)
            return context_data
    
    async def get_user_contexts(self, user_id: str) -> List[Dict[str, Any]]:
        """Get all contexts for a user."""
        async with self.db_manager.get_session() as session:
            logger.info("Contexts retrieved", user_id=user_id)
            return []  # Placeholder


class FeedbackManager:
    """Feedback management operations."""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
    
    async def create_feedback(self, user_id: str, action_id: str, feedback_type: str, content: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Create feedback entry."""
        async with self.db_manager.get_session() as session:
            feedback_data = {
                "id": f"feedback_{int(time.time())}",
                "user_id": user_id,
                "action_id": action_id,
                "feedback_type": feedback_type,
                "content": content or {},
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }
            
            logger.info("Feedback created", feedback_id=feedback_data["id"], user_id=user_id, type=feedback_type)
            return feedback_data


def get_managers() -> Dict[str, Any]:
    """Get all database managers (legacy compatibility)."""
    manager = get_database_manager()
    
    return {
        "users": UserManager(manager),
        "contexts": ContextManager(manager),
        "feedback": FeedbackManager(manager),
    }
