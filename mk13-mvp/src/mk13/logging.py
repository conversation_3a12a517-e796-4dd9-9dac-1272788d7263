"""
Production Logging Configuration for MK13 AI Assistant
Structured logging with correlation IDs and comprehensive error tracking
"""

import logging
import logging.config
import sys
import uuid
from contextvars import ContextVar
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, Optional

import structlog
from structlog.types import EventDict, Processor

from mk13.config import Log<PERSON><PERSON>l, Settings, get_settings

# Context variables for request correlation
correlation_id: ContextVar[Optional[str]] = ContextVar("correlation_id", default=None)
user_id: ContextVar[Optional[str]] = ContextVar("user_id", default=None)
request_id: ContextVar[Optional[str]] = ContextVar("request_id", default=None)


def add_correlation_id(logger: Any, method_name: str, event_dict: EventDict) -> EventDict:
    """Add correlation ID to log events."""
    if correlation_id.get():
        event_dict["correlation_id"] = correlation_id.get()
    if user_id.get():
        event_dict["user_id"] = user_id.get()
    if request_id.get():
        event_dict["request_id"] = request_id.get()
    return event_dict


def add_timestamp(logger: Any, method_name: str, event_dict: EventDict) -> EventDict:
    """Add ISO timestamp to log events."""
    event_dict["timestamp"] = datetime.now(timezone.utc).isoformat()
    return event_dict


def add_log_level(logger: Any, method_name: str, event_dict: EventDict) -> EventDict:
    """Add log level to event dict."""
    event_dict["level"] = method_name.upper()
    return event_dict


def add_logger_name(logger: Any, method_name: str, event_dict: EventDict) -> EventDict:
    """Add logger name to event dict."""
    event_dict["logger"] = logger.name
    return event_dict


def filter_sensitive_data(logger: Any, method_name: str, event_dict: EventDict) -> EventDict:
    """Filter sensitive data from logs."""
    sensitive_keys = {
        "password", "token", "secret", "key", "authorization", 
        "api_key", "access_token", "refresh_token", "session_id"
    }
    
    def _filter_dict(data: Dict[str, Any]) -> Dict[str, Any]:
        """Recursively filter sensitive data from dictionary."""
        filtered = {}
        for key, value in data.items():
            if any(sensitive in key.lower() for sensitive in sensitive_keys):
                filtered[key] = "***REDACTED***"
            elif isinstance(value, dict):
                filtered[key] = _filter_dict(value)
            elif isinstance(value, list):
                filtered[key] = [
                    _filter_dict(item) if isinstance(item, dict) else item
                    for item in value
                ]
            else:
                filtered[key] = value
        return filtered
    
    # Filter the event dict itself
    for key in list(event_dict.keys()):
        if any(sensitive in key.lower() for sensitive in sensitive_keys):
            event_dict[key] = "***REDACTED***"
        elif isinstance(event_dict[key], dict):
            event_dict[key] = _filter_dict(event_dict[key])
    
    return event_dict


class CorrelationIDProcessor:
    """Processor to add correlation IDs to all log messages."""
    
    def __call__(self, logger: Any, method_name: str, event_dict: EventDict) -> EventDict:
        """Add correlation context to log events."""
        return add_correlation_id(logger, method_name, event_dict)


class TimestampProcessor:
    """Processor to add timestamps to log messages."""
    
    def __call__(self, logger: Any, method_name: str, event_dict: EventDict) -> EventDict:
        """Add timestamp to log events."""
        return add_timestamp(logger, method_name, event_dict)


def setup_logging(settings: Optional[Settings] = None) -> None:
    """
    Setup structured logging for the application.
    
    Args:
        settings: Application settings. If None, will get from get_settings()
    """
    if settings is None:
        settings = get_settings()
    
    # Configure structlog processors
    processors: list[Processor] = [
        structlog.contextvars.merge_contextvars,
        add_correlation_id,
        add_timestamp,
        add_log_level,
        add_logger_name,
        filter_sensitive_data,
        structlog.processors.add_logger_name,
        structlog.processors.add_log_level,
        structlog.processors.StackInfoRenderer(),
        structlog.dev.set_exc_info,
    ]
    
    # Add appropriate renderer based on environment
    if settings.is_development:
        processors.append(structlog.dev.ConsoleRenderer(colors=True))
    else:
        processors.append(structlog.processors.JSONRenderer())
    
    # Configure structlog
    structlog.configure(
        processors=processors,
        wrapper_class=structlog.make_filtering_bound_logger(
            getattr(logging, settings.log_level.value)
        ),
        logger_factory=structlog.WriteLoggerFactory(),
        cache_logger_on_first_use=True,
    )
    
    # Configure standard logging
    log_config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "standard": {
                "format": settings.log_format,
                "datefmt": "%Y-%m-%d %H:%M:%S",
            },
            "json": {
                "()": "pythonjsonlogger.jsonlogger.JsonFormatter",
                "format": "%(asctime)s %(name)s %(levelname)s %(message)s",
            },
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "level": settings.log_level.value,
                "formatter": "json" if settings.is_production else "standard",
                "stream": sys.stdout,
            },
        },
        "loggers": {
            "mk13": {
                "level": settings.log_level.value,
                "handlers": ["console"],
                "propagate": False,
            },
            "uvicorn": {
                "level": "INFO",
                "handlers": ["console"],
                "propagate": False,
            },
            "uvicorn.access": {
                "level": "INFO",
                "handlers": ["console"],
                "propagate": False,
            },
            "fastapi": {
                "level": "INFO",
                "handlers": ["console"],
                "propagate": False,
            },
            "sqlalchemy": {
                "level": "WARNING",
                "handlers": ["console"],
                "propagate": False,
            },
            "celery": {
                "level": "INFO",
                "handlers": ["console"],
                "propagate": False,
            },
        },
        "root": {
            "level": settings.log_level.value,
            "handlers": ["console"],
        },
    }
    
    # Add file handler if log file is specified
    if settings.log_file:
        log_file_path = Path(settings.log_file)
        log_file_path.parent.mkdir(parents=True, exist_ok=True)
        
        log_config["handlers"]["file"] = {
            "class": "logging.handlers.RotatingFileHandler",
            "level": settings.log_level.value,
            "formatter": "json",
            "filename": str(log_file_path),
            "maxBytes": 10 * 1024 * 1024,  # 10MB
            "backupCount": 5,
        }
        
        # Add file handler to all loggers
        for logger_config in log_config["loggers"].values():
            logger_config["handlers"].append("file")
        log_config["root"]["handlers"].append("file")
    
    # Apply logging configuration
    logging.config.dictConfig(log_config)
    
    # Set up third-party library log levels
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("asyncio").setLevel(logging.WARNING)
    
    # Create application logger
    logger = structlog.get_logger("mk13.startup")
    logger.info(
        "Logging configured",
        log_level=settings.log_level.value,
        environment=settings.environment.value,
        log_file=str(settings.log_file) if settings.log_file else None,
    )


def get_logger(name: str) -> structlog.BoundLogger:
    """
    Get a structured logger instance.
    
    Args:
        name: Logger name
        
    Returns:
        Configured structlog logger
    """
    return structlog.get_logger(name)


def set_correlation_id(correlation_id_value: str) -> None:
    """Set correlation ID for current context."""
    correlation_id.set(correlation_id_value)


def set_user_id(user_id_value: str) -> None:
    """Set user ID for current context."""
    user_id.set(user_id_value)


def set_request_id(request_id_value: str) -> None:
    """Set request ID for current context."""
    request_id.set(request_id_value)


def generate_correlation_id() -> str:
    """Generate a new correlation ID."""
    return str(uuid.uuid4())


def clear_context() -> None:
    """Clear all context variables."""
    correlation_id.set(None)
    user_id.set(None)
    request_id.set(None)
