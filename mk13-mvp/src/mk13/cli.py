"""
MK13 AI Assistant CLI
Command-line interface for production deployment and management
"""

import asyncio
import sys
from pathlib import Path
from typing import Optional

import click
import structlog
import uvicorn

from mk13.config import Environment, get_settings, reload_settings
from mk13.database import initialize_database
from mk13.logging import setup_logging

logger = structlog.get_logger(__name__)


@click.group()
@click.option("--config", "-c", help="Configuration file path")
@click.option("--env", "-e", type=click.Choice([e.value for e in Environment]), help="Environment")
@click.option("--debug", is_flag=True, help="Enable debug mode")
def cli(config: Optional[str], env: Optional[str], debug: bool):
    """MK13 AI Assistant CLI."""
    # Set environment variables if provided
    if env:
        import os
        os.environ["MK13_ENVIRONMENT"] = env
    
    if debug:
        import os
        os.environ["MK13_DEBUG"] = "true"
    
    # Reload settings with new environment
    settings = reload_settings()
    setup_logging(settings)
    
    logger.info("MK13 CLI initialized", environment=settings.environment.value, debug=settings.debug)


@cli.command()
@click.option("--host", default="0.0.0.0", help="Host to bind to")
@click.option("--port", default=8000, type=int, help="Port to bind to")
@click.option("--workers", default=1, type=int, help="Number of worker processes")
@click.option("--reload", is_flag=True, help="Enable auto-reload")
def server(host: str, port: int, workers: int, reload: bool):
    """Start the MK13 server."""
    settings = get_settings()
    
    logger.info(
        "Starting MK13 server",
        host=host,
        port=port,
        workers=workers,
        reload=reload,
        environment=settings.environment.value,
    )
    
    # Override settings with CLI options
    settings.host = host
    settings.port = port
    settings.workers = workers
    settings.reload = reload
    
    try:
        uvicorn.run(
            "mk13.main:app",
            host=host,
            port=port,
            workers=1 if reload else workers,
            reload=reload,
            log_config=None,  # Use our custom logging
            access_log=False,  # Handled by our middleware
        )
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error("Server error", error=str(e), exc_info=True)
        sys.exit(1)


@cli.command()
def worker():
    """Start a Celery worker."""
    settings = get_settings()
    
    logger.info("Starting Celery worker", environment=settings.environment.value)
    
    try:
        # This would start the actual Celery worker
        # For now, just a placeholder
        logger.info("Celery worker would start here")
        
    except KeyboardInterrupt:
        logger.info("Worker stopped by user")
    except Exception as e:
        logger.error("Worker error", error=str(e), exc_info=True)
        sys.exit(1)


@cli.command()
@click.option("--check", is_flag=True, help="Check database connection only")
def database(check: bool):
    """Database management commands."""
    
    async def run_database_command():
        settings = get_settings()
        
        if check:
            logger.info("Checking database connection")
            try:
                db_manager = await initialize_database()
                health = await db_manager.health_check()
                
                if health["status"] == "healthy":
                    logger.info("Database connection successful", **health)
                    click.echo("✅ Database connection successful")
                else:
                    logger.error("Database connection failed", **health)
                    click.echo("❌ Database connection failed")
                    sys.exit(1)
                    
            except Exception as e:
                logger.error("Database check failed", error=str(e), exc_info=True)
                click.echo(f"❌ Database check failed: {e}")
                sys.exit(1)
        else:
            logger.info("Database management - no action specified")
            click.echo("Use --check to test database connection")
    
    asyncio.run(run_database_command())


@cli.command()
@click.option("--format", "output_format", default="table", type=click.Choice(["table", "json"]), help="Output format")
def health(output_format: str):
    """Check application health."""
    
    async def run_health_check():
        try:
            # Initialize database for health check
            db_manager = await initialize_database()
            health_result = await db_manager.health_check()
            
            if output_format == "json":
                import json
                click.echo(json.dumps(health_result, indent=2))
            else:
                status = health_result["status"]
                emoji = "✅" if status == "healthy" else "❌"
                click.echo(f"{emoji} Database: {status}")
                
                if "response_time_ms" in health_result:
                    click.echo(f"   Response time: {health_result['response_time_ms']}ms")
                
                if "error" in health_result:
                    click.echo(f"   Error: {health_result['error']}")
            
            # Exit with error code if unhealthy
            if health_result["status"] != "healthy":
                sys.exit(1)
                
        except Exception as e:
            logger.error("Health check failed", error=str(e), exc_info=True)
            click.echo(f"❌ Health check failed: {e}")
            sys.exit(1)
    
    asyncio.run(run_health_check())


@cli.command()
def config():
    """Show current configuration."""
    settings = get_settings()
    
    click.echo("MK13 Configuration:")
    click.echo(f"  Environment: {settings.environment.value}")
    click.echo(f"  Debug: {settings.debug}")
    click.echo(f"  Host: {settings.host}")
    click.echo(f"  Port: {settings.port}")
    click.echo(f"  Workers: {settings.workers}")
    click.echo(f"  Database URL: {str(settings.database_url).split('@')[-1]}")  # Hide credentials
    click.echo(f"  Redis URL: {str(settings.redis_url).split('@')[-1]}")  # Hide credentials
    click.echo(f"  Log Level: {settings.log_level.value}")
    
    # Feature flags
    click.echo("  Features:")
    click.echo(f"    Collaboration: {settings.enable_collaboration}")
    click.echo(f"    Workflows: {settings.enable_workflows}")
    click.echo(f"    Context Intelligence: {settings.enable_context_intelligence}")
    click.echo(f"    AI Assistant: {settings.enable_ai_assistant}")
    click.echo(f"    Multi-tenancy: {settings.enable_multi_tenancy}")


@cli.command()
@click.option("--output", "-o", help="Output file path")
def export_config(output: Optional[str]):
    """Export current configuration to file."""
    settings = get_settings()
    
    config_data = {
        "environment": settings.environment.value,
        "debug": settings.debug,
        "host": settings.host,
        "port": settings.port,
        "workers": settings.workers,
        "log_level": settings.log_level.value,
        "features": {
            "collaboration": settings.enable_collaboration,
            "workflows": settings.enable_workflows,
            "context_intelligence": settings.enable_context_intelligence,
            "ai_assistant": settings.enable_ai_assistant,
            "multi_tenancy": settings.enable_multi_tenancy,
        },
        "limits": {
            "upload_max_size": settings.upload_max_size,
            "rate_limit_requests": settings.rate_limit_requests,
            "rate_limit_window": settings.rate_limit_window,
        },
    }
    
    import json
    config_json = json.dumps(config_data, indent=2)
    
    if output:
        output_path = Path(output)
        output_path.write_text(config_json)
        click.echo(f"Configuration exported to {output_path}")
    else:
        click.echo(config_json)


@cli.command()
@click.option("--host", default="0.0.0.0", help="Metrics server host")
@click.option("--port", default=9090, type=int, help="Metrics server port")
def metrics(host: str, port: int):
    """Start standalone metrics server."""
    from prometheus_client import start_http_server
    
    logger.info("Starting metrics server", host=host, port=port)
    
    try:
        start_http_server(port, addr=host)
        click.echo(f"Metrics server running on http://{host}:{port}/metrics")
        
        # Keep running until interrupted
        while True:
            import time
            time.sleep(1)
            
    except KeyboardInterrupt:
        logger.info("Metrics server stopped by user")
    except Exception as e:
        logger.error("Metrics server error", error=str(e), exc_info=True)
        sys.exit(1)


@cli.command()
def version():
    """Show version information."""
    settings = get_settings()
    
    click.echo(f"MK13 AI Assistant v{settings.app_version}")
    click.echo(f"Environment: {settings.environment.value}")
    
    # Show Python version
    import sys
    click.echo(f"Python: {sys.version}")
    
    # Show key dependencies
    try:
        import fastapi
        click.echo(f"FastAPI: {fastapi.__version__}")
    except ImportError:
        pass
    
    try:
        import sqlalchemy
        click.echo(f"SQLAlchemy: {sqlalchemy.__version__}")
    except ImportError:
        pass


def main():
    """Main CLI entry point."""
    cli()


if __name__ == "__main__":
    main()
