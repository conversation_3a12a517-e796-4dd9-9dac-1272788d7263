"""
MK13 Enterprise AI Assistant - Production Main Application
High-performance, scalable FastAPI backend with comprehensive monitoring
"""

import asyncio
import uuid
from contextlib import asynccontextmanager
from datetime import datetime, timezone
from typing import Any, Dict

import structlog
from fastapi import BackgroundTasks, Depends, FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse

from mk13.config import get_settings
from mk13.database import get_database_manager, get_managers, initialize_database
from mk13.logging import get_logger, set_correlation_id, setup_logging
from mk13.middleware import (
    MetricsMiddleware,
    RateLimitMiddleware,
    RequestLoggingMiddleware,
    SecurityHeadersMiddleware,
)
from mk13.monitoring import setup_monitoring

# Initialize settings and logging
settings = get_settings()
setup_logging(settings)
logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager for startup and shutdown."""
    # Startup
    logger.info("Starting MK13 AI Assistant", version=settings.app_version, environment=settings.environment.value)
    
    try:
        # Initialize database
        await initialize_database()
        logger.info("Database initialized successfully")
        
        # Initialize other services (placeholder for now)
        # await initialize_ai_service()
        # await initialize_collaboration_hub()
        # await initialize_workflow_engine()
        
        logger.info("All services initialized successfully")
        
    except Exception as e:
        logger.error("Failed to initialize services", error=str(e), exc_info=True)
        raise
    
    yield
    
    # Shutdown
    logger.info("Shutting down MK13 AI Assistant")
    
    try:
        # Close database connections
        db_manager = get_database_manager()
        await db_manager.close()
        logger.info("Database connections closed")
        
    except Exception as e:
        logger.error("Error during shutdown", error=str(e), exc_info=True)


# Create FastAPI application
app = FastAPI(
    title=settings.app_name,
    description="Enterprise-grade Personal AI Assistant with Real-time Collaboration",
    version=settings.app_version,
    docs_url="/docs" if not settings.is_production else None,
    redoc_url="/redoc" if not settings.is_production else None,
    openapi_url="/openapi.json" if not settings.is_production else None,
    lifespan=lifespan,
)

# Add middleware in correct order (last added = first executed)
app.add_middleware(MetricsMiddleware)
app.add_middleware(SecurityHeadersMiddleware)
app.add_middleware(RateLimitMiddleware)
app.add_middleware(RequestLoggingMiddleware)

# CORS middleware
if settings.cors_origins:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=[str(origin) for origin in settings.cors_origins],
        allow_credentials=settings.cors_allow_credentials,
        allow_methods=settings.cors_allow_methods,
        allow_headers=settings.cors_allow_headers,
    )

# Trusted host middleware for production
if settings.is_production:
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["*"]  # Configure with actual allowed hosts
    )

# Setup monitoring endpoints
setup_monitoring(app)


# Exception handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """Handle HTTP exceptions with proper logging."""
    correlation_id = getattr(request.state, "correlation_id", str(uuid.uuid4()))
    
    logger.warning(
        "HTTP exception",
        status_code=exc.status_code,
        detail=exc.detail,
        path=request.url.path,
        method=request.method,
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": exc.detail,
            "status_code": exc.status_code,
            "correlation_id": correlation_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        },
        headers={"X-Correlation-ID": correlation_id}
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """Handle general exceptions with proper logging."""
    correlation_id = getattr(request.state, "correlation_id", str(uuid.uuid4()))
    
    logger.error(
        "Unhandled exception",
        error=str(exc),
        error_type=type(exc).__name__,
        path=request.url.path,
        method=request.method,
        exc_info=True,
    )
    
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "status_code": 500,
            "correlation_id": correlation_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        },
        headers={"X-Correlation-ID": correlation_id}
    )


# Root endpoints
@app.get("/")
async def root():
    """Root endpoint with application information."""
    return {
        "name": settings.app_name,
        "version": settings.app_version,
        "environment": settings.environment.value,
        "status": "healthy",
        "timestamp": datetime.now(timezone.utc).isoformat(),
    }


@app.get("/info")
async def app_info():
    """Application information endpoint."""
    return {
        "name": settings.app_name,
        "version": settings.app_version,
        "environment": settings.environment.value,
        "features": {
            "collaboration": settings.enable_collaboration,
            "workflows": settings.enable_workflows,
            "context_intelligence": settings.enable_context_intelligence,
            "ai_assistant": settings.enable_ai_assistant,
            "multi_tenancy": settings.enable_multi_tenancy,
        },
        "limits": {
            "upload_max_size": settings.upload_max_size,
            "rate_limit_requests": settings.rate_limit_requests,
            "rate_limit_window": settings.rate_limit_window,
        },
    }


# User Management Endpoints
@app.post("/api/users")
async def create_user(user_data: Dict[str, Any]):
    """Create a new user."""
    try:
        managers = get_managers()
        user = await managers["users"].create_user(
            email=user_data["email"],
            name=user_data["name"],
            google_token=user_data.get("google_token")
        )
        
        logger.info("User created", user_id=user["id"], email=user["email"])
        return {"success": True, "user": user}
        
    except Exception as e:
        logger.error("Error creating user", error=str(e), exc_info=True)
        raise HTTPException(status_code=400, detail=str(e))


@app.get("/api/users/{email}")
async def get_user(email: str):
    """Get user by email."""
    try:
        managers = get_managers()
        user = await managers["users"].get_user_by_email(email)
        
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        return {"success": True, "user": user}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error getting user", email=email, error=str(e), exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


# Context Management Endpoints
@app.post("/api/contexts")
async def create_context(context_data: Dict[str, Any]):
    """Create a new context."""
    try:
        managers = get_managers()
        context = await managers["contexts"].create_context(
            user_id=context_data["user_id"],
            name=context_data["name"],
            state=context_data["state"],
            metadata=context_data.get("metadata")
        )
        
        logger.info("Context created", context_id=context["id"], user_id=context["user_id"])
        return {"success": True, "context": context}
        
    except Exception as e:
        logger.error("Error creating context", error=str(e), exc_info=True)
        raise HTTPException(status_code=400, detail=str(e))


@app.get("/api/users/{user_id}/contexts")
async def get_user_contexts(user_id: str):
    """Get all contexts for a user."""
    try:
        managers = get_managers()
        contexts = await managers["contexts"].get_user_contexts(user_id)
        
        return {"success": True, "contexts": contexts}
        
    except Exception as e:
        logger.error("Error getting user contexts", user_id=user_id, error=str(e), exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


# Feedback Endpoints
@app.post("/api/feedback")
async def create_feedback(feedback_data: Dict[str, Any]):
    """Create feedback entry."""
    try:
        managers = get_managers()
        feedback = await managers["feedback"].create_feedback(
            user_id=feedback_data["user_id"],
            action_id=feedback_data["action_id"],
            feedback_type=feedback_data["feedback_type"],
            content=feedback_data.get("content")
        )
        
        logger.info("Feedback created", feedback_id=feedback["id"], user_id=feedback["user_id"])
        return {"success": True, "feedback": feedback}
        
    except Exception as e:
        logger.error("Error creating feedback", error=str(e), exc_info=True)
        raise HTTPException(status_code=400, detail=str(e))


# Background task example
@app.post("/api/tasks/example")
async def create_background_task(background_tasks: BackgroundTasks, task_data: Dict[str, Any]):
    """Create a background task."""
    
    def process_task(data: Dict[str, Any]):
        """Process background task."""
        logger.info("Processing background task", data=data)
        # Task processing logic here
    
    background_tasks.add_task(process_task, task_data)
    
    return {
        "success": True,
        "message": "Background task created",
        "task_id": str(uuid.uuid4())
    }


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "mk13.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.reload,
        workers=1 if settings.reload else settings.workers,
        log_config=None,  # Use our custom logging
        access_log=False,  # Handled by our middleware
    )
