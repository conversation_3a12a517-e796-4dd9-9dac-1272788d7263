"""
Production Configuration Management for MK13 AI Assistant
Enterprise-grade configuration with environment-specific settings
"""

import os
import secrets
from datetime import timezone
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

from pydantic import (
    BaseSettings,
    Field,
    PostgresDsn,
    RedisDsn,
    validator,
    root_validator,
)
from pydantic.networks import AnyHttpUrl


class Environment(str, Enum):
    """Application environment types."""
    
    DEVELOPMENT = "development"
    TESTING = "testing"
    STAGING = "staging"
    PRODUCTION = "production"


class LogLevel(str, Enum):
    """Logging levels."""
    
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class Settings(BaseSettings):
    """
    Application settings with environment-specific configuration.
    
    All settings can be overridden via environment variables.
    Environment variables should be prefixed with 'MK13_'.
    """
    
    # Application
    app_name: str = Field(default="MK13 AI Assistant", description="Application name")
    app_version: str = Field(default="1.0.0", description="Application version")
    environment: Environment = Field(default=Environment.DEVELOPMENT, description="Application environment")
    debug: bool = Field(default=False, description="Debug mode")
    
    # Server
    host: str = Field(default="0.0.0.0", description="Server host")
    port: int = Field(default=8000, description="Server port")
    workers: int = Field(default=1, description="Number of worker processes")
    reload: bool = Field(default=False, description="Auto-reload on code changes")
    
    # Security
    secret_key: str = Field(default_factory=lambda: secrets.token_urlsafe(32), description="Secret key for JWT")
    access_token_expire_minutes: int = Field(default=30, description="Access token expiration in minutes")
    refresh_token_expire_days: int = Field(default=7, description="Refresh token expiration in days")
    password_min_length: int = Field(default=8, description="Minimum password length")
    
    # CORS
    cors_origins: List[AnyHttpUrl] = Field(default=[], description="Allowed CORS origins")
    cors_allow_credentials: bool = Field(default=True, description="Allow CORS credentials")
    cors_allow_methods: List[str] = Field(default=["*"], description="Allowed CORS methods")
    cors_allow_headers: List[str] = Field(default=["*"], description="Allowed CORS headers")
    
    # Database
    database_url: PostgresDsn = Field(
        default="postgresql://mk13:mk13@localhost:5432/mk13",
        description="PostgreSQL database URL"
    )
    database_pool_size: int = Field(default=20, description="Database connection pool size")
    database_max_overflow: int = Field(default=30, description="Database max overflow connections")
    database_pool_timeout: int = Field(default=30, description="Database pool timeout in seconds")
    database_pool_recycle: int = Field(default=3600, description="Database pool recycle time in seconds")
    
    # Redis
    redis_url: RedisDsn = Field(
        default="redis://localhost:6379/0",
        description="Redis URL for caching and sessions"
    )
    redis_pool_size: int = Field(default=20, description="Redis connection pool size")
    redis_socket_timeout: int = Field(default=5, description="Redis socket timeout in seconds")
    redis_socket_connect_timeout: int = Field(default=5, description="Redis connection timeout in seconds")
    
    # Celery
    celery_broker_url: str = Field(
        default="redis://localhost:6379/1",
        description="Celery broker URL"
    )
    celery_result_backend: str = Field(
        default="redis://localhost:6379/2",
        description="Celery result backend URL"
    )
    celery_task_serializer: str = Field(default="json", description="Celery task serializer")
    celery_result_serializer: str = Field(default="json", description="Celery result serializer")
    celery_timezone: str = Field(default="UTC", description="Celery timezone")
    
    # AI Services
    openai_api_key: Optional[str] = Field(default=None, description="OpenAI API key")
    openai_organization: Optional[str] = Field(default=None, description="OpenAI organization ID")
    anthropic_api_key: Optional[str] = Field(default=None, description="Anthropic API key")
    google_ai_api_key: Optional[str] = Field(default=None, description="Google AI API key")
    
    # Google Workspace
    google_client_id: Optional[str] = Field(default=None, description="Google OAuth client ID")
    google_client_secret: Optional[str] = Field(default=None, description="Google OAuth client secret")
    google_redirect_uri: Optional[str] = Field(default=None, description="Google OAuth redirect URI")
    
    # WebRTC
    stun_servers: List[str] = Field(
        default=["stun:stun.l.google.com:19302", "stun:stun1.l.google.com:19302"],
        description="STUN servers for WebRTC"
    )
    turn_servers: List[Dict[str, Any]] = Field(default=[], description="TURN servers for WebRTC")
    
    # File Storage
    upload_max_size: int = Field(default=10 * 1024 * 1024, description="Max upload size in bytes (10MB)")
    upload_allowed_extensions: List[str] = Field(
        default=[".jpg", ".jpeg", ".png", ".gif", ".pdf", ".doc", ".docx", ".txt"],
        description="Allowed file extensions for uploads"
    )
    storage_path: Path = Field(default=Path("./storage"), description="Local storage path")
    
    # Logging
    log_level: LogLevel = Field(default=LogLevel.INFO, description="Logging level")
    log_format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="Log format"
    )
    log_file: Optional[Path] = Field(default=None, description="Log file path")
    log_rotation: str = Field(default="1 day", description="Log rotation interval")
    log_retention: str = Field(default="30 days", description="Log retention period")
    
    # Monitoring
    enable_metrics: bool = Field(default=True, description="Enable Prometheus metrics")
    metrics_port: int = Field(default=9090, description="Metrics server port")
    enable_tracing: bool = Field(default=False, description="Enable distributed tracing")
    jaeger_endpoint: Optional[str] = Field(default=None, description="Jaeger tracing endpoint")
    sentry_dsn: Optional[str] = Field(default=None, description="Sentry DSN for error tracking")
    
    # Rate Limiting
    rate_limit_enabled: bool = Field(default=True, description="Enable rate limiting")
    rate_limit_requests: int = Field(default=100, description="Rate limit requests per window")
    rate_limit_window: int = Field(default=60, description="Rate limit window in seconds")
    
    # Health Checks
    health_check_interval: int = Field(default=30, description="Health check interval in seconds")
    health_check_timeout: int = Field(default=10, description="Health check timeout in seconds")
    
    # Feature Flags
    enable_collaboration: bool = Field(default=True, description="Enable collaboration features")
    enable_workflows: bool = Field(default=True, description="Enable workflow automation")
    enable_context_intelligence: bool = Field(default=True, description="Enable context intelligence")
    enable_ai_assistant: bool = Field(default=True, description="Enable AI assistant")
    
    # Multi-tenancy
    enable_multi_tenancy: bool = Field(default=False, description="Enable multi-tenancy")
    default_tenant: str = Field(default="default", description="Default tenant ID")
    
    class Config:
        """Pydantic configuration."""
        
        env_prefix = "MK13_"
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        validate_assignment = True
        
    @validator("cors_origins", pre=True)
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        """Parse CORS origins from string or list."""
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)
    
    @validator("storage_path", pre=True)
    def validate_storage_path(cls, v: Union[str, Path]) -> Path:
        """Validate and create storage path."""
        path = Path(v)
        path.mkdir(parents=True, exist_ok=True)
        return path
    
    @root_validator
    def validate_environment_settings(cls, values: Dict[str, Any]) -> Dict[str, Any]:
        """Validate environment-specific settings."""
        environment = values.get("environment")
        
        if environment == Environment.PRODUCTION:
            # Production-specific validations
            if values.get("debug", False):
                raise ValueError("Debug mode must be disabled in production")
            
            if values.get("secret_key") == "dev-secret-key":
                raise ValueError("Secret key must be changed in production")
            
            if not values.get("sentry_dsn"):
                print("Warning: Sentry DSN not configured for production")
        
        elif environment == Environment.DEVELOPMENT:
            # Development-specific defaults
            values.setdefault("debug", True)
            values.setdefault("reload", True)
            values.setdefault("log_level", LogLevel.DEBUG)
        
        return values
    
    @property
    def is_production(self) -> bool:
        """Check if running in production environment."""
        return self.environment == Environment.PRODUCTION
    
    @property
    def is_development(self) -> bool:
        """Check if running in development environment."""
        return self.environment == Environment.DEVELOPMENT
    
    @property
    def is_testing(self) -> bool:
        """Check if running in testing environment."""
        return self.environment == Environment.TESTING


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get application settings."""
    return settings


def reload_settings() -> Settings:
    """Reload settings from environment."""
    global settings
    settings = Settings()
    return settings
