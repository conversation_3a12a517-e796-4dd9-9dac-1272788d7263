"""
Production Monitoring and Observability for MK13 AI Assistant
Prometheus metrics, health checks, and distributed tracing
"""

import asyncio
import time
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

import psutil
import structlog
from fastapi import FastAPI
from prometheus_client import (
    Counter,
    Gauge,
    Histogram,
    Info,
    generate_latest,
    CONTENT_TYPE_LATEST,
)
from starlette.responses import Response

from mk13.config import get_settings

logger = structlog.get_logger(__name__)

# Application metrics
APP_INFO = Info("mk13_app", "Application information")
APP_START_TIME = Gauge("mk13_app_start_time_seconds", "Application start time")
APP_UPTIME = Gauge("mk13_app_uptime_seconds", "Application uptime")

# System metrics
SYSTEM_CPU_USAGE = Gauge("mk13_system_cpu_usage_percent", "System CPU usage percentage")
SYSTEM_MEMORY_USAGE = Gauge("mk13_system_memory_usage_bytes", "System memory usage in bytes")
SYSTEM_MEMORY_TOTAL = Gauge("mk13_system_memory_total_bytes", "System total memory in bytes")
SYSTEM_DISK_USAGE = Gauge("mk13_system_disk_usage_bytes", "System disk usage in bytes")
SYSTEM_DISK_TOTAL = Gauge("mk13_system_disk_total_bytes", "System total disk space in bytes")

# Database metrics
DB_CONNECTIONS_ACTIVE = Gauge("mk13_db_connections_active", "Active database connections")
DB_CONNECTIONS_TOTAL = Gauge("mk13_db_connections_total", "Total database connections")
DB_QUERY_DURATION = Histogram("mk13_db_query_duration_seconds", "Database query duration")
DB_QUERY_COUNT = Counter("mk13_db_queries_total", "Total database queries", ["operation"])

# AI service metrics
AI_REQUESTS_TOTAL = Counter("mk13_ai_requests_total", "Total AI requests", ["model", "status"])
AI_REQUEST_DURATION = Histogram("mk13_ai_request_duration_seconds", "AI request duration", ["model"])
AI_TOKENS_USED = Counter("mk13_ai_tokens_used_total", "Total AI tokens used", ["model", "type"])

# Collaboration metrics
COLLABORATION_SESSIONS_ACTIVE = Gauge("mk13_collaboration_sessions_active", "Active collaboration sessions")
COLLABORATION_PARTICIPANTS = Gauge("mk13_collaboration_participants_total", "Total collaboration participants")
COLLABORATION_EVENTS = Counter("mk13_collaboration_events_total", "Total collaboration events", ["event_type"])

# Workflow metrics
WORKFLOW_EXECUTIONS_TOTAL = Counter("mk13_workflow_executions_total", "Total workflow executions", ["status"])
WORKFLOW_EXECUTION_DURATION = Histogram("mk13_workflow_execution_duration_seconds", "Workflow execution duration")
WORKFLOWS_ACTIVE = Gauge("mk13_workflows_active", "Active workflows")

# WebRTC metrics
WEBRTC_CONNECTIONS_ACTIVE = Gauge("mk13_webrtc_connections_active", "Active WebRTC connections")
WEBRTC_SIGNALING_EVENTS = Counter("mk13_webrtc_signaling_events_total", "WebRTC signaling events", ["event_type"])


class HealthChecker:
    """Health check manager for application dependencies."""
    
    def __init__(self):
        self.settings = get_settings()
        self.checks: Dict[str, Dict[str, Any]] = {}
        self.last_check_time: Optional[datetime] = None
    
    async def check_database(self) -> Dict[str, Any]:
        """Check database connectivity and performance."""
        try:
            from mk13.database import get_managers
            
            start_time = time.time()
            managers = get_managers()
            
            # Simple connectivity check
            # This would be replaced with actual database ping
            await asyncio.sleep(0.001)  # Simulate DB check
            
            duration = time.time() - start_time
            
            return {
                "status": "healthy",
                "response_time_ms": round(duration * 1000, 2),
                "details": "Database connection successful"
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "details": "Database connection failed"
            }
    
    async def check_redis(self) -> Dict[str, Any]:
        """Check Redis connectivity and performance."""
        try:
            # This would be replaced with actual Redis ping
            start_time = time.time()
            await asyncio.sleep(0.001)  # Simulate Redis check
            duration = time.time() - start_time
            
            return {
                "status": "healthy",
                "response_time_ms": round(duration * 1000, 2),
                "details": "Redis connection successful"
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "details": "Redis connection failed"
            }
    
    async def check_ai_services(self) -> Dict[str, Any]:
        """Check AI service availability."""
        try:
            # This would check actual AI service endpoints
            return {
                "status": "healthy",
                "details": "AI services available",
                "services": {
                    "openai": "available" if self.settings.openai_api_key else "not_configured",
                    "anthropic": "available" if self.settings.anthropic_api_key else "not_configured",
                    "google": "available" if self.settings.google_ai_api_key else "not_configured",
                }
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "details": "AI services check failed"
            }
    
    async def check_system_resources(self) -> Dict[str, Any]:
        """Check system resource usage."""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # Update metrics
            SYSTEM_CPU_USAGE.set(cpu_percent)
            SYSTEM_MEMORY_USAGE.set(memory.used)
            SYSTEM_MEMORY_TOTAL.set(memory.total)
            SYSTEM_DISK_USAGE.set(disk.used)
            SYSTEM_DISK_TOTAL.set(disk.total)
            
            # Determine health status
            status = "healthy"
            warnings = []
            
            if cpu_percent > 90:
                status = "degraded"
                warnings.append(f"High CPU usage: {cpu_percent}%")
            
            if memory.percent > 90:
                status = "degraded"
                warnings.append(f"High memory usage: {memory.percent}%")
            
            if disk.percent > 90:
                status = "degraded"
                warnings.append(f"High disk usage: {disk.percent}%")
            
            return {
                "status": status,
                "warnings": warnings,
                "details": {
                    "cpu_percent": cpu_percent,
                    "memory_percent": memory.percent,
                    "disk_percent": disk.percent,
                    "memory_available_gb": round(memory.available / (1024**3), 2),
                    "disk_free_gb": round(disk.free / (1024**3), 2),
                }
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "details": "System resource check failed"
            }
    
    async def run_all_checks(self) -> Dict[str, Any]:
        """Run all health checks and return comprehensive status."""
        start_time = time.time()
        
        # Run checks concurrently
        checks = await asyncio.gather(
            self.check_database(),
            self.check_redis(),
            self.check_ai_services(),
            self.check_system_resources(),
            return_exceptions=True
        )
        
        check_names = ["database", "redis", "ai_services", "system_resources"]
        results = {}
        
        for name, result in zip(check_names, checks):
            if isinstance(result, Exception):
                results[name] = {
                    "status": "unhealthy",
                    "error": str(result),
                    "details": f"{name} check failed with exception"
                }
            else:
                results[name] = result
        
        # Determine overall status
        statuses = [check.get("status", "unhealthy") for check in results.values()]
        
        if all(status == "healthy" for status in statuses):
            overall_status = "healthy"
        elif any(status == "unhealthy" for status in statuses):
            overall_status = "unhealthy"
        else:
            overall_status = "degraded"
        
        duration = time.time() - start_time
        self.last_check_time = datetime.now(timezone.utc)
        
        return {
            "status": overall_status,
            "timestamp": self.last_check_time.isoformat(),
            "duration_ms": round(duration * 1000, 2),
            "checks": results,
            "version": self.settings.app_version,
            "environment": self.settings.environment.value,
        }


# Global health checker instance
health_checker = HealthChecker()


def setup_monitoring(app: FastAPI) -> None:
    """Setup monitoring endpoints and background tasks."""
    settings = get_settings()
    
    # Set application info
    APP_INFO.info({
        "version": settings.app_version,
        "environment": settings.environment.value,
        "name": settings.app_name,
    })
    
    # Set start time
    APP_START_TIME.set(time.time())
    
    # Add metrics endpoint
    @app.get("/metrics")
    async def metrics():
        """Prometheus metrics endpoint."""
        # Update uptime
        APP_UPTIME.set(time.time() - APP_START_TIME._value.get())
        
        return Response(
            generate_latest(),
            media_type=CONTENT_TYPE_LATEST
        )
    
    # Add health check endpoints
    @app.get("/health")
    async def health():
        """Basic health check endpoint."""
        return {
            "status": "healthy",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "version": settings.app_version,
        }
    
    @app.get("/health/detailed")
    async def detailed_health():
        """Detailed health check with all dependencies."""
        return await health_checker.run_all_checks()
    
    @app.get("/health/ready")
    async def readiness():
        """Kubernetes readiness probe endpoint."""
        health_result = await health_checker.run_all_checks()
        
        if health_result["status"] in ["healthy", "degraded"]:
            return {"status": "ready"}
        else:
            from fastapi import HTTPException
            raise HTTPException(status_code=503, detail="Service not ready")
    
    @app.get("/health/live")
    async def liveness():
        """Kubernetes liveness probe endpoint."""
        return {"status": "alive"}
    
    logger.info("Monitoring endpoints configured", endpoints=[
        "/metrics", "/health", "/health/detailed", "/health/ready", "/health/live"
    ])


def record_ai_request(model: str, status: str, duration: float, tokens_used: int = 0, token_type: str = "total") -> None:
    """Record AI request metrics."""
    AI_REQUESTS_TOTAL.labels(model=model, status=status).inc()
    AI_REQUEST_DURATION.labels(model=model).observe(duration)
    if tokens_used > 0:
        AI_TOKENS_USED.labels(model=model, type=token_type).inc(tokens_used)


def record_collaboration_event(event_type: str) -> None:
    """Record collaboration event metrics."""
    COLLABORATION_EVENTS.labels(event_type=event_type).inc()


def record_workflow_execution(status: str, duration: float) -> None:
    """Record workflow execution metrics."""
    WORKFLOW_EXECUTIONS_TOTAL.labels(status=status).inc()
    WORKFLOW_EXECUTION_DURATION.observe(duration)


def record_webrtc_event(event_type: str) -> None:
    """Record WebRTC signaling event metrics."""
    WEBRTC_SIGNALING_EVENTS.labels(event_type=event_type).inc()


def update_active_sessions(collaboration_sessions: int, webrtc_connections: int, workflows: int) -> None:
    """Update active session metrics."""
    COLLABORATION_SESSIONS_ACTIVE.set(collaboration_sessions)
    WEBRTC_CONNECTIONS_ACTIVE.set(webrtc_connections)
    WORKFLOWS_ACTIVE.set(workflows)
