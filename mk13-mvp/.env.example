# MK13 AI Assistant Environment Configuration
# Copy this file to .env and update with your values

# Application
MK13_ENVIRONMENT=development
MK13_DEBUG=true
MK13_APP_NAME="MK13 AI Assistant"
MK13_APP_VERSION=1.0.0

# Server
MK13_HOST=0.0.0.0
MK13_PORT=8000
MK13_WORKERS=1
MK13_RELOAD=true

# Security
MK13_SECRET_KEY=your-secret-key-here-change-in-production
MK13_ACCESS_TOKEN_EXPIRE_MINUTES=30
MK13_REFRESH_TOKEN_EXPIRE_DAYS=7

# Database
MK13_DATABASE_URL=postgresql://mk13:mk13@localhost:5432/mk13
MK13_DATABASE_POOL_SIZE=20
MK13_DATABASE_MAX_OVERFLOW=30

# Redis
MK13_REDIS_URL=redis://localhost:6379/0
MK13_REDIS_POOL_SIZE=20

# Celery
MK13_CELERY_BROKER_URL=redis://localhost:6379/1
MK13_CELERY_RESULT_BACKEND=redis://localhost:6379/2

# CORS
MK13_CORS_ORIGINS=["http://localhost:3000","http://localhost:80"]
MK13_CORS_ALLOW_CREDENTIALS=true

# AI Services
MK13_OPENAI_API_KEY=your-openai-api-key
MK13_OPENAI_ORGANIZATION=your-openai-org-id
MK13_ANTHROPIC_API_KEY=your-anthropic-api-key
MK13_GOOGLE_AI_API_KEY=your-google-ai-api-key

# Google Workspace
MK13_GOOGLE_CLIENT_ID=your-google-client-id
MK13_GOOGLE_CLIENT_SECRET=your-google-client-secret
MK13_GOOGLE_REDIRECT_URI=http://localhost:8000/auth/google/callback

# Logging
MK13_LOG_LEVEL=DEBUG
MK13_LOG_FILE=./logs/mk13.log

# Monitoring
MK13_ENABLE_METRICS=true
MK13_METRICS_PORT=9090
MK13_ENABLE_TRACING=false
MK13_JAEGER_ENDPOINT=http://localhost:14268/api/traces
MK13_SENTRY_DSN=your-sentry-dsn

# Rate Limiting
MK13_RATE_LIMIT_ENABLED=true
MK13_RATE_LIMIT_REQUESTS=100
MK13_RATE_LIMIT_WINDOW=60

# File Storage
MK13_UPLOAD_MAX_SIZE=10485760
MK13_STORAGE_PATH=./storage

# Feature Flags
MK13_ENABLE_COLLABORATION=true
MK13_ENABLE_WORKFLOWS=true
MK13_ENABLE_CONTEXT_INTELLIGENCE=true
MK13_ENABLE_AI_ASSISTANT=true
MK13_ENABLE_MULTI_TENANCY=false

# WebRTC
MK13_STUN_SERVERS=["stun:stun.l.google.com:19302","stun:stun1.l.google.com:19302"]

# Production-specific (only set in production)
# MK13_ENVIRONMENT=production
# MK13_DEBUG=false
# MK13_SECRET_KEY=your-production-secret-key
# MK13_DATABASE_URL=***************************************/mk13
# MK13_REDIS_URL=redis://user:password@prod-redis:6379/0
