# Multi-stage Dockerfile for <PERSON>K13 AI Assistant Backend
# Optimized for production with minimal attack surface

# Build stage
FROM python:3.11-slim as builder

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies for building
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN groupadd -r mk13 && useradd -r -g mk13 mk13

# Set work directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements-prod.txt .
COPY pyproject.toml .

# Install Python dependencies
RUN pip install --upgrade pip setuptools wheel && \
    pip install -r requirements-prod.txt

# Copy source code
COPY src/ src/
COPY README.md LICENSE ./

# Install the package
RUN pip install -e .

# Production stage
FROM python:3.11-slim as production

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PATH="/home/<USER>/.local/bin:$PATH" \
    MK13_ENVIRONMENT=production

# Install runtime dependencies only
RUN apt-get update && apt-get install -y \
    curl \
    postgresql-client \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create non-root user
RUN groupadd -r mk13 && useradd -r -g mk13 mk13

# Create directories
RUN mkdir -p /app /app/logs /app/storage && \
    chown -R mk13:mk13 /app

# Switch to non-root user
USER mk13

# Set work directory
WORKDIR /app

# Copy installed packages from builder
COPY --from=builder --chown=mk13:mk13 /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=builder --chown=mk13:mk13 /usr/local/bin /usr/local/bin

# Copy application code
COPY --from=builder --chown=mk13:mk13 /app/src ./src
COPY --chown=mk13:mk13 pyproject.toml README.md LICENSE ./

# Install the package in user space
RUN pip install --user -e .

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health/live || exit 1

# Expose port
EXPOSE 8000

# Default command
CMD ["mk13-server", "--host", "0.0.0.0", "--port", "8000"]
