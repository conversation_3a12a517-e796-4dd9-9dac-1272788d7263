# MK13 Mobile Companion App

React Native mobile application for the MK13 AI Assistant platform, providing context-aware AI assistance on iOS and Android devices.

## 🚀 Features

### Core Functionality
- **Voice-First Interface**: Optimized for hands-free interaction
- **Context Synchronization**: Seamless context sharing across devices
- **Push Notifications**: Proactive AI suggestions and alerts
- **Offline Mode**: Basic functionality without internet connection
- **Biometric Authentication**: Secure login with fingerprint/face recognition

### AI Capabilities
- **Real-time Voice Processing**: Speech-to-text with AI response
- **Context-Aware Responses**: AI understands your current situation
- **Proactive Assistance**: AI anticipates your needs
- **Multi-modal Input**: Voice, text, and image inputs

### Mobile-Specific Features
- **Location Context**: GPS-based context detection
- **Calendar Integration**: Meeting and event awareness
- **Battery Optimization**: Efficient background processing
- **Quick Actions**: iOS shortcuts and Android widgets
- **Haptic Feedback**: Enhanced user experience

## 📋 Prerequisites

### Development Environment
- **Node.js**: 18.x or higher
- **React Native CLI**: Latest version
- **Android Studio**: For Android development
- **Xcode**: For iOS development (macOS only)
- **Java**: JDK 11 or higher

### Device Requirements
- **iOS**: 12.0 or higher
- **Android**: API level 21 (Android 5.0) or higher

## 🛠️ Installation

### 1. Clone and Setup
```bash
cd mk13-mvp/mobile
npm install
```

### 2. iOS Setup
```bash
cd ios
pod install
cd ..
```

### 3. Android Setup
Ensure Android SDK is properly configured in Android Studio.

### 4. Environment Configuration
```bash
cp .env.example .env
# Edit .env with your configuration
```

Required environment variables:
```env
REACT_APP_API_URL=http://localhost:8000
REACT_APP_WS_URL=ws://localhost:8000
REACT_APP_GOOGLE_CLIENT_ID=your_google_client_id
```

## 🚀 Running the App

### Development Mode

#### Start Metro Bundler
```bash
npm start
```

#### Run on iOS
```bash
npm run ios
# or
npx react-native run-ios
```

#### Run on Android
```bash
npm run android
# or
npx react-native run-android
```

### Production Build

#### Android
```bash
npm run build:android
```

#### iOS
```bash
npm run build:ios
```

## 🏗️ Architecture

### State Management
- **Redux Toolkit**: Centralized state management
- **Redux Persist**: State persistence with MMKV
- **Async Thunks**: Asynchronous action handling

### Navigation
- **React Navigation 6**: Stack and tab navigation
- **Deep Linking**: URL-based navigation
- **Modal Screens**: Overlay screens for specific actions

### Services
- **API Service**: HTTP client with authentication
- **Auth Service**: Google OAuth and biometric auth
- **Voice Service**: Speech recognition and synthesis
- **Context Service**: Context detection and management
- **Notification Service**: Push notifications

### Key Components
```
src/
├── components/          # Reusable UI components
├── screens/            # Screen components
├── services/           # Business logic services
├── store/              # Redux store and slices
├── hooks/              # Custom React hooks
├── utils/              # Utility functions
├── types/              # TypeScript type definitions
└── assets/             # Images, fonts, etc.
```

## 🔧 Configuration

### Push Notifications
Configure Firebase for push notifications:

1. Add `google-services.json` (Android) and `GoogleService-Info.plist` (iOS)
2. Configure Firebase Cloud Messaging
3. Set up notification permissions

### Voice Recognition
Configure speech recognition:

1. Add microphone permissions
2. Configure speech recognition languages
3. Set up wake word detection

### Biometric Authentication
Configure biometric authentication:

1. Add biometric permissions
2. Configure keychain access
3. Set up fallback authentication

## 🧪 Testing

### Unit Tests
```bash
npm test
```

### E2E Tests (Detox)
```bash
# Build for testing
npm run build:e2e:ios
npm run build:e2e:android

# Run tests
npm run test:e2e:ios
npm run test:e2e:android
```

### Manual Testing
```bash
# Debug mode with logs
npm run start -- --verbose
```

## 📱 Platform-Specific Features

### iOS
- **Siri Shortcuts**: Voice commands integration
- **Widgets**: Home screen widgets
- **Background App Refresh**: Context updates
- **Haptic Feedback**: Enhanced touch feedback

### Android
- **App Shortcuts**: Quick actions from launcher
- **Adaptive Icons**: Dynamic icon theming
- **Background Services**: Context monitoring
- **Notification Channels**: Categorized notifications

## 🔒 Security

### Data Protection
- **Keychain/Keystore**: Secure credential storage
- **Certificate Pinning**: API communication security
- **Biometric Authentication**: Device-level security
- **Data Encryption**: Local data protection

### Privacy
- **Permission Management**: Granular permission control
- **Data Minimization**: Only collect necessary data
- **Local Processing**: On-device AI when possible
- **Transparent Logging**: Clear data usage

## 🚀 Deployment

### App Store (iOS)
1. Configure signing certificates
2. Update version and build numbers
3. Create archive in Xcode
4. Upload to App Store Connect

### Google Play (Android)
1. Generate signed APK/AAB
2. Update version code and name
3. Upload to Google Play Console
4. Configure release tracks

### Over-the-Air Updates
Configure CodePush for instant updates:

```bash
npm install -g code-push-cli
code-push app add MK13Mobile-iOS ios react-native
code-push app add MK13Mobile-Android android react-native
```

## 🔧 Troubleshooting

### Common Issues

#### Metro Bundler Issues
```bash
npm start -- --reset-cache
```

#### iOS Build Issues
```bash
cd ios
pod deintegrate
pod install
```

#### Android Build Issues
```bash
cd android
./gradlew clean
cd ..
npm run android
```

#### Permission Issues
Ensure all required permissions are declared in:
- `android/app/src/main/AndroidManifest.xml`
- `ios/MK13Mobile/Info.plist`

### Performance Optimization

#### Bundle Size
```bash
npm run analyze-bundle
```

#### Memory Usage
Use Flipper for debugging memory leaks and performance issues.

#### Battery Usage
Monitor background tasks and optimize context detection intervals.

## 📊 Analytics

### Crash Reporting
Configure Crashlytics for crash reporting:

```javascript
import crashlytics from '@react-native-firebase/crashlytics';
crashlytics().recordError(error);
```

### Usage Analytics
Track user interactions while respecting privacy:

```javascript
import analytics from '@react-native-firebase/analytics';
analytics().logEvent('voice_command_used');
```

## 🤝 Contributing

1. Follow React Native best practices
2. Use TypeScript for type safety
3. Write tests for new features
4. Follow the existing code style
5. Update documentation

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

---

**Built with ❤️ for seamless AI assistance on mobile devices**
