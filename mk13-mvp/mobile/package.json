{"name": "mk13-mobile", "version": "1.0.0", "description": "MK13 Mobile Companion App - AI Assistant with Context Awareness", "main": "index.js", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "build:android": "cd android && ./gradlew assembleRelease", "build:ios": "cd ios && xcodebuild -workspace MK13Mobile.xcworkspace -scheme MK13Mobile -configuration Release -destination generic/platform=iOS -archivePath MK13Mobile.xcarchive archive", "clean": "react-native clean", "pod-install": "cd ios && pod install"}, "dependencies": {"react": "18.2.0", "react-native": "0.73.2", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@react-navigation/bottom-tabs": "^6.5.11", "@reduxjs/toolkit": "^2.0.1", "react-redux": "^9.0.4", "socket.io-client": "^4.7.4", "@react-native-async-storage/async-storage": "^1.21.0", "@react-native-voice/voice": "^3.2.4", "react-native-permissions": "^4.1.1", "react-native-push-notification": "^8.1.1", "@react-native-firebase/app": "^19.0.1", "@react-native-firebase/messaging": "^19.0.1", "react-native-gesture-handler": "^2.14.1", "react-native-reanimated": "^3.6.2", "react-native-safe-area-context": "^4.8.2", "react-native-screens": "^3.29.0", "react-native-vector-icons": "^10.0.3", "react-native-keychain": "^8.1.3", "react-native-biometrics": "^3.0.1", "react-native-device-info": "^10.12.0", "react-native-background-job": "^1.2.0", "react-native-background-task": "^0.2.1", "react-native-network-info": "^5.2.1", "react-native-orientation-locker": "^1.6.0", "react-native-haptic-feedback": "^2.2.0", "react-native-linear-gradient": "^2.8.3", "react-native-svg": "^14.1.0", "react-native-modal": "^13.0.1", "react-native-toast-message": "^2.2.0", "react-native-share": "^10.0.2", "react-native-document-picker": "^9.1.1", "react-native-image-picker": "^7.1.0", "react-native-contacts": "^7.0.8", "react-native-calendar-events": "^2.2.0", "react-native-fs": "^2.20.0", "react-native-url-polyfill": "^2.0.0", "react-native-mmkv": "^2.11.0", "react-native-quick-actions": "^0.3.13"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.73.1", "@react-native/metro-config": "^0.73.2", "@react-native/typescript-config": "^0.73.1", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "metro-react-native-babel-preset": "0.77.0", "prettier": "2.8.8", "react-test-renderer": "18.2.0", "typescript": "5.0.4", "@testing-library/react-native": "^12.4.3", "@testing-library/jest-native": "^5.4.3", "detox": "^20.13.5"}, "engines": {"node": ">=18"}, "keywords": ["react-native", "ai-assistant", "productivity", "context-awareness", "mobile-app"], "author": "MK13 Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/mk13-mvp.git"}, "bugs": {"url": "https://github.com/your-org/mk13-mvp/issues"}, "homepage": "https://github.com/your-org/mk13-mvp#readme"}