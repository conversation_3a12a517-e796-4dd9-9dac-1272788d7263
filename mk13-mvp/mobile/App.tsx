/**
 * MK13 Mobile Companion App
 * Main Application Component with Navigation and State Management
 */

import React, { useEffect } from 'react';
import { StatusBar, Platform, Alert } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { Provider } from 'react-redux';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import Toast from 'react-native-toast-message';
import { requestNotifications, PERMISSIONS, request } from 'react-native-permissions';

import { store } from './src/store/store';
import AppNavigator from './src/navigation/AppNavigator';
import { initializeServices } from './src/services/initialization';
import { useAppDispatch } from './src/hooks/redux';
import { setDeviceInfo } from './src/store/slices/deviceSlice';
import DeviceInfo from 'react-native-device-info';
import { initializeNotifications } from './src/services/notificationService';
import { initializeVoiceService } from './src/services/voiceService';
import { initializeContextService } from './src/services/contextService';

const AppContent: React.FC = () => {
  const dispatch = useAppDispatch();

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      // Initialize device information
      const deviceInfo = {
        deviceId: await DeviceInfo.getUniqueId(),
        deviceName: await DeviceInfo.getDeviceName(),
        systemName: DeviceInfo.getSystemName(),
        systemVersion: DeviceInfo.getSystemVersion(),
        appVersion: DeviceInfo.getVersion(),
        buildNumber: DeviceInfo.getBuildNumber(),
        bundleId: DeviceInfo.getBundleId(),
        isTablet: DeviceInfo.isTablet(),
        hasNotch: DeviceInfo.hasNotch(),
      };

      dispatch(setDeviceInfo(deviceInfo));

      // Request permissions
      await requestPermissions();

      // Initialize services
      await initializeServices();
      await initializeNotifications();
      await initializeVoiceService();
      await initializeContextService();

      console.log('MK13 Mobile App initialized successfully');
    } catch (error) {
      console.error('Failed to initialize app:', error);
      Alert.alert(
        'Initialization Error',
        'Failed to initialize the app. Some features may not work properly.',
        [{ text: 'OK' }]
      );
    }
  };

  const requestPermissions = async () => {
    try {
      // Request notification permissions
      const notificationResult = await requestNotifications(['alert', 'badge', 'sound']);
      console.log('Notification permission:', notificationResult.status);

      // Request microphone permission for voice input
      const microphonePermission = Platform.select({
        ios: PERMISSIONS.IOS.MICROPHONE,
        android: PERMISSIONS.ANDROID.RECORD_AUDIO,
      });

      if (microphonePermission) {
        const micResult = await request(microphonePermission);
        console.log('Microphone permission:', micResult);
      }

      // Request contacts permission (for context awareness)
      const contactsPermission = Platform.select({
        ios: PERMISSIONS.IOS.CONTACTS,
        android: PERMISSIONS.ANDROID.READ_CONTACTS,
      });

      if (contactsPermission) {
        const contactsResult = await request(contactsPermission);
        console.log('Contacts permission:', contactsResult);
      }

      // Request calendar permission (for context awareness)
      const calendarPermission = Platform.select({
        ios: PERMISSIONS.IOS.CALENDARS,
        android: PERMISSIONS.ANDROID.READ_CALENDAR,
      });

      if (calendarPermission) {
        const calendarResult = await request(calendarPermission);
        console.log('Calendar permission:', calendarResult);
      }

      // Request location permission (for context awareness)
      const locationPermission = Platform.select({
        ios: PERMISSIONS.IOS.LOCATION_WHEN_IN_USE,
        android: PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
      });

      if (locationPermission) {
        const locationResult = await request(locationPermission);
        console.log('Location permission:', locationResult);
      }

    } catch (error) {
      console.error('Permission request failed:', error);
    }
  };

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <SafeAreaProvider>
        <StatusBar
          barStyle="light-content"
          backgroundColor="#000000"
          translucent={true}
        />
        <NavigationContainer>
          <AppNavigator />
        </NavigationContainer>
        <Toast />
      </SafeAreaProvider>
    </GestureHandlerRootView>
  );
};

const App: React.FC = () => {
  return (
    <Provider store={store}>
      <AppContent />
    </Provider>
  );
};

export default App;
