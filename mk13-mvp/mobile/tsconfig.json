{"extends": "@react-native/typescript-config/tsconfig.json", "compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": "./src", "paths": {"@/*": ["*"], "@components/*": ["components/*"], "@screens/*": ["screens/*"], "@services/*": ["services/*"], "@store/*": ["store/*"], "@hooks/*": ["hooks/*"], "@utils/*": ["utils/*"], "@types/*": ["types/*"], "@assets/*": ["assets/*"]}}, "include": ["src/**/*", "App.tsx", "index.js"], "exclude": ["node_modules", "android", "ios", "**/*.test.ts", "**/*.test.tsx"]}