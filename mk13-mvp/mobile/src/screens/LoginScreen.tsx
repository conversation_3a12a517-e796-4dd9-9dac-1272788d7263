/**
 * Login Screen for MK13 Mobile App
 * Handles Google OAuth and biometric authentication
 */

import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { useAppSelector, useAppDispatch } from '../hooks/redux';
import { loginWithGoogle, loginWithBiometric } from '../store/slices/authSlice';
import { authService } from '../services/authService';

const LoginScreen: React.FC = () => {
  const dispatch = useAppDispatch();
  const { isLoading, error } = useAppSelector(state => state.auth);
  const [biometricAvailable, setBiometricAvailable] = useState(false);
  const [hasStoredCredentials, setHasStoredCredentials] = useState(false);

  useEffect(() => {
    checkBiometricAvailability();
  }, []);

  const checkBiometricAvailability = async () => {
    try {
      const { available } = await authService.isBiometricAvailable();
      setBiometricAvailable(available);

      if (available) {
        const hasCredentials = await authService.hasStoredCredentials();
        setHasStoredCredentials(hasCredentials);
      }
    } catch (error) {
      console.error('Failed to check biometric availability:', error);
    }
  };

  const handleGoogleLogin = async () => {
    try {
      await dispatch(loginWithGoogle()).unwrap();
    } catch (error: any) {
      Alert.alert(
        'Login Failed',
        error.message || 'Failed to login with Google. Please try again.',
        [{ text: 'OK' }]
      );
    }
  };

  const handleBiometricLogin = async () => {
    try {
      await dispatch(loginWithBiometric()).unwrap();
    } catch (error: any) {
      Alert.alert(
        'Biometric Login Failed',
        error.message || 'Failed to authenticate with biometrics. Please try again.',
        [{ text: 'OK' }]
      );
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={['#1a1a2e', '#16213e', '#0f3460']}
        style={styles.gradient}
      >
        <View style={styles.content}>
          {/* Logo and Title */}
          <View style={styles.header}>
            <View style={styles.logoContainer}>
              <Icon name="assistant" size={80} color="#4CAF50" />
            </View>
            <Text style={styles.title}>MK13</Text>
            <Text style={styles.subtitle}>
              Your AI-Powered Assistant
            </Text>
            <Text style={styles.description}>
              Experience context-aware AI assistance that adapts to your workflow
            </Text>
          </View>

          {/* Login Options */}
          <View style={styles.loginSection}>
            {/* Biometric Login */}
            {biometricAvailable && hasStoredCredentials && (
              <TouchableOpacity
                style={[styles.loginButton, styles.biometricButton]}
                onPress={handleBiometricLogin}
                disabled={isLoading}
              >
                <Icon name="fingerprint" size={24} color="#FFFFFF" />
                <Text style={styles.loginButtonText}>
                  Login with Biometrics
                </Text>
              </TouchableOpacity>
            )}

            {/* Google Login */}
            <TouchableOpacity
              style={[styles.loginButton, styles.googleButton]}
              onPress={handleGoogleLogin}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator size="small" color="#FFFFFF" />
              ) : (
                <Icon name="login" size={24} color="#FFFFFF" />
              )}
              <Text style={styles.loginButtonText}>
                Continue with Google
              </Text>
            </TouchableOpacity>

            {/* Error Message */}
            {error && (
              <View style={styles.errorContainer}>
                <Icon name="error" size={20} color="#F44336" />
                <Text style={styles.errorText}>{error}</Text>
              </View>
            )}
          </View>

          {/* Features */}
          <View style={styles.featuresSection}>
            <Text style={styles.featuresTitle}>Key Features</Text>
            
            <View style={styles.feature}>
              <Icon name="visibility" size={20} color="#4CAF50" />
              <Text style={styles.featureText}>
                Context-aware AI that understands your workflow
              </Text>
            </View>
            
            <View style={styles.feature}>
              <Icon name="mic" size={20} color="#4CAF50" />
              <Text style={styles.featureText}>
                Voice-first interface for hands-free interaction
              </Text>
            </View>
            
            <View style={styles.feature}>
              <Icon name="sync" size={20} color="#4CAF50" />
              <Text style={styles.featureText}>
                Seamless sync across all your devices
              </Text>
            </View>
            
            <View style={styles.feature}>
              <Icon name="security" size={20} color="#4CAF50" />
              <Text style={styles.featureText}>
                Enterprise-grade security and privacy
              </Text>
            </View>
          </View>

          {/* Footer */}
          <View style={styles.footer}>
            <Text style={styles.footerText}>
              By continuing, you agree to our Terms of Service and Privacy Policy
            </Text>
          </View>
        </View>
      </LinearGradient>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a1a2e',
  },
  gradient: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    justifyContent: 'space-between',
  },
  header: {
    alignItems: 'center',
    paddingTop: 60,
  },
  logoContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 18,
    color: 'rgba(255, 255, 255, 0.8)',
    marginBottom: 16,
  },
  description: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.6)',
    textAlign: 'center',
    lineHeight: 20,
    maxWidth: 280,
  },
  loginSection: {
    paddingVertical: 20,
  },
  loginButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    marginBottom: 16,
  },
  biometricButton: {
    backgroundColor: '#673AB7',
  },
  googleButton: {
    backgroundColor: '#4285F4',
  },
  loginButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 12,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(244, 67, 54, 0.1)',
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
  },
  errorText: {
    color: '#F44336',
    fontSize: 14,
    marginLeft: 8,
    flex: 1,
  },
  featuresSection: {
    paddingVertical: 20,
  },
  featuresTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 16,
    textAlign: 'center',
  },
  feature: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  featureText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    marginLeft: 12,
    flex: 1,
  },
  footer: {
    paddingBottom: 20,
  },
  footerText: {
    color: 'rgba(255, 255, 255, 0.5)',
    fontSize: 12,
    textAlign: 'center',
    lineHeight: 16,
  },
});

export default LoginScreen;
