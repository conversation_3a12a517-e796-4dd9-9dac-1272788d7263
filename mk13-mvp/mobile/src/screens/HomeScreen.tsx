/**
 * Home Screen for MK13 Mobile App
 * Main dashboard with context awareness and quick actions
 */

import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { useAppSelector, useAppDispatch } from '../hooks/redux';
import { detectCurrentContext, fetchContexts } from '../store/slices/contextSlice';
import { addNotification } from '../store/slices/notificationSlice';
import VoiceButton from '../components/VoiceButton';
import ContextCard from '../components/ContextCard';
import QuickActionGrid from '../components/QuickActionGrid';
import RecentActivity from '../components/RecentActivity';
import WeatherWidget from '../components/WeatherWidget';

const { width } = Dimensions.get('window');

const HomeScreen: React.FC = () => {
  const dispatch = useAppDispatch();
  const { user } = useAppSelector(state => state.auth);
  const { currentContext, detectedContext, contexts } = useAppSelector(state => state.context);
  const { info: deviceInfo } = useAppSelector(state => state.device);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    if (user) {
      loadInitialData();
    }
  }, [user]);

  const loadInitialData = async () => {
    try {
      if (user) {
        await dispatch(fetchContexts(user.id)).unwrap();
        await dispatch(detectCurrentContext()).unwrap();
      }
    } catch (error) {
      console.error('Failed to load initial data:', error);
      dispatch(addNotification({
        title: 'Error',
        message: 'Failed to load data. Please try again.',
        type: 'error',
      }));
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadInitialData();
    setRefreshing(false);
  };

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 18) return 'Good afternoon';
    return 'Good evening';
  };

  const getBatteryColor = () => {
    const level = deviceInfo?.batteryLevel || 100;
    if (level > 50) return '#4CAF50';
    if (level > 20) return '#FF9800';
    return '#F44336';
  };

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={['#1a1a2e', '#16213e', '#0f3460']}
        style={styles.gradient}
      >
        <ScrollView
          style={styles.scrollView}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          showsVerticalScrollIndicator={false}
        >
          {/* Header */}
          <View style={styles.header}>
            <View style={styles.headerLeft}>
              <Text style={styles.greeting}>
                {getGreeting()}, {user?.name?.split(' ')[0] || 'User'}
              </Text>
              <Text style={styles.subtitle}>
                How can I assist you today?
              </Text>
            </View>
            <View style={styles.headerRight}>
              <View style={styles.batteryIndicator}>
                <Icon 
                  name="battery-std" 
                  size={20} 
                  color={getBatteryColor()} 
                />
                <Text style={styles.batteryText}>
                  {deviceInfo?.batteryLevel || '--'}%
                </Text>
              </View>
            </View>
          </View>

          {/* Current Context */}
          {currentContext && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Current Context</Text>
              <ContextCard context={currentContext} />
            </View>
          )}

          {/* Context Detection */}
          {detectedContext && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Detected Context</Text>
              <View style={styles.detectedContextCard}>
                <View style={styles.detectedContextHeader}>
                  <Icon name="visibility" size={20} color="#4CAF50" />
                  <Text style={styles.detectedContextTitle}>
                    Context Detected
                  </Text>
                  <Text style={styles.confidenceText}>
                    {Math.round(detectedContext.confidence * 100)}%
                  </Text>
                </View>
                <Text style={styles.detectedContextDescription}>
                  {detectedContext.suggestedContext?.name || 'Unknown context'}
                </Text>
                <View style={styles.detectedContextActions}>
                  <TouchableOpacity style={styles.acceptButton}>
                    <Text style={styles.acceptButtonText}>Switch</Text>
                  </TouchableOpacity>
                  <TouchableOpacity style={styles.dismissButton}>
                    <Text style={styles.dismissButtonText}>Dismiss</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          )}

          {/* Voice Assistant */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Voice Assistant</Text>
            <View style={styles.voiceSection}>
              <VoiceButton />
              <Text style={styles.voiceHint}>
                Tap and hold to speak, or say "Hey MK13"
              </Text>
            </View>
          </View>

          {/* Quick Actions */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Quick Actions</Text>
            <QuickActionGrid />
          </View>

          {/* Weather Widget */}
          <View style={styles.section}>
            <WeatherWidget />
          </View>

          {/* Recent Activity */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Recent Activity</Text>
            <RecentActivity />
          </View>

          {/* Context Suggestions */}
          {contexts.length > 0 && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Your Contexts</Text>
              <ScrollView 
                horizontal 
                showsHorizontalScrollIndicator={false}
                style={styles.contextScroll}
              >
                {contexts.slice(0, 5).map((context) => (
                  <View key={context.id} style={styles.contextItem}>
                    <ContextCard context={context} compact />
                  </View>
                ))}
              </ScrollView>
            </View>
          )}
        </ScrollView>
      </LinearGradient>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a1a2e',
  },
  gradient: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingVertical: 20,
  },
  headerLeft: {
    flex: 1,
  },
  headerRight: {
    alignItems: 'flex-end',
  },
  greeting: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.7)',
  },
  batteryIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  batteryText: {
    color: '#FFFFFF',
    fontSize: 12,
    marginLeft: 4,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 12,
  },
  detectedContextCard: {
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: 'rgba(76, 175, 80, 0.3)',
  },
  detectedContextHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  detectedContextTitle: {
    color: '#4CAF50',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
    flex: 1,
  },
  confidenceText: {
    color: '#4CAF50',
    fontSize: 14,
    fontWeight: '500',
  },
  detectedContextDescription: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    marginBottom: 12,
  },
  detectedContextActions: {
    flexDirection: 'row',
    gap: 12,
  },
  acceptButton: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  acceptButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  dismissButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  dismissButtonText: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 14,
    fontWeight: '600',
  },
  voiceSection: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  voiceHint: {
    color: 'rgba(255, 255, 255, 0.6)',
    fontSize: 14,
    marginTop: 12,
    textAlign: 'center',
  },
  contextScroll: {
    marginHorizontal: -20,
    paddingHorizontal: 20,
  },
  contextItem: {
    marginRight: 12,
    width: width * 0.7,
  },
});

export default HomeScreen;
