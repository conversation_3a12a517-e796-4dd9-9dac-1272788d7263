/**
 * Voice Button Component for MK13 Mobile App
 * Handles voice input with visual feedback
 */

import React, { useEffect, useRef } from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Text,
  Vibration,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useAppSelector, useAppDispatch } from '../hooks/redux';
import {
  startListening,
  stopListening,
  setCurrentTranscript,
  setFinalTranscript,
  processVoiceCommand,
} from '../store/slices/voiceSlice';

const VoiceButton: React.FC = () => {
  const dispatch = useAppDispatch();
  const { isListening, isProcessing, currentTranscript, isAvailable } = useAppSelector(
    state => state.voice
  );

  const scaleAnim = useRef(new Animated.Value(1)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const opacityAnim = useRef(new Animated.Value(0.3)).current;

  useEffect(() => {
    if (isListening) {
      startPulseAnimation();
    } else {
      stopPulseAnimation();
    }
  }, [isListening]);

  const startPulseAnimation = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.2,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
      ])
    ).start();

    Animated.loop(
      Animated.sequence([
        Animated.timing(opacityAnim, {
          toValue: 0.8,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(opacityAnim, {
          toValue: 0.3,
          duration: 800,
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  const stopPulseAnimation = () => {
    pulseAnim.stopAnimation();
    opacityAnim.stopAnimation();
    
    Animated.parallel([
      Animated.timing(pulseAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnim, {
        toValue: 0.3,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const handlePressIn = async () => {
    if (!isAvailable || isProcessing) return;

    Vibration.vibrate(50);
    
    Animated.spring(scaleAnim, {
      toValue: 0.95,
      useNativeDriver: true,
    }).start();

    try {
      await dispatch(startListening()).unwrap();
    } catch (error) {
      console.error('Failed to start listening:', error);
    }
  };

  const handlePressOut = async () => {
    if (!isAvailable) return;

    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
    }).start();

    if (isListening) {
      try {
        await dispatch(stopListening()).unwrap();
        
        // Process the final transcript if available
        if (currentTranscript.trim()) {
          await dispatch(processVoiceCommand(currentTranscript)).unwrap();
        }
      } catch (error) {
        console.error('Failed to stop listening or process command:', error);
      }
    }
  };

  const getButtonColor = () => {
    if (!isAvailable) return '#666666';
    if (isProcessing) return '#FF9800';
    if (isListening) return '#F44336';
    return '#4CAF50';
  };

  const getButtonIcon = () => {
    if (isProcessing) return 'hourglass-empty';
    if (isListening) return 'mic';
    return 'mic-none';
  };

  const getStatusText = () => {
    if (!isAvailable) return 'Voice not available';
    if (isProcessing) return 'Processing...';
    if (isListening) return 'Listening...';
    return 'Tap to speak';
  };

  return (
    <View style={styles.container}>
      {/* Pulse Ring */}
      {isListening && (
        <Animated.View
          style={[
            styles.pulseRing,
            {
              transform: [{ scale: pulseAnim }],
              opacity: opacityAnim,
              borderColor: getButtonColor(),
            },
          ]}
        />
      )}

      {/* Main Button */}
      <Animated.View
        style={[
          styles.buttonContainer,
          {
            transform: [{ scale: scaleAnim }],
          },
        ]}
      >
        <TouchableOpacity
          style={[
            styles.button,
            {
              backgroundColor: getButtonColor(),
              opacity: isAvailable ? 1 : 0.5,
            },
          ]}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          disabled={!isAvailable}
          activeOpacity={0.8}
        >
          <Icon
            name={getButtonIcon()}
            size={32}
            color="#FFFFFF"
          />
        </TouchableOpacity>
      </Animated.View>

      {/* Status Text */}
      <Text style={styles.statusText}>
        {getStatusText()}
      </Text>

      {/* Transcript Display */}
      {currentTranscript && (
        <View style={styles.transcriptContainer}>
          <Text style={styles.transcriptText}>
            {currentTranscript}
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  pulseRing: {
    position: 'absolute',
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 2,
  },
  buttonContainer: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  button: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statusText: {
    marginTop: 12,
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
  },
  transcriptContainer: {
    marginTop: 16,
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 8,
    maxWidth: 280,
  },
  transcriptText: {
    color: '#FFFFFF',
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default VoiceButton;
