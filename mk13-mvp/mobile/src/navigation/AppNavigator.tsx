/**
 * App Navigator for MK13 Mobile App
 * Main navigation structure with authentication flow
 */

import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { useAppSelector } from '../hooks/redux';

// Auth Screens
import LoginScreen from '../screens/LoginScreen';
import OnboardingScreen from '../screens/OnboardingScreen';

// Main Screens
import HomeScreen from '../screens/HomeScreen';
import ChatScreen from '../screens/ChatScreen';
import ContextScreen from '../screens/ContextScreen';
import SettingsScreen from '../screens/SettingsScreen';
import ProfileScreen from '../screens/ProfileScreen';

// Modal Screens
import VoiceModalScreen from '../screens/VoiceModalScreen';
import ContextDetailScreen from '../screens/ContextDetailScreen';
import WorkflowScreen from '../screens/WorkflowScreen';

const Stack = createStackNavigator();
const Tab = createBottomTabNavigator();

const TabNavigator: React.FC = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: string;

          switch (route.name) {
            case 'Home':
              iconName = 'home';
              break;
            case 'Chat':
              iconName = 'chat';
              break;
            case 'Context':
              iconName = 'layers';
              break;
            case 'Settings':
              iconName = 'settings';
              break;
            default:
              iconName = 'help';
          }

          return <Icon name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: '#4CAF50',
        tabBarInactiveTintColor: 'rgba(255, 255, 255, 0.6)',
        tabBarStyle: {
          backgroundColor: '#1a1a2e',
          borderTopColor: 'rgba(255, 255, 255, 0.1)',
          borderTopWidth: 1,
          paddingBottom: 8,
          paddingTop: 8,
          height: 80,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '500',
        },
        headerShown: false,
      })}
    >
      <Tab.Screen name="Home" component={HomeScreen} />
      <Tab.Screen name="Chat" component={ChatScreen} />
      <Tab.Screen name="Context" component={ContextScreen} />
      <Tab.Screen name="Settings" component={SettingsScreen} />
    </Tab.Navigator>
  );
};

const AuthNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        cardStyle: { backgroundColor: '#1a1a2e' },
      }}
    >
      <Stack.Screen name="Login" component={LoginScreen} />
      <Stack.Screen name="Onboarding" component={OnboardingScreen} />
    </Stack.Navigator>
  );
};

const MainNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        cardStyle: { backgroundColor: '#1a1a2e' },
      }}
    >
      <Stack.Screen name="MainTabs" component={TabNavigator} />
      <Stack.Screen 
        name="VoiceModal" 
        component={VoiceModalScreen}
        options={{
          presentation: 'modal',
          cardStyle: { backgroundColor: 'rgba(0, 0, 0, 0.8)' },
        }}
      />
      <Stack.Screen 
        name="ContextDetail" 
        component={ContextDetailScreen}
        options={{
          presentation: 'card',
          headerShown: true,
          headerStyle: {
            backgroundColor: '#1a1a2e',
          },
          headerTintColor: '#FFFFFF',
          headerTitle: 'Context Details',
        }}
      />
      <Stack.Screen 
        name="Workflow" 
        component={WorkflowScreen}
        options={{
          presentation: 'card',
          headerShown: true,
          headerStyle: {
            backgroundColor: '#1a1a2e',
          },
          headerTintColor: '#FFFFFF',
          headerTitle: 'Workflows',
        }}
      />
      <Stack.Screen 
        name="Profile" 
        component={ProfileScreen}
        options={{
          presentation: 'card',
          headerShown: true,
          headerStyle: {
            backgroundColor: '#1a1a2e',
          },
          headerTintColor: '#FFFFFF',
          headerTitle: 'Profile',
        }}
      />
    </Stack.Navigator>
  );
};

const AppNavigator: React.FC = () => {
  const { isAuthenticated } = useAppSelector(state => state.auth);

  return (
    <NavigationContainer>
      {isAuthenticated ? <MainNavigator /> : <AuthNavigator />}
    </NavigationContainer>
  );
};

export default AppNavigator;
