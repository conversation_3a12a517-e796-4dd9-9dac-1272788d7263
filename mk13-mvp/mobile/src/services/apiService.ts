/**
 * API Service for MK13 Mobile App
 * Handles HTTP requests to the backend API
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { store } from '../store/store';
import { refreshAccessToken, clearAuth } from '../store/slices/authSlice';
import { setOnlineStatus } from '../store/slices/syncSlice';

class ApiService {
  private api: AxiosInstance;
  private isRefreshing = false;
  private failedQueue: Array<{
    resolve: (value?: any) => void;
    reject: (error?: any) => void;
  }> = [];

  constructor() {
    this.api = axios.create({
      baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8000',
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor
    this.api.interceptors.request.use(
      (config) => {
        const state = store.getState();
        const { accessToken, sessionToken } = state.auth;

        // Add authentication headers
        if (accessToken) {
          config.headers.Authorization = `Bearer ${accessToken}`;
        }

        if (sessionToken) {
          config.headers['X-Session-Token'] = sessionToken;
        }

        // Add device info
        const { info } = state.device;
        if (info) {
          config.headers['X-Device-ID'] = info.deviceId;
          config.headers['X-Device-Platform'] = info.systemName;
          config.headers['X-App-Version'] = info.appVersion;
        }

        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.api.interceptors.response.use(
      (response) => {
        // Update online status
        store.dispatch(setOnlineStatus(true));
        return response;
      },
      async (error) => {
        const originalRequest = error.config;

        // Handle network errors
        if (!error.response) {
          store.dispatch(setOnlineStatus(false));
          return Promise.reject(new Error('Network error. Please check your connection.'));
        }

        // Update online status
        store.dispatch(setOnlineStatus(true));

        // Handle 401 errors (token expired)
        if (error.response.status === 401 && !originalRequest._retry) {
          if (this.isRefreshing) {
            // If already refreshing, queue the request
            return new Promise((resolve, reject) => {
              this.failedQueue.push({ resolve, reject });
            }).then(() => {
              return this.api(originalRequest);
            }).catch(err => {
              return Promise.reject(err);
            });
          }

          originalRequest._retry = true;
          this.isRefreshing = true;

          try {
            // Attempt to refresh token
            await store.dispatch(refreshAccessToken()).unwrap();
            
            // Process failed queue
            this.processQueue(null);
            
            // Retry original request
            return this.api(originalRequest);
          } catch (refreshError) {
            // Refresh failed, clear auth and redirect to login
            this.processQueue(refreshError);
            store.dispatch(clearAuth());
            return Promise.reject(refreshError);
          } finally {
            this.isRefreshing = false;
          }
        }

        // Handle other errors
        const errorMessage = error.response?.data?.message || error.message || 'An error occurred';
        return Promise.reject(new Error(errorMessage));
      }
    );
  }

  private processQueue(error: any) {
    this.failedQueue.forEach(({ resolve, reject }) => {
      if (error) {
        reject(error);
      } else {
        resolve();
      }
    });
    
    this.failedQueue = [];
  }

  // HTTP Methods
  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.api.get(url, config);
  }

  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.api.post(url, data, config);
  }

  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.api.put(url, data, config);
  }

  async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.api.patch(url, data, config);
  }

  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.api.delete(url, config);
  }

  // File upload
  async uploadFile(url: string, file: any, onProgress?: (progress: number) => void): Promise<AxiosResponse> {
    const formData = new FormData();
    formData.append('file', file);

    return this.api.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      },
    });
  }

  // Specific API endpoints
  async sendChatMessage(message: string, contextId?: string) {
    return this.post('/api/ai/chat', {
      message,
      context_id: contextId,
    });
  }

  async processVoiceInput(audioFile: any, language = 'en-US') {
    const formData = new FormData();
    formData.append('audio_file', audioFile);
    formData.append('language', language);

    return this.api.post('/api/ai/voice', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }

  async fetchContexts(userId: string) {
    return this.get(`/api/contexts?user_id=${userId}`);
  }

  async createContext(contextData: any) {
    return this.post('/api/contexts', contextData);
  }

  async updateContext(contextId: string, updates: any) {
    return this.put(`/api/contexts/${contextId}`, updates);
  }

  async deleteContext(contextId: string) {
    return this.delete(`/api/contexts/${contextId}`);
  }

  async getGoogleEmails(limit = 10) {
    return this.get(`/api/google/emails?limit=${limit}`);
  }

  async getGoogleCalendarEvents() {
    return this.get('/api/google/calendar');
  }

  async sendGoogleEmail(emailData: any) {
    return this.post('/api/google/email/send', emailData);
  }

  // Health check
  async healthCheck() {
    return this.get('/health');
  }

  // Update base URL (for switching environments)
  updateBaseURL(baseURL: string) {
    this.api.defaults.baseURL = baseURL;
  }

  // Get current base URL
  getBaseURL(): string {
    return this.api.defaults.baseURL || '';
  }
}

export const apiService = new ApiService();
