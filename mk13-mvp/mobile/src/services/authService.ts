/**
 * Authentication Service for MK13 Mobile App
 * Handles Google OAuth, biometric authentication, and session management
 */

import { GoogleSignin } from '@react-native-google-signin/google-signin';
import ReactNativeBiometrics from 'react-native-biometrics';
import Keychain from 'react-native-keychain';
import { apiService } from './apiService';

const rnBiometrics = new ReactNativeBiometrics();

export interface AuthResult {
  user: {
    id: string;
    email: string;
    name: string;
    picture?: string;
    verified_email: boolean;
    created_at: string;
    last_login: string;
  };
  accessToken: string;
  refreshToken?: string;
  sessionToken: string;
  expiresAt: string;
}

class AuthService {
  private isInitialized = false;

  async initialize() {
    if (this.isInitialized) return;

    try {
      // Configure Google Sign-In
      GoogleSignin.configure({
        webClientId: process.env.REACT_APP_GOOGLE_CLIENT_ID,
        offlineAccess: true,
        hostedDomain: '',
        forceCodeForRefreshToken: true,
      });

      this.isInitialized = true;
      console.log('Auth service initialized');
    } catch (error) {
      console.error('Failed to initialize auth service:', error);
      throw error;
    }
  }

  async loginWithGoogle(): Promise<AuthResult> {
    try {
      await this.initialize();

      // Check if device has Google Play Services
      await GoogleSignin.hasPlayServices();

      // Sign in with Google
      const userInfo = await GoogleSignin.signIn();
      
      if (!userInfo.idToken) {
        throw new Error('No ID token received from Google');
      }

      // Send token to backend for verification and session creation
      const response = await apiService.post('/auth/google/mobile', {
        idToken: userInfo.idToken,
        accessToken: userInfo.accessToken,
      });

      const authResult: AuthResult = response.data;

      // Store tokens securely
      await this.storeTokensSecurely(authResult);

      return authResult;
    } catch (error: any) {
      console.error('Google login failed:', error);
      throw new Error(error.message || 'Google login failed');
    }
  }

  async loginWithBiometric(): Promise<AuthResult> {
    try {
      // Check if biometric authentication is available
      const { available, biometryType } = await rnBiometrics.isSensorAvailable();
      
      if (!available) {
        throw new Error('Biometric authentication not available');
      }

      // Retrieve stored credentials
      const credentials = await Keychain.getInternetCredentials('mk13_auth');
      
      if (!credentials || !credentials.password) {
        throw new Error('No stored credentials found');
      }

      // Prompt for biometric authentication
      const { success } = await rnBiometrics.simplePrompt({
        promptMessage: 'Authenticate to access MK13',
        fallbackPromptMessage: 'Use passcode',
      });

      if (!success) {
        throw new Error('Biometric authentication failed');
      }

      // Parse stored auth data
      const authData = JSON.parse(credentials.password);
      
      // Validate session with backend
      const response = await apiService.post('/auth/validate-session', {
        sessionToken: authData.sessionToken,
      });

      return response.data;
    } catch (error: any) {
      console.error('Biometric login failed:', error);
      throw new Error(error.message || 'Biometric login failed');
    }
  }

  async refreshToken(refreshToken: string): Promise<{ accessToken: string; expiresAt: string; sessionToken?: string }> {
    try {
      const response = await apiService.post('/auth/refresh', {
        refreshToken,
      });

      const result = response.data;

      // Update stored tokens
      const credentials = await Keychain.getInternetCredentials('mk13_auth');
      if (credentials && credentials.password) {
        const authData = JSON.parse(credentials.password);
        authData.accessToken = result.accessToken;
        authData.expiresAt = result.expiresAt;
        
        if (result.sessionToken) {
          authData.sessionToken = result.sessionToken;
        }

        await Keychain.setInternetCredentials(
          'mk13_auth',
          'mk13_user',
          JSON.stringify(authData)
        );
      }

      return result;
    } catch (error: any) {
      console.error('Token refresh failed:', error);
      throw new Error(error.message || 'Token refresh failed');
    }
  }

  async logout(sessionToken: string): Promise<void> {
    try {
      // Logout from backend
      await apiService.post('/auth/logout', {}, {
        headers: {
          'X-Session-Token': sessionToken,
        },
      });
    } catch (error) {
      console.warn('Backend logout failed:', error);
    } finally {
      // Always clear local storage
      await this.clearStoredCredentials();
      await GoogleSignin.signOut();
    }
  }

  async validateSession(sessionToken: string): Promise<{ user: any }> {
    try {
      const response = await apiService.get('/auth/me', {
        headers: {
          'X-Session-Token': sessionToken,
        },
      });

      return response.data;
    } catch (error: any) {
      console.error('Session validation failed:', error);
      throw new Error(error.message || 'Session validation failed');
    }
  }

  async enableBiometricAuth(authResult: AuthResult): Promise<void> {
    try {
      const { available } = await rnBiometrics.isSensorAvailable();
      
      if (!available) {
        throw new Error('Biometric authentication not available');
      }

      // Store auth data securely
      await this.storeTokensSecurely(authResult);

      console.log('Biometric authentication enabled');
    } catch (error: any) {
      console.error('Failed to enable biometric auth:', error);
      throw error;
    }
  }

  async disableBiometricAuth(): Promise<void> {
    try {
      await this.clearStoredCredentials();
      console.log('Biometric authentication disabled');
    } catch (error: any) {
      console.error('Failed to disable biometric auth:', error);
      throw error;
    }
  }

  async isBiometricAvailable(): Promise<{ available: boolean; biometryType?: string }> {
    try {
      const result = await rnBiometrics.isSensorAvailable();
      return result;
    } catch (error) {
      return { available: false };
    }
  }

  async hasStoredCredentials(): Promise<boolean> {
    try {
      const credentials = await Keychain.getInternetCredentials('mk13_auth');
      return !!(credentials && credentials.password);
    } catch (error) {
      return false;
    }
  }

  private async storeTokensSecurely(authResult: AuthResult): Promise<void> {
    try {
      const authData = {
        accessToken: authResult.accessToken,
        refreshToken: authResult.refreshToken,
        sessionToken: authResult.sessionToken,
        expiresAt: authResult.expiresAt,
        user: authResult.user,
      };

      await Keychain.setInternetCredentials(
        'mk13_auth',
        'mk13_user',
        JSON.stringify(authData),
        {
          accessControl: Keychain.ACCESS_CONTROL.BIOMETRY_ANY,
          authenticationType: Keychain.AUTHENTICATION_TYPE.DEVICE_PASSCODE_OR_SET_BIOMETRY,
        }
      );
    } catch (error: any) {
      console.error('Failed to store tokens securely:', error);
      throw error;
    }
  }

  private async clearStoredCredentials(): Promise<void> {
    try {
      await Keychain.resetInternetCredentials('mk13_auth');
    } catch (error) {
      console.warn('Failed to clear stored credentials:', error);
    }
  }
}

export const authService = new AuthService();
