/**
 * Redux Store Configuration for MK13 Mobile App
 * Centralized state management with persistence and middleware
 */

import { configureStore, combineReducers } from '@reduxjs/toolkit';
import { persistStore, persistReducer } from 'redux-persist';
import { MMKV } from 'react-native-mmkv';
import { Storage } from 'redux-persist';

// Import slices
import authSlice from './slices/authSlice';
import deviceSlice from './slices/deviceSlice';
import contextSlice from './slices/contextSlice';
import chatSlice from './slices/chatSlice';
import voiceSlice from './slices/voiceSlice';
import notificationSlice from './slices/notificationSlice';
import settingsSlice from './slices/settingsSlice';
import workflowSlice from './slices/workflowSlice';
import syncSlice from './slices/syncSlice';

// MMKV Storage for better performance
const storage = new MMKV();

const mmkvStorage: Storage = {
  setItem: (key, value) => {
    storage.set(key, value);
    return Promise.resolve(true);
  },
  getItem: (key) => {
    const value = storage.getString(key);
    return Promise.resolve(value);
  },
  removeItem: (key) => {
    storage.delete(key);
    return Promise.resolve();
  },
};

// Persist configuration
const persistConfig = {
  key: 'root',
  storage: mmkvStorage,
  whitelist: ['auth', 'settings', 'context'], // Only persist these slices
  blacklist: ['voice', 'notification'], // Don't persist these slices
};

// Root reducer
const rootReducer = combineReducers({
  auth: authSlice,
  device: deviceSlice,
  context: contextSlice,
  chat: chatSlice,
  voice: voiceSlice,
  notifications: notificationSlice,
  settings: settingsSlice,
  workflows: workflowSlice,
  sync: syncSlice,
});

// Persisted reducer
const persistedReducer = persistReducer(persistConfig, rootReducer);

// Configure store
export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
        ignoredPaths: ['register'],
      },
    }),
  devTools: __DEV__,
});

// Persistor
export const persistor = persistStore(store);

// Types
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Clear storage function (for logout)
export const clearStorage = () => {
  storage.clearAll();
  persistor.purge();
};
