# Docker Compose for MK13 AI Assistant Development
# Complete development environment with all services

version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: mk13-postgres
    environment:
      POSTGRES_DB: mk13
      POSTGRES_USER: mk13
      POSTGRES_PASSWORD: mk13_dev_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U mk13 -d mk13"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - mk13-network

  # Redis Cache & Message Broker
  redis:
    image: redis:7-alpine
    container_name: mk13-redis
    command: redis-server --appendonly yes --requirepass mk13_redis_password
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - mk13-network

  # Backend API
  backend:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: mk13-backend
    environment:
      MK13_ENVIRONMENT: development
      MK13_DEBUG: "true"
      MK13_DATABASE_URL: *************************************************/mk13
      MK13_REDIS_URL: redis://:mk13_redis_password@redis:6379/0
      MK13_CELERY_BROKER_URL: redis://:mk13_redis_password@redis:6379/1
      MK13_CELERY_RESULT_BACKEND: redis://:mk13_redis_password@redis:6379/2
      MK13_LOG_LEVEL: DEBUG
      MK13_CORS_ORIGINS: '["http://localhost:3000", "http://localhost:80"]'
    volumes:
      - ./src:/app/src
      - ./logs:/app/logs
      - ./storage:/app/storage
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - mk13-network

  # Celery Worker
  worker:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: mk13-worker
    command: mk13-worker
    environment:
      MK13_ENVIRONMENT: development
      MK13_DEBUG: "true"
      MK13_DATABASE_URL: *************************************************/mk13
      MK13_REDIS_URL: redis://:mk13_redis_password@redis:6379/0
      MK13_CELERY_BROKER_URL: redis://:mk13_redis_password@redis:6379/1
      MK13_CELERY_RESULT_BACKEND: redis://:mk13_redis_password@redis:6379/2
      MK13_LOG_LEVEL: DEBUG
    volumes:
      - ./src:/app/src
      - ./logs:/app/logs
      - ./storage:/app/storage
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - mk13-network

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: production
    container_name: mk13-frontend
    environment:
      REACT_APP_API_URL: http://localhost:8000
      REACT_APP_WS_URL: ws://localhost:8000
    ports:
      - "80:80"
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - mk13-network

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: mk13-prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - mk13-network

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: mk13-grafana
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin
      GF_USERS_ALLOW_SIGN_UP: "false"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    ports:
      - "3001:3000"
    depends_on:
      - prometheus
    networks:
      - mk13-network

  # Nginx Load Balancer (for production simulation)
  nginx:
    image: nginx:alpine
    container_name: mk13-nginx
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
    ports:
      - "443:443"
      - "8080:80"
    depends_on:
      - frontend
      - backend
    networks:
      - mk13-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  mk13-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
