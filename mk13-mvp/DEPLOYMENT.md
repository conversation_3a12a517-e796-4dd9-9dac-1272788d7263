# MK13 MVP Deployment Guide

This guide covers deploying the MK13 MVP application to production environments.

## 🎯 Deployment Options

### 1. Cloud Deployment (Recommended)
- **Backend**: Railway, Render, or DigitalOcean App Platform
- **Frontend**: Electron app distributed via GitHub Releases
- **Database**: Supabase (managed PostgreSQL)
- **Cache/Queue**: Redis Cloud or Upstash
- **File Storage**: Supabase Storage or AWS S3

### 2. Self-Hosted Deployment
- **Server**: Ubuntu 20.04+ or CentOS 8+
- **Reverse Proxy**: Nginx
- **Process Manager**: PM2 or systemd
- **Database**: Self-hosted PostgreSQL
- **Cache/Queue**: Self-hosted Redis

## 🚀 Cloud Deployment (Railway)

### Prerequisites
- Railway account
- GitHub repository
- Domain name (optional)

### Backend Deployment

#### 1. Prepare the Backend
```bash
# Create Procfile for Railway
echo "web: uvicorn main:app --host 0.0.0.0 --port \$PORT" > backend/Procfile

# Create railway.json
cat > backend/railway.json << EOF
{
  "build": {
    "builder": "NIXPACKS"
  },
  "deploy": {
    "startCommand": "uvicorn main:app --host 0.0.0.0 --port \$PORT --workers 4",
    "healthcheckPath": "/health",
    "healthcheckTimeout": 100,
    "restartPolicyType": "ON_FAILURE",
    "restartPolicyMaxRetries": 10
  }
}
EOF
```

#### 2. Deploy to Railway
```bash
# Install Railway CLI
npm install -g @railway/cli

# Login to Railway
railway login

# Create new project
railway new

# Deploy backend
cd backend
railway up

# Set environment variables
railway variables set OPENAI_API_KEY=your_key
railway variables set ANTHROPIC_API_KEY=your_key
railway variables set GOOGLE_API_KEY=your_key
railway variables set GOOGLE_CLIENT_ID=your_client_id
railway variables set GOOGLE_CLIENT_SECRET=your_secret
railway variables set SUPABASE_URL=your_supabase_url
railway variables set SUPABASE_KEY=your_supabase_key
railway variables set JWT_SECRET_KEY=your_jwt_secret
railway variables set ENCRYPTION_KEY=your_encryption_key
railway variables set REDIS_URL=your_redis_url
railway variables set ALLOWED_ORIGINS=https://yourdomain.com
```

#### 3. Set up Redis
```bash
# Add Redis service to Railway
railway add redis

# Get Redis URL
railway variables
```

#### 4. Configure Domain (Optional)
```bash
# Add custom domain
railway domain add yourdomain.com
```

### Frontend Distribution

#### 1. Build for Production
```bash
cd frontend

# Update environment variables for production
cat > .env.production << EOF
REACT_APP_API_URL=https://your-backend-url.railway.app
REACT_APP_WS_URL=wss://your-backend-url.railway.app
REACT_APP_GOOGLE_CLIENT_ID=your_google_client_id
EOF

# Build the application
npm run build
npm run electron:pack
```

#### 2. Create GitHub Release
```bash
# Create release with built artifacts
gh release create v1.0.0 \
  --title "MK13 MVP v1.0.0" \
  --notes "Initial release of MK13 MVP" \
  dist/MK13-MVP-*.exe \
  dist/MK13-MVP-*.dmg \
  dist/MK13-MVP-*.AppImage
```

## 🖥️ Self-Hosted Deployment

### Server Setup (Ubuntu 20.04)

#### 1. Initial Server Configuration
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install required packages
sudo apt install -y nginx postgresql postgresql-contrib redis-server \
  python3 python3-pip python3-venv nodejs npm git certbot \
  python3-certbot-nginx

# Create application user
sudo useradd -m -s /bin/bash mk13
sudo usermod -aG sudo mk13
```

#### 2. Database Setup
```bash
# Configure PostgreSQL
sudo -u postgres createuser --interactive mk13
sudo -u postgres createdb mk13_production

# Set password for database user
sudo -u postgres psql
ALTER USER mk13 PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE mk13_production TO mk13;
\q
```

#### 3. Redis Configuration
```bash
# Configure Redis
sudo systemctl enable redis-server
sudo systemctl start redis-server

# Secure Redis (optional)
sudo nano /etc/redis/redis.conf
# Uncomment and set: requirepass your_redis_password
sudo systemctl restart redis-server
```

### Application Deployment

#### 1. Deploy Backend
```bash
# Switch to application user
sudo su - mk13

# Clone repository
git clone <your-repo-url> mk13-mvp
cd mk13-mvp/backend

# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Create production environment file
cat > .env << EOF
OPENAI_API_KEY=your_key
ANTHROPIC_API_KEY=your_key
GOOGLE_API_KEY=your_key
GOOGLE_CLIENT_ID=your_client_id
GOOGLE_CLIENT_SECRET=your_secret
DATABASE_URL=postgresql://mk13:secure_password@localhost/mk13_production
REDIS_URL=redis://localhost:6379/0
JWT_SECRET_KEY=your_jwt_secret
ENCRYPTION_KEY=your_encryption_key
ALLOWED_ORIGINS=https://yourdomain.com
EOF

# Test the application
python main.py
```

#### 2. Configure Process Manager (PM2)
```bash
# Install PM2
npm install -g pm2

# Create PM2 ecosystem file
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [
    {
      name: 'mk13-backend',
      script: 'venv/bin/uvicorn',
      args: 'main:app --host 0.0.0.0 --port 8000 --workers 4',
      cwd: '/home/<USER>/mk13-mvp/backend',
      env: {
        NODE_ENV: 'production'
      },
      error_file: '/var/log/mk13/backend-error.log',
      out_file: '/var/log/mk13/backend-out.log',
      log_file: '/var/log/mk13/backend.log',
      time: true
    },
    {
      name: 'mk13-celery',
      script: 'venv/bin/celery',
      args: '-A celery_app worker --loglevel=info',
      cwd: '/home/<USER>/mk13-mvp/backend',
      error_file: '/var/log/mk13/celery-error.log',
      out_file: '/var/log/mk13/celery-out.log',
      log_file: '/var/log/mk13/celery.log',
      time: true
    }
  ]
};
EOF

# Create log directory
sudo mkdir -p /var/log/mk13
sudo chown mk13:mk13 /var/log/mk13

# Start applications
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

#### 3. Configure Nginx
```bash
# Create Nginx configuration
sudo nano /etc/nginx/sites-available/mk13

# Add configuration:
server {
    listen 80;
    server_name yourdomain.com;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /ws {
        proxy_pass http://127.0.0.1:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

# Enable site
sudo ln -s /etc/nginx/sites-available/mk13 /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

#### 4. SSL Certificate
```bash
# Obtain SSL certificate
sudo certbot --nginx -d yourdomain.com

# Test auto-renewal
sudo certbot renew --dry-run
```

## 🔧 Production Configuration

### Environment Variables Checklist

#### Backend
- [ ] `OPENAI_API_KEY` - OpenAI API key
- [ ] `ANTHROPIC_API_KEY` - Anthropic API key
- [ ] `GOOGLE_API_KEY` - Google Cloud API key
- [ ] `GOOGLE_CLIENT_ID` - Google OAuth2 client ID
- [ ] `GOOGLE_CLIENT_SECRET` - Google OAuth2 client secret
- [ ] `DATABASE_URL` - PostgreSQL connection string
- [ ] `REDIS_URL` - Redis connection string
- [ ] `JWT_SECRET_KEY` - JWT signing secret
- [ ] `ENCRYPTION_KEY` - Data encryption key
- [ ] `ALLOWED_ORIGINS` - CORS allowed origins

#### Frontend
- [ ] `REACT_APP_API_URL` - Backend API URL
- [ ] `REACT_APP_WS_URL` - WebSocket URL
- [ ] `REACT_APP_GOOGLE_CLIENT_ID` - Google OAuth2 client ID

### Security Hardening

#### 1. Firewall Configuration
```bash
# Configure UFW
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw enable
```

#### 2. Fail2Ban Setup
```bash
# Install Fail2Ban
sudo apt install fail2ban

# Configure Fail2Ban for Nginx
sudo nano /etc/fail2ban/jail.local

[nginx-http-auth]
enabled = true
port = http,https
logpath = /var/log/nginx/error.log

[nginx-limit-req]
enabled = true
port = http,https
logpath = /var/log/nginx/error.log
maxretry = 10

sudo systemctl enable fail2ban
sudo systemctl start fail2ban
```

#### 3. Regular Updates
```bash
# Create update script
cat > /home/<USER>/update.sh << EOF
#!/bin/bash
cd /home/<USER>/mk13-mvp
git pull origin main
cd backend
source venv/bin/activate
pip install -r requirements.txt
pm2 restart all
EOF

chmod +x /home/<USER>/update.sh

# Schedule regular updates (optional)
crontab -e
# Add: 0 2 * * 0 /home/<USER>/update.sh >> /var/log/mk13/update.log 2>&1
```

## 📊 Monitoring and Logging

### 1. Application Monitoring
```bash
# Monitor PM2 processes
pm2 monit

# View logs
pm2 logs mk13-backend
pm2 logs mk13-celery

# Check system resources
htop
df -h
free -h
```

### 2. Log Rotation
```bash
# Configure logrotate
sudo nano /etc/logrotate.d/mk13

/var/log/mk13/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 mk13 mk13
    postrotate
        pm2 reloadLogs
    endscript
}
```

### 3. Health Checks
```bash
# Create health check script
cat > /home/<USER>/health_check.sh << EOF
#!/bin/bash
curl -f http://localhost:8000/health || exit 1
redis-cli ping || exit 1
pg_isready -h localhost -p 5432 || exit 1
EOF

chmod +x /home/<USER>/health_check.sh

# Schedule health checks
crontab -e
# Add: */5 * * * * /home/<USER>/health_check.sh || echo "Health check failed" | mail -s "MK13 Health Alert" <EMAIL>
```

## 🔄 Backup and Recovery

### 1. Database Backup
```bash
# Create backup script
cat > /home/<USER>/backup.sh << EOF
#!/bin/bash
BACKUP_DIR="/home/<USER>/backups"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# Database backup
pg_dump mk13_production > $BACKUP_DIR/db_backup_$DATE.sql

# Redis backup
cp /var/lib/redis/dump.rdb $BACKUP_DIR/redis_backup_$DATE.rdb

# Application files backup
tar -czf $BACKUP_DIR/app_backup_$DATE.tar.gz /home/<USER>/mk13-mvp

# Clean old backups (keep 30 days)
find $BACKUP_DIR -name "*.sql" -mtime +30 -delete
find $BACKUP_DIR -name "*.rdb" -mtime +30 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
EOF

chmod +x /home/<USER>/backup.sh

# Schedule daily backups
crontab -e
# Add: 0 3 * * * /home/<USER>/backup.sh
```

### 2. Recovery Procedures
```bash
# Database recovery
psql mk13_production < /home/<USER>/backups/db_backup_YYYYMMDD_HHMMSS.sql

# Redis recovery
sudo systemctl stop redis-server
sudo cp /home/<USER>/backups/redis_backup_YYYYMMDD_HHMMSS.rdb /var/lib/redis/dump.rdb
sudo chown redis:redis /var/lib/redis/dump.rdb
sudo systemctl start redis-server

# Application recovery
cd /home/<USER>
tar -xzf backups/app_backup_YYYYMMDD_HHMMSS.tar.gz
pm2 restart all
```

## 🚨 Troubleshooting

### Common Issues

#### Backend not starting
```bash
# Check logs
pm2 logs mk13-backend

# Check environment variables
cat /home/<USER>/mk13-mvp/backend/.env

# Test database connection
psql mk13_production -c "SELECT 1;"

# Test Redis connection
redis-cli ping
```

#### High memory usage
```bash
# Check memory usage
free -h
ps aux --sort=-%mem | head

# Restart services if needed
pm2 restart all
sudo systemctl restart redis-server
```

#### SSL certificate issues
```bash
# Check certificate status
sudo certbot certificates

# Renew certificate
sudo certbot renew

# Test Nginx configuration
sudo nginx -t
```

This deployment guide provides comprehensive instructions for both cloud and self-hosted deployments, ensuring your MK13 MVP application runs reliably in production.
