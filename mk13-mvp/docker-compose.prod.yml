# Docker Compose for MK13 AI Assistant Production
# Production-ready configuration with security and performance optimizations

version: '3.8'

services:
  # PostgreSQL Database with replication
  postgres:
    image: postgres:15-alpine
    container_name: mk13-postgres-prod
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-mk13}
      POSTGRES_USER: ${POSTGRES_USER:-mk13}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    command: >
      postgres
      -c shared_preload_libraries=pg_stat_statements
      -c pg_stat_statements.track=all
      -c max_connections=200
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100
      -c random_page_cost=1.1
      -c effective_io_concurrency=200
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-mk13} -d ${POSTGRES_DB:-mk13}"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - mk13-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Redis Cluster for high availability
  redis:
    image: redis:7-alpine
    container_name: mk13-redis-prod
    command: >
      redis-server
      --appendonly yes
      --requirepass ${REDIS_PASSWORD}
      --maxmemory 512mb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - mk13-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Backend API with multiple replicas
  backend:
    image: mk13/backend:${VERSION:-latest}
    deploy:
      replicas: 3
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    environment:
      MK13_ENVIRONMENT: production
      MK13_DEBUG: "false"
      MK13_DATABASE_URL: postgresql://${POSTGRES_USER:-mk13}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB:-mk13}
      MK13_REDIS_URL: redis://:${REDIS_PASSWORD}@redis:6379/0
      MK13_CELERY_BROKER_URL: redis://:${REDIS_PASSWORD}@redis:6379/1
      MK13_CELERY_RESULT_BACKEND: redis://:${REDIS_PASSWORD}@redis:6379/2
      MK13_SECRET_KEY: ${SECRET_KEY}
      MK13_LOG_LEVEL: INFO
      MK13_SENTRY_DSN: ${SENTRY_DSN}
      MK13_CORS_ORIGINS: ${CORS_ORIGINS}
      MK13_OPENAI_API_KEY: ${OPENAI_API_KEY}
      MK13_ANTHROPIC_API_KEY: ${ANTHROPIC_API_KEY}
      MK13_GOOGLE_AI_API_KEY: ${GOOGLE_AI_API_KEY}
    volumes:
      - ./logs:/app/logs
      - ./storage:/app/storage
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - mk13-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Celery Workers with auto-scaling
  worker:
    image: mk13/backend:${VERSION:-latest}
    command: mk13-worker
    deploy:
      replicas: 2
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    environment:
      MK13_ENVIRONMENT: production
      MK13_DEBUG: "false"
      MK13_DATABASE_URL: postgresql://${POSTGRES_USER:-mk13}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB:-mk13}
      MK13_REDIS_URL: redis://:${REDIS_PASSWORD}@redis:6379/0
      MK13_CELERY_BROKER_URL: redis://:${REDIS_PASSWORD}@redis:6379/1
      MK13_CELERY_RESULT_BACKEND: redis://:${REDIS_PASSWORD}@redis:6379/2
      MK13_SECRET_KEY: ${SECRET_KEY}
      MK13_LOG_LEVEL: INFO
      MK13_SENTRY_DSN: ${SENTRY_DSN}
      MK13_OPENAI_API_KEY: ${OPENAI_API_KEY}
      MK13_ANTHROPIC_API_KEY: ${ANTHROPIC_API_KEY}
      MK13_GOOGLE_AI_API_KEY: ${GOOGLE_AI_API_KEY}
    volumes:
      - ./logs:/app/logs
      - ./storage:/app/storage
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - mk13-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Frontend with CDN optimization
  frontend:
    image: mk13/frontend:${VERSION:-latest}
    deploy:
      replicas: 2
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.1'
          memory: 128M
    environment:
      REACT_APP_API_URL: ${API_URL}
      REACT_APP_WS_URL: ${WS_URL}
      REACT_APP_SENTRY_DSN: ${FRONTEND_SENTRY_DSN}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - mk13-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Nginx Load Balancer with SSL termination
  nginx:
    image: nginx:alpine
    container_name: mk13-nginx-prod
    volumes:
      - ./nginx/nginx.prod.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./ssl:/etc/nginx/ssl
      - ./logs/nginx:/var/log/nginx
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - frontend
      - backend
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - mk13-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: mk13-prometheus-prod
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=15d'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    volumes:
      - ./monitoring/prometheus.prod.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    restart: unless-stopped
    networks:
      - mk13-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: mk13-grafana-prod
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD}
      GF_USERS_ALLOW_SIGN_UP: "false"
      GF_SECURITY_DISABLE_GRAVATAR: "true"
      GF_ANALYTICS_REPORTING_ENABLED: "false"
      GF_ANALYTICS_CHECK_FOR_UPDATES: "false"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    restart: unless-stopped
    depends_on:
      - prometheus
    networks:
      - mk13-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  mk13-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# Production secrets should be managed externally
secrets:
  postgres_password:
    external: true
  redis_password:
    external: true
  secret_key:
    external: true
